package com.bcels.cies.infrastructure.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Random;

/**
 * 获取市场电价工具类
 */
@Component
public class MarketApiUtil {

    @Autowired
    private RestTemplateUtil restTemplateUtil;

    private static String APP_SECRET;
    private static String APP_ID;

    public static void init(String appId,String appSecret) {
        APP_ID = appId;
        APP_SECRET = appSecret;
    }

    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final String BASE_URL = "https://platform.eesaenergy.com";

    private static String generateSign(HeaderParams params) {
        String originSign = String.format(
                "appId=%s&appSecret=%s&nonce=%s&timestamp=%d",
                params.getAppId(),  APP_SECRET, params.getNonce(),  params.getTimestamp()
        );
        return DigestUtils.md5Hex(originSign).toUpperCase();
    }

    private static HeaderParams generateHeaderParams() {
        HeaderParams params = new HeaderParams();
        params.setAppId(APP_ID);
        params.setTimestamp(System.currentTimeMillis());
        params.setNonce(generateNonce());
        return params;
    }


    private static String generateNonce() {
        Random random = new Random();
        int length = 10 + random.nextInt(41);  // 10~50位随机长度
        StringBuilder nonce = new StringBuilder(length);
        nonce.setLength(0);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(CHAR_POOL.length());
            nonce.append(CHAR_POOL.charAt(index));
        }
        return nonce.toString();

    }
    static class HeaderParams {
        private String appId;
        private long timestamp;
        private String nonce;

        public String getAppId() { return appId; }
        public void setAppId(String appId) { this.appId  = appId; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp  = timestamp; }
        public String getNonce() { return nonce; }
        public void setNonce(String nonce) { this.nonce  = nonce; }
    }


    public <T> T doGet(String url, Map<String, Object> params, Class<T> responseType) {
        String completeUrl = BASE_URL + url;
        HeaderParams headerParams = generateHeaderParams();
        String sign = generateSign(headerParams);

        HttpHeaders headers = new HttpHeaders();
        headers.set("appId",  headerParams.getAppId());
        headers.set("timestamp",  String.valueOf(headerParams.getTimestamp()));
        headers.set("nonce",  headerParams.getNonce());
        headers.set("sign",  sign);
        return restTemplateUtil.get(completeUrl,  headers, params, responseType);
    }

    public <T> T doPost(String url, Map<String, Object> params, Class<T> responseType) {
        String completeUrl = BASE_URL + url;
        HeaderParams headerParams = generateHeaderParams();
        String sign = generateSign(headerParams);
        HttpHeaders headers = new HttpHeaders();
        headers.set("appId",  headerParams.getAppId());
        headers.set("timestamp",  String.valueOf(headerParams.getTimestamp()));
        headers.set("nonce",  headerParams.getNonce());
        headers.set("sign",  sign);
        return restTemplateUtil.postJson(completeUrl,  headers, params, responseType);
    }
}
