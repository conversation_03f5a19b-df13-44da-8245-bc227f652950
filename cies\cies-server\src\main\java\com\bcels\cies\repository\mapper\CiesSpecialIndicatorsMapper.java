package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.repository.entity.CiesSpecialIndicatorsEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.request.CiesSpecialIndicatorsListRequest;
import com.bcels.cies.response.CiesProjectInfoResponse;
import com.bcels.cies.response.CiesSpecialIndicatorsResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 特殊指标 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Mapper
public interface CiesSpecialIndicatorsMapper extends BaseMapper<CiesSpecialIndicatorsEntity> {


    IPage<CiesSpecialIndicatorsResponse> findSpecialIndicatorsForPage(@Param("page") Page<CiesSpecialIndicatorsResponse> page, @Param("request") CiesSpecialIndicatorsListRequest request);

    CiesSpecialIndicatorsResponse findSpecialIndicatorsById(String specialIndicatorsId);

}
