package com.bcels.cies.repository.mapper;

import com.bcels.cies.repository.entity.CiesMarketElecPriEntity;
import com.bcels.cies.repository.entity.CiesMarketStageEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 市场分时阶段 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Mapper
public interface CiesMarketStageMapper extends BaseMapper<CiesMarketStageEntity> {

    List<CiesMarketStageEntity> findStageInfoByYear(@Param("projectId") String projectId, @Param("year") Integer year);

}
