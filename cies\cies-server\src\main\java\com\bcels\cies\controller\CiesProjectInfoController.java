package com.bcels.cies.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bcels.cies.api.CiesProjectInfoApi;
import com.bcels.cies.domain.CiesProjectDomainService;
import com.bcels.cies.repository.entity.CiesProjectInfoEntity;
import com.bcels.cies.request.CiesConnectionPointRequest;
import com.bcels.cies.request.CiesEsPlanRuleRequest;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.response.CiesConnectionPointResponse;
import com.bcels.cies.response.CiesEsPlanRuleResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import com.bcels.cies.response.CiesStatisticsProjectResponse;
import com.zwy.common.utils.annotation.Dict;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/projectInfo/v1")
public class CiesProjectInfoController implements CiesProjectInfoApi {

    @Autowired
    private CiesProjectDomainService ciesProjectDomainService;
    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesProjectInfoResponse>> findForPage(@RequestBody CiesProjectInfoRequest request) {
        return ResultData.success(ciesProjectDomainService.getProjectInfoForPage(request));
    }

    @Override
    @PostMapping("findMarKetForPage")
    public ResultData<PageResponse<CiesProjectInfoResponse>> findMarKetForPage(@RequestBody CiesProjectInfoRequest request) {
        return ResultData.success(ciesProjectDomainService.findMarKetForPage(request));
    }


    @Override
    @GetMapping("findById")
    public ResultData<CiesProjectInfoResponse> getProjectInfById(@RequestParam("projectId") String projectId) {
        return ResultData.success(ciesProjectDomainService.getProjectInfById(projectId));
    }

    @Override
    @PostMapping("update")
    public ResultData<Void> updateProjectInfo(@RequestBody CiesProjectInfoRequest request){
        ciesProjectDomainService.updateProjectInfo(request);
        return ResultData.success();
    }

    @Override
    @GetMapping("findRuleByProjectId")
    public ResultData<CiesEsPlanRuleResponse> findEsPlanRuleByProjectId(@RequestParam("projectId") String projectId) {
        return ResultData.success(ciesProjectDomainService.findEsPlanRuleByProjectId(projectId));
    }


    @Override
    @PostMapping("updateConnPoint")
    public ResultData<Void> updateConnPoint(@RequestBody CiesConnectionPointRequest request){
        ciesProjectDomainService.updateConnPoint(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("updateRule")
    public ResultData<Void> updateRule(@RequestBody CiesEsPlanRuleRequest request){
        ciesProjectDomainService.updateRule(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("query")
    public ResultData<List<CiesProjectInfoResponse>> findProjectByEnterpriseId(@RequestBody CiesProjectInfoRequest request) {
        return ResultData.success(ciesProjectDomainService.findProjectByEnterpriseId(request));
    }

    @Override
    @PostMapping("queryEnergyProject")
    public ResultData<List<CiesProjectInfoResponse>> findEnergyProjectByEnterpriseId(@RequestBody CiesProjectInfoRequest request) {
        return ResultData.success(ciesProjectDomainService.findEnergyProjectByEnterpriseId(request));
    }

    @Override
    @GetMapping("findConnInfoById")
    public ResultData<CiesConnectionPointResponse> findConnInfoById(@RequestParam("connectionPointId") String connectionPointId) {
        return ResultData.success(ciesProjectDomainService.findConnInfoById(connectionPointId));
    }

    @Override
    @GetMapping("findArea")
    public ResultData<List<String>> findArea() {
        return ResultData.success(ciesProjectDomainService.findArea());
    }

    @Override
    @GetMapping("getElectricityType")
    public ResultData<JSONArray> getElectricityType(@RequestParam("regionName") String regionName) {
        return ResultData.success(ciesProjectDomainService.getElectricityType(regionName));
    }

    @GetMapping("statisticsProject")
    public ResultData<CiesStatisticsProjectResponse> statisticsProject() {
        return ResultData.success(ciesProjectDomainService.statisticsProject());
    }

    @PostMapping("/getProvinceProject")
    public ResultData<Map<String, Object>> getProvinceProject(@RequestBody CiesProjectInfoRequest request) {
        return ResultData.success(ciesProjectDomainService.getProvinceProject(request));
    }

    @PostMapping("/getPowerCurve")
    public ResultData<CiesProjectInfoResponse> getPowerCurve(@RequestBody CiesProjectInfoRequest request) {
        return ResultData.success(ciesProjectDomainService.getPowerCurve(request));
    }

    @PostMapping("/getSettlementStatement")
    @Dict(code = "")
    public ResultData<List<CiesProjectInfoResponse>> getSettlementStatement(@RequestBody CiesProjectInfoRequest request) {
        return ResultData.success(ciesProjectDomainService.getSettlementStatement(request));
    }
    @PostMapping("/getProjectInfo")
    public ResultData<List<CiesProjectInfoResponse>> getProjectInfo() {
        List<CiesProjectInfoResponse> projectInfo = ciesProjectDomainService.getProjectInfo();
        return ResultData.success(projectInfo);
    }

    @PostMapping("/verificationStatement")
    public ResultData<Void> verificationStatement(@RequestBody CiesProjectInfoRequest request) {
        ciesProjectDomainService.verificationStatement(request);
        return ResultData.success();
    }
}
