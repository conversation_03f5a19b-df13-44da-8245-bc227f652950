<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesPeakValleyPriceGapMapper">

    <select id="queryPriceGap" resultType="com.bcels.cies.response.CiesPeakValleyPriceGapResponse">
        select gap.*,val.total_amount as totalAmount from cies_peak_valley_price_gap gap
        left join cies_peak_valley_summary val on gap.ess_bill_id=val.ess_bill_id and
        gap.connection_point_id=val.connection_point_id
        and gap.stat_period = val.stat_period and val.dr = 0
        <where>
            <if test="request.connectionPointId  != null and request.connectionPointId  != ''">
                AND gap.connection_point_id=#{request.connectionPointId}
            </if>
            <if test="request.essBillId  != null and request.essBillId  != ''">
                AND gap.ess_bill_id=#{request.essBillId}
            </if>
            <if test="request.statPeriod  != null and request.statPeriod  != ''">
                AND gap.stat_period=#{request.statPeriod}
            </if>
        </where>
    </select>
</mapper>
