package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;


@Getter
@Setter
@TableName("cies_dict")
@ApiModel(value = "CiesDictEntity对象", description = "字典映射表")
public class CiesDictEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("字典Id")
    private Integer dictId;

    @ApiModelProperty("字典类型")
    private String dictType;

    @ApiModelProperty("字典编码")
    private String dictCode;

    @ApiModelProperty("字典描述")
    private String dictDesc;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
