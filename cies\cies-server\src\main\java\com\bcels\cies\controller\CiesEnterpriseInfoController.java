package com.bcels.cies.controller;

import com.bcels.cies.api.CiesEnterpriseInfoApi;
import com.bcels.cies.domain.CiesEnterpriseDomainService;
import com.bcels.cies.request.CiesEnterpriseInfoRequest;
import com.bcels.cies.response.CiesEnterpriseInfoResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import java.util.List;

@RestController
@RequestMapping("/ciesEnterprise/v1")
public class CiesEnterpriseInfoController implements CiesEnterpriseInfoApi {

    @Autowired
    private CiesEnterpriseDomainService ciesEnterpriseDomainService;
    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesEnterpriseInfoResponse>> findForPage(@RequestBody CiesEnterpriseInfoRequest request) {
        return ResultData.success(ciesEnterpriseDomainService.findEnterpriseForPage(request));
    }

    @Override
    @PostMapping("update")
    public ResultData<Void> updateEquipInfo(@RequestBody CiesEnterpriseInfoRequest request) {
        ciesEnterpriseDomainService.updateEnterpriseInfo(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("add")
    public ResultData<Void> addEquipInfo(@RequestBody CiesEnterpriseInfoRequest request) {
        ciesEnterpriseDomainService.addEnterpriseInfo(request);
        return ResultData.success();
    }

    @Override
    @GetMapping("queryByName")
    public ResultData<List<CiesEnterpriseInfoResponse>> findEnterprise(@RequestParam(value = "enterpriseName",required = false) String enterpriseName) {
        return ResultData.success(ciesEnterpriseDomainService.findEnterprise(enterpriseName));
    }

    @Override
    @GetMapping("queryEnterprise")
    public ResultData<List<CiesEnterpriseInfoResponse>> findEnergyEnterprise(@RequestParam(value = "enterpriseName",required = false) String enterpriseName) {
        return ResultData.success(ciesEnterpriseDomainService.findEnergyEnterprise(enterpriseName));
    }

    @Override
    @GetMapping("findById")
    public ResultData<CiesEnterpriseInfoResponse> findEnterpriseById(@RequestParam("enterpriseId") String enterpriseId) {
        return ResultData.success(ciesEnterpriseDomainService.findEnterpriseById(enterpriseId));
    }
}
