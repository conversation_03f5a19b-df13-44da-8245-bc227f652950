package com.bcels.cies.response;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@Schema(description = "生成结算单")
public class CiesSettlementBillGenerateResponse implements Serializable {

    @Schema(description = "结算单主键")
    private String essBillId;

    @Schema(description = "结算单标题")
    private String billTitle;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "甲方分成比例(%)")
    private BigDecimal partyARatio;

    @Schema(description = "甲方收益（元）")
    private BigDecimal partyAIncome;

    @Schema(description = "乙方分成比例(%)")
    private BigDecimal partyBRatio;

    @Schema(description = "乙方收益（元）")
    private BigDecimal partyBIncome;

    @Schema(description = "甲方分成金额(元)")
    private BigDecimal partyAAmount;

    @Schema(description = "乙方分成金额(元)")
    private BigDecimal partyBAmount;

    @Schema(description = "税差计算比例")
    private BigDecimal taxAdjustmentRate;

    @Schema(description = "税差金额(元)")
    private BigDecimal taxAdjustmentAmount;

    @ApiModelProperty("甲方企业名称")
    private String partyAName;

    @ApiModelProperty("乙方企业名称")
    private String partyBName;

    @Schema(description = "日期")
    private String generateDate;

    @Schema(description = "峰谷价差总收益（元）")
    private BigDecimal peakValleyRevenue;

    @Schema(description = "结算单模板")
    private String templateType;

    @Schema(description = "结算单明细")
    private List<CiesPeakValleyPriceGapResponse> billList;
}

