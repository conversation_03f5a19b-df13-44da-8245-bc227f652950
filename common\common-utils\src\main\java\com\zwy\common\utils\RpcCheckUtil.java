package com.zwy.common.utils;

import com.zwy.common.utils.bean.ResultData;
import com.zwy.common.utils.exception.CommonException;
import org.apache.commons.lang3.*;

public class RpcCheckUtil {

    public static <T> void check(ResultData<T> resultData) {
        check(resultData, null);
    }

    public static <T> void check(ResultData<T> resultData, String errorMessage) {
        if(resultData == null) {
            throw new CommonException("系统异常");
        }
        if(!resultData.hasSuccessed()) {
            if(StringUtils.isBlank(errorMessage)) {
                throw new CommonException(resultData.getMessage());
            }
            throw new CommonException(errorMessage + ":" + resultData.getMessage());
        }
    }

    public static <T> T checkAndGet(ResultData<T> resultData, String errorMessage) {
        if(!resultData.hasSuccessed() || resultData.getData() == null) {
            if(StringUtils.isBlank(errorMessage)) {
                throw new CommonException(resultData.getMessage());
            }
            throw new CommonException(errorMessage + ":" + resultData.getMessage());
        }
        return resultData.getData();
    }

    public static <T> T checkAndGet(ResultData<T> resultData) {
        return checkAndGet(resultData, null);
    }

    public static <T> T checkOrElseNull(ResultData<T> resultData, String errorMessage) {
        check(resultData, errorMessage);
        return resultData.getData();
    }

    public static <T> T checkOrElseNull(ResultData<T> resultData) {
        return checkOrElseNull(resultData, null);
    }

}
