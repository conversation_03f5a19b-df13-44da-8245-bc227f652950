package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "深谷信息")
public class CiesValleyConfigDetailsResponse implements Serializable {
    
    @Schema(description = "配置明细主键")
    private String configDetailId;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "深谷数据")
    private String deepData;

    @Schema(description = "配置主键")
    private String configId;

    @Schema(description = "并网点Id")
    private String connectionPointId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;
}

