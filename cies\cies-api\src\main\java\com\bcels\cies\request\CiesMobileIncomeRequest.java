package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Schema(description = "小程序-收益查询参数")
public class CiesMobileIncomeRequest  implements Serializable {

    @Schema(description = "项目集合")
    private List<String> projectIds;

    @Schema(description = "月份")
    private String month;
}
