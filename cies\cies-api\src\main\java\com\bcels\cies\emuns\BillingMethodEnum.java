package com.bcels.cies.emuns;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 开具方式
 */
@Getter
@AllArgsConstructor
public enum BillingMethodEnum {
    OFFLINE_ISSUE("OFFLINE_ISSUE", "线下开具"),
    PLATFORM_ISSUE("PLATFORM_ISSUE", "平台开具"),
    ;
    private final String code;
    private final String desc;

    private static final Map<String, String> CODE_TO_DESC = Arrays.stream(values())
            .collect(Collectors.toMap(BillingMethodEnum::getCode,  BillingMethodEnum::getDesc));

    private static final Map<String, String> DESC_TO_CODE = Arrays.stream(values())
            .collect(Collectors.toMap(
                    BillingMethodEnum::getDesc,
                    BillingMethodEnum::getCode,
                    (oldVal, newVal) -> oldVal
            ));

    // 根据code获取desc
    public static String getDescByCode(String code) {
        return CODE_TO_DESC.getOrDefault(code,  "未知类型");
    }
}
