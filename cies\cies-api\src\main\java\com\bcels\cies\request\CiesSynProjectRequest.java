package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "同步数据中台项目信息")
public class CiesSynProjectRequest implements Serializable {

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "租户编码")
    private String tenantCode;

    @Schema(description = "时间维度")
    private String timeDimension;

    @Schema(description = "同步类型 add:新增;update:编辑")
    private String sycType;

}
