package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "特殊指标")
public class CiesSpecialIndicatorsResponse implements Serializable {

    @Schema(description = "特殊指标关联主键")
    private String specialIndicatorsId;

    @Schema(description = "业务指标名称")
    private String indicatorName;

    @Schema(description = "企业")
    private String enterpriseName;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "关联数据类型")
    private String relateDataType;

    @Schema(description = "数据名称")
    private String dataName;

    @Schema(description = "最新数据")
    private BigDecimal currentData;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "通道ID")
    private String cId;

    @Schema(description = "设备名称")
    private String equipName;

    @Schema(description = "设备ID")
    private String equipId;

    @Schema(description = "关联指标名称")
    private String relateIndicatorName;

    @Schema(description = "关联指标ID")
    private String relateIndicatorId;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;
}
