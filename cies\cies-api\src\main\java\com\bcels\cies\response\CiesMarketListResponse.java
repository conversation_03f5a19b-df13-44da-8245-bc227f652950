package com.bcels.cies.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.cloud.client.ConditionalOnDiscoveryHealthIndicatorEnabled;

import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "用户代购电")
public class CiesMarketListResponse {

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "地区")
    private String elecArea;

    @Schema(description = "用电类型")
    private String elecType;

    @Schema(description = "电压等级")
    private String voltageLevel;

    @Schema(description = "尖峰电量电价")
    private BigDecimal peakEnergyPrice;

    @Schema(description = "尖峰时段")
    private String peakPeriod;

    @Schema(description = "高峰电量电价")
    private BigDecimal highEnergyPrice;

    @Schema(description = "高峰时段")
    private String highPeriod;

    @Schema(description = "平时电量电价")
    private BigDecimal normalEnergyPrice;

    @Schema(description = "平时段")
    private String normalPeriod;

    @Schema(description = "低谷电量电价")
    private BigDecimal offPeakEnergyPrice;

    @Schema(description = "低谷时段")
    private String offPeakPeriod;

    @Schema(description = "深谷电量电价")
    private BigDecimal valleyEnergyPrice;

    @Schema(description = "深谷时段")
    private String valleyPeriod;

    @Schema(description = "需量电价(元/千瓦·月)")
    private BigDecimal demandPrice;

    @Schema(description = "容量电价(元/千伏安·月)")
    private BigDecimal capacityPrice;
}
