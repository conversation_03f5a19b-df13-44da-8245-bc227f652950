package com.bcels.cies.repository.mapper;

import com.bcels.cies.repository.entity.CiesEnterpriseInfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.response.CiesEnterpriseInfoResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 企业信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Mapper
public interface CiesEnterpriseInfoMapper extends BaseMapper<CiesEnterpriseInfoEntity> {


    List<CiesEnterpriseInfoResponse> findEnergyEnterprise(@Param("enterpriseName") String enterpriseName, @Param("projectType") String projectType);

}
