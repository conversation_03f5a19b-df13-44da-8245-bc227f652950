//package com.bcels.cies.infrastructure.filter;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.bcels.cies.infrastructure.config.CiesUserContext;
//import com.bcels.cies.infrastructure.utils.RedisUtils;
//import com.bcels.cies.infrastructure.utils.RestTemplateUtil;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpHeaders;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//import org.springframework.web.servlet.HandlerInterceptor;
//import org.springframework.web.util.UriComponentsBuilder;
//
//import java.nio.charset.StandardCharsets;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
//@Component
//@Slf4j
//public class AuthInterceptor implements HandlerInterceptor {
//
//    @Value("${app.auth.app-code}")
//    private String appCode;
//
//    @Value("${app.auth.user-info-url}")
//    private String userInfoUrl;
//
//    @Value("${app.auth.permission-url}")
//    private String permissionUrl;
//
//    private static final String SUCCESS_CODE = "200";
//
//    @Autowired
//    private RestTemplateUtil restTemplateUtil;
//
//    @Autowired
//    private RedisUtils redisUtils;
//
//    @Override
//    public boolean preHandle(HttpServletRequest request,
//                             HttpServletResponse response,
//                             Object handler) {
//
//        // 获取并校验Token
//        String token = extractToken(request);
//        String jwtToken = request.getHeader("X-ZWY-TOKEN");
//
//        String tenantCode = request.getHeader("tenantCode");
//        String jsonStr = JSONObject.toJSONString(request.getHeader("X-SSO-USER"));
//        JSONObject jsonObj = JSONObject.parseObject(jsonStr);
//        String userCode = jsonObj.getString("userCode");
//        // 网关添加的当前登录人
//        String userAccount = jsonObj.getString("userAccount");
//        log.info("获取用户权限tenantCode：{}，userCode：{}", tenantCode, userCode);
//
//
//        // 检查缓存
//        String cacheKey = "user:token:" + token;
//        String loginName = (String) redisUtils.get(cacheKey);
//
//        String cacheKey1 = "user:perms:" + token;
//        List<String> list = (List<String>) redisUtils.get(cacheKey1);
//
//        // 缓存未命中时调用第三方接口
//        if (loginName == null) {
//            if (org.apache.commons.lang3.StringUtils.isEmpty(userAccount)){
//                loginName = fetchUserInfo(token, jwtToken);
//            }else{
//                loginName = userAccount;
//            }
//            redisUtils.set(
//                    "user:token:" + token,
//                    loginName,
//                    30, TimeUnit.MINUTES
//            );
//        }
//
//        if (CollectionUtils.isEmpty(list)) {
//            List<String> permissions = fetchPermissions(tenantCode,userCode);
//            redisUtils.set(
//                    "user:perms:" + token,
//                    permissions,
//                    24, TimeUnit.HOURS
//            );
//        }
//
//        // 将用户信息存入请求上下文
//        CiesUserContext.setCurrentUser(loginName);
//        return true;
//    }
//
//    private String extractToken(HttpServletRequest request) {
//        String header = request.getHeader("Authorization");
//        if (StringUtils.hasText(header) && header.startsWith("Bearer ")) {
//            return header.substring(7);
//        }
//        return null;
//    }
//
//    private String fetchUserInfo(String token, String jwtToken) {
//        HttpHeaders headers = new HttpHeaders();
//        headers.set("Authorization", "Bearer " + token);
//        headers.set("X-ZWY-TOKEN", jwtToken);
//
//        String url = userInfoUrl.replace("appCode", appCode);
//        try {
//            JSONObject jsonObject = restTemplateUtil.get(url, headers, JSONObject.class);
//            return jsonObject.getString("username");
//        }catch (Exception e){
//            // 不做处理,获取用户信息
//        }
//        return null;
//    }
//
//    private List<String> fetchPermissions(String tenantCode,String userCode) {
//        List<String> list = new ArrayList<>();
//        try {
//            String url = UriComponentsBuilder
//                    .fromHttpUrl(permissionUrl)
//                    .queryParam("userCode", userCode)
//                    .queryParam("tenantCode", tenantCode)
//                    .encode(StandardCharsets.UTF_8)
//                    .toUriString();
//            JSONObject jsonObject = restTemplateUtil.get(url, null, JSONObject.class);
//            if (SUCCESS_CODE.equals(jsonObject.getString("code")) && jsonObject.getJSONObject("data").containsKey("permissionsByProjectResponses")) {
//                JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("permissionsByProjectResponses");
//                if (jsonArray != null && jsonArray.size() > 0) {
//                    for (int i = 0; i < jsonArray.size(); i++) {
//                        String permission = jsonArray.getJSONObject(i).getString("projectId");
//                        list.add(permission);
//                    }
//                }
//            }
//            return list;
//        } catch (Exception e) {
//            // 不做处理，获取权限
//        }
//        return null;
//    }
//
//
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
//        // 必须清理，防止内存泄漏
//        CiesUserContext.clear();
//    }
//}