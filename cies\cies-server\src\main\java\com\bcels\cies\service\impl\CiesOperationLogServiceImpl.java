package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.repository.entity.CiesEnterpriseInfoEntity;
import com.bcels.cies.repository.entity.CiesOperationLogEntity;
import com.bcels.cies.repository.mapper.CiesOperationLogMapper;
import com.bcels.cies.request.CiesOperationLogRequest;
import com.bcels.cies.response.CiesEnterpriseInfoResponse;
import com.bcels.cies.response.CiesOperationLogResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import com.bcels.cies.service.ICiesOperationLogService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.ConvertUtils;
import com.zwy.common.utils.bean.PageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class CiesOperationLogServiceImpl extends ServiceImpl<CiesOperationLogMapper, CiesOperationLogEntity> implements ICiesOperationLogService {

    @Autowired
    private CiesOperationLogMapper ciesOperationLogMapper;


    @Override
    public PageResponse<CiesOperationLogResponse> findOperationLog(CiesOperationLogRequest request) {
        LambdaQueryWrapper<CiesOperationLogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CiesOperationLogEntity::getEssBillId, request.getEssBillId());

        IPage<CiesOperationLogEntity> pageResult = page(new Page<>(request.getPage(), request.getPageSize()), queryWrapper);
        List<CiesOperationLogResponse> projectInfoList = BeanCopyUtil.copyListProperties(pageResult.getRecords(), CiesOperationLogResponse::new);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), projectInfoList);
    }

    @Override
    public void saveOpeationLog(CiesOperationLogEntity ciesOperationLog) {
        ciesOperationLogMapper.insert(ciesOperationLog);
    }
}
