package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "设备信息")
public class CiesEquipInfoRequest extends PageRequest implements Serializable {

    @Schema(description = "设备主键")
    private String equipId;

    @Schema(description = "设备名称")
    private String equipName;

    @Schema(description = "等级")
    private Integer level;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "上级设备ID")
    private String parentEquipId;

    @Schema(description = "上级设备名称")
    private String parentEquipName;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目ID")
    private String projectId;
}
