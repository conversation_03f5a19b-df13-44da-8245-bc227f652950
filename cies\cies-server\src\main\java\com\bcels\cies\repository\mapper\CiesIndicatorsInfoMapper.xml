<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesIndicatorsInfoMapper">

    <select id="findProjectInfoForPage" resultType="com.bcels.cies.response.CiesIndicatorsInfoResponse">
        select ind.*,enter.enterprise_name,pro.pro_name,equip.equip_name as relatedEquipName
        from cies_indicators_info ind
         left join cies_equip_info equip on equip.equip_id = ind.equip_id
         left join cies_project_info pro on pro.project_id=ind.project_id
         left join cies_enterprise_info enter on enter.enterprise_id=pro.enterprise_id
        <where>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND pro.project_id  = #{request.projectId}
            </if>
            <if test="request.enterpriseName  != null and request.enterpriseName  != ''">
                AND enter.enterprise_name = #{request.enterpriseName}
            </if>
            <if test="request.relatedEquipName  != null and request.relatedEquipName  != ''">
                AND equip.equip_name LIKE CONCAT('%', #{request.relatedEquipName},  '%')
            </if>
            <if test="request.dataType  != null and request.dataType  != ''">
                AND ind.data_type LIKE CONCAT('%', #{request.dataType},  '%')
            </if>
            <if test="request.dataName  != null and request.dataName  != ''">
                AND ind.data_name LIKE CONCAT('%', #{request.dataName},  '%')
            </if>
            <if test="request.pageDisplayName  != null and request.pageDisplayName  != ''">
                AND ind.page_display_name LIKE CONCAT('%', #{request.pageDisplayName},  '%')
            </if>
                and ind.dr = 0
        </where>
    order by pro.pro_name asc,ind.create_time desc,equip.equip_name,ind.data_type
    </select>
    <select id="findIndicatorById" resultType="com.bcels.cies.response.CiesIndicatorsInfoResponse">
         select ind.*,enter.enterprise_name,pro.pro_name,equip.equip_name as relatedEquipName
             from cies_indicators_info ind
                    join cies_equip_info equip on equip.equip_id = ind.equip_id
                    join cies_project_info pro on pro.project_id=ind.project_id
                    join cies_enterprise_info enter on enter.enterprise_id=pro.enterprise_id
         where ind.indicator_id = #{indicatorById}
    </select>
</mapper>
