package com.bcels.cies.api;

import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "3、设备信息API")
@FeignClient(name = "equipInfo", path = "/equipInfo/v1")
public interface CiesEquipInfoApi {

    @Operation(summary = "设备列表")
    @PostMapping("page")
    ResultData<PageResponse<CiesEquipInfoResponse>> findForPage(@RequestBody CiesEquipInfoRequest request);


    @Operation(summary = "编辑设备信息")
    @PostMapping("update")
    ResultData<Void> updateEquipInfo(@RequestBody CiesEquipInfoRequest request);

    @Operation(summary = "查询单个设备信息")
    @GetMapping("findById")
    @Parameters({
            @Parameter(name = "equipId", description = "设备ID", required = true)
    })
    ResultData<CiesEquipInfoResponse> findEquipInfoById(@RequestParam("equipId") String equipId);

    @Operation(summary = "查询父级设备列表")
    @PostMapping("findParentEquip")
    ResultData<List<CiesEquipInfoResponse>> findParentEquip(@RequestBody CiesEquipInfoRequest request);
}
