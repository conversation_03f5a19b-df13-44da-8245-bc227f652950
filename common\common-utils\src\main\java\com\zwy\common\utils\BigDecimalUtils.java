package com.zwy.common.utils;

import com.zwy.common.utils.exception.CommonException;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.*;
import java.util.stream.Collectors;


public class BigDecimalUtils {

    public static final BigDecimal ZERO = new BigDecimal(0);

    public static final BigDecimal ONE = new BigDecimal(1);
    public static final BigDecimal TWO = new BigDecimal(2);

    public static final BigDecimal THREE = new BigDecimal(3);

    public static final BigDecimal HUNDRED = new BigDecimal(100);

    public static final BigDecimal THOUSAND = new BigDecimal(1000);

    public static BigDecimal sqrt(BigDecimal a, final int SCALE) {
        BigDecimal x0 = new BigDecimal("0");
        BigDecimal x1 = new BigDecimal(Math.sqrt(a.doubleValue()));

        while (!x0.equals(x1)) {
            x0 = x1;
            x1 = a.divide(x0, SCALE, RoundingMode.HALF_UP);
            x1 = x1.add(x0);
            x1 = x1.divide(TWO, SCALE, RoundingMode.HALF_UP);
        }
        return x1;
    }

    public static BigDecimal max(BigDecimal... values) {
        return max(Arrays.asList(values));
    }

    public static BigDecimal max(List<BigDecimal> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        return list.stream().filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    }

    public static BigDecimal min(BigDecimal... values) {
        return min(Arrays.asList(values));
    }

    public static BigDecimal min(List<BigDecimal> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        return list.stream().filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    }


    public static <T> BigDecimal sum(List<T> collectEntities, Function<T, BigDecimal> map,Predicate<? super T> predicate) {
        if (CollectionUtils.isEmpty(collectEntities)) {
            return BigDecimal.ZERO;
        }
        return collectEntities.stream().filter(predicate).map(map).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }


    public static BigDecimal sum(BigDecimal... values) {
        List<BigDecimal> list = Arrays.asList(values);
        return list.stream().filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }


    public static BigDecimal sum(List<BigDecimal> list) {
        return list.stream().filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    public static <T> BigDecimal sum(List<T> collectEntities, Function<T, BigDecimal> map) {
        if (CollectionUtils.isEmpty(collectEntities)) {
            return BigDecimal.ZERO;
        }
        return collectEntities.stream().map(map).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    public static  BigDecimal multiply4(BigDecimal value1, BigDecimal value2) {
        if(value1 == null || value2 == null){
            throw new CommonException();
        }
        return value1.multiply(value2).setScale(4,RoundingMode.HALF_UP);
    }



    public static <T> BigDecimal max(List<T> collectEntities, Function<T, BigDecimal> map) {
        if (CollectionUtils.isEmpty(collectEntities)) {
            return BigDecimal.ZERO;
        }
        return collectEntities.stream().map(map).filter(Objects::nonNull).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    }

    public static <T> BigDecimal min(List<T> collectEntities, Function<T, BigDecimal> map) {
        if (CollectionUtils.isEmpty(collectEntities)) {
            return BigDecimal.ZERO;
        }
        return collectEntities.stream().map(map).filter(Objects::nonNull).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
    }

    public static <T> BigDecimal avg(List<T> collectEntities, Function<T, BigDecimal> map) {
        List<BigDecimal> collect = collectEntities.stream().map(map).filter(Objects::nonNull).collect(Collectors.toList());
        return avg(collect);
    }

    public static BigDecimal avg(List<BigDecimal> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return BigDecimal.ZERO;
        }
        return collect.stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO).divide(new BigDecimal(collect.size()), 4, RoundingMode.HALF_UP);
    }


    public static BigDecimal divide2(BigDecimal molecular, BigDecimal denominator) {
        return divide(molecular, denominator, 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide2Down(BigDecimal molecular, BigDecimal denominator) {
        return divide(molecular, denominator, 2, RoundingMode.DOWN);
    }

    public static BigDecimal divide4Down(BigDecimal molecular, BigDecimal denominator) {
        return divide(molecular, denominator, 4, RoundingMode.DOWN);
    }

    public static BigDecimal divide5Down(BigDecimal molecular, BigDecimal denominator) {
        return divide(molecular, denominator, 5, RoundingMode.DOWN);
    }

    public static BigDecimal divide4(BigDecimal molecular, BigDecimal denominator) {
        return divide(molecular, denominator, 4, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide5(BigDecimal molecular, BigDecimal denominator) {
        return divide(molecular, denominator, 5, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide6(BigDecimal molecular, BigDecimal denominator) {
        return divide(molecular, denominator, 6, RoundingMode.HALF_UP);
    }

    public static BigDecimal divide(BigDecimal molecular, BigDecimal denominator, int scale, RoundingMode roundingMode) {
        if (molecular == null || denominator == null || denominator.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return molecular.divide(denominator, scale, roundingMode);
    }


    public static boolean equals4(BigDecimal v1, BigDecimal v2) {
        if (v1 == null || v2 == null) {
            return false;
        }
        return v1.setScale(4,RoundingMode.HALF_UP).compareTo(v2.setScale(4,RoundingMode.HALF_UP)) == 0;
    }



    public static boolean signEquals(BigDecimal v1, BigDecimal v2) {
        if (v1 == null || v2 == null) {
            return false;
        }
        if (v1.compareTo(v2) == 0) {
            return true;
        }
     return (v1.compareTo(BigDecimal.ZERO) < 0 && v2.compareTo(BigDecimal.ZERO) < 0 )||(v1.compareTo(BigDecimal.ZERO) > 0 && v2.compareTo(BigDecimal.ZERO) > 0 );
    }


}
