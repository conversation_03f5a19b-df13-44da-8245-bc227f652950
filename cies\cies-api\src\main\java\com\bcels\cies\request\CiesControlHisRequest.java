package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "场站控制")
public class CiesControlHisRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "历史记录ID")
    private String historyRecordId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "操作开始时间")
    private String startTime;

    @Schema(description = "操作结束时间")
    private String endTime;

    @Schema(description = "项目主键")
    private String projectId;
}
