package com.bcels.cies.emuns;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SpecialIndicatorEnum {

    DAILY_CHARGE("日充电量", "kWh"),
    DAILY_DISCHARGE("日放电量", "kWh"),
    ESS_POWER("储能站有功功率", "kW"),
    FACTORY_LOAD("工厂用电有功功率", "kW"),
    TOTAL_LOAD("工厂总有功功率", "kW"),
    ESS_SOC("储能站SOC", "%"),
    QUARTER_CHARGE("15分钟充电量", "kWh"),
    QUARTER_DISCHARGE("15分钟放电量", "kWh"),
    CHARGE_STATE("充放电状态","")
    ;

    private final String name;
    private final String unit;
}
