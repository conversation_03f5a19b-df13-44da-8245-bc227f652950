package com.bcels.cies.domain;

import com.alibaba.fastjson.JSONObject;
import com.bcels.cies.client.CiesInternalInterfaceClient;
import com.bcels.cies.infrastructure.utils.ExcelExportUtil;
import com.bcels.cies.repository.entity.CiesIndicatorsInfoEntity;
import com.bcels.cies.request.CiesIndicatorHisRequest;
import com.bcels.cies.request.CiesQueryIndicatorsRequest;
import com.bcels.cies.response.*;
import com.bcels.cies.service.ICiesEquipInfoService;
import com.bcels.cies.service.ICiesIndicatorsInfoService;
import com.zwy.common.utils.BeanCopyUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CiesStationMonitorDomainService {


    @Autowired
    private ICiesIndicatorsInfoService iCiesIndicatorsInfoService;

    @Autowired
    private ICiesEquipInfoService iCiesEquipInfoService;

    @Autowired
    private CiesInternalInterfaceClient client;

    /**
     * 设备树以及第一台设备的指标信息
     * @param projectId
     * @return
     */
    public CiesStationMonitorResponse findStationMonitorInfo(String projectId) {
        CiesStationMonitorResponse response = new CiesStationMonitorResponse();

        // 查询设备树
        List<CiesEquipTreeResponse> equipInfo = iCiesEquipInfoService.findEquipByProjectId(projectId);
        if (equipInfo == null) {
            return null;
        }
        // 转化数据为map
        Map<Integer, List<CiesEquipTreeResponse>> levelMap = equipInfo.stream()
                .collect(Collectors.groupingBy(CiesEquipTreeResponse::getLevel));

        // 获取父级类型
        List<CiesEquipTreeResponse> parentEquip = equipInfo.stream()
                .filter(obj -> 1 == obj.getLevel())
                .sorted(
                        Comparator.comparing(
                                CiesEquipTreeResponse::getSort,
                                Comparator.nullsLast(Comparator.naturalOrder())
                        )
                )
                .toList();
        response.setEquipTree(parentEquip);

        parentEquip.forEach(root ->
                buildChildren(root, levelMap, 2)
        );
        response.setEquipTree(parentEquip);

        // 初始化显示第一台设备下的指标信息
        List<CiesIndicatorsInfoResponse> indicatorList = iCiesIndicatorsInfoService.findIndicatorsByEquipId(parentEquip.get(0).getEquipId());
        response.setIndicators(indicatorList);
        return response;
    }

    /**
     * 查询指标历史
     * @param request
     * @return
     */
     public CiesIndicatorHis findIndicatorHis(CiesIndicatorHisRequest request){
         CiesIndicatorHis ciesIndicatorHis = new CiesIndicatorHis();
         List<CiesHisIndicatorsDataResponse> hisResponse = processIndicators(request);
         if (!CollectionUtils.isEmpty(hisResponse)) {
             DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

             // 按时间升序排序
             hisResponse.sort(Comparator.comparing(CiesHisIndicatorsDataResponse::getUpdateTime));
             // 提取并转换时间数组（格式化为 yyyy-MM-dd HH:mm）
             List<String> list = hisResponse.stream().map(data -> data.getUpdateTime().format(outputFormatter)).toList();
             // 提取指标值数组
             List<BigDecimal> list1 = hisResponse.stream().map(CiesHisIndicatorsDataResponse::getIndicatorValue).toList();
             ciesIndicatorHis.setTime(list);
             ciesIndicatorHis.setData(list1);
         }
         return ciesIndicatorHis;
     }

    /**
     * 导出指标历史
     * @param request
     * @param response
     * @throws IOException
     */
     public void export(CiesIndicatorHisRequest request, HttpServletResponse response) throws IOException {
         List<CiesHisIndicatorsDataResponse> responseList = processIndicators(request);
         // 获取指标与测点名称
         CiesIndicatorsInfoEntity byId = iCiesIndicatorsInfoService.getById(request.getIndicatorId());
         String indicatorName  = byId.getDataName();
         responseList.sort(Comparator.comparing(CiesHisIndicatorsDataResponse::getUpdateTime));
         DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
         responseList.forEach(obj -> {
             // 截断毫秒（保留到秒）
             LocalDateTime truncatedTime = obj.getUpdateTime().truncatedTo(ChronoUnit.SECONDS);

             // 格式化为字符串
             String formattedTime = truncatedTime.format(outputFormatter);
             obj.setUpdateTimeStr(formattedTime);
         });
         List<CiesIndicatorHisResponse> indicatorsHis =  BeanCopyUtil.copyListProperties(responseList,CiesIndicatorHisResponse::new);
         if (!CollectionUtils.isEmpty(indicatorsHis)) {
             indicatorsHis.forEach(item -> item.setIndicatorName(indicatorName));
         }
         String customFileName = "指标历史" + System.currentTimeMillis() + ".xlsx";
         response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

         response.setHeader("Content-Disposition", "attachment; filename=" + customFileName);

         byte[] excelBytes = ExcelExportUtil.exportToExcel(indicatorsHis, "指标历史",CiesIndicatorHisResponse.class);
         response.getOutputStream().write(excelBytes);
     }

    /**
     * 按设备查询指标
     * @param equip
     * @return
     */
     public List<CiesIndicatorsInfoResponse>  findIndicatorsByEquipId(String equip){
         return iCiesIndicatorsInfoService.findIndicatorsByEquipId(equip);

     }

    /**
     * 处理设备树
     * @param parent
     * @param levelMap
     * @param currentLevel
     */

    private void buildChildren(CiesEquipTreeResponse parent, Map<Integer, List<CiesEquipTreeResponse>> levelMap, int currentLevel) {
        if (currentLevel > 4) return;
        // 获取当前层所有节点
        List<CiesEquipTreeResponse> currentLevelNodes = levelMap.getOrDefault(currentLevel, Collections.emptyList());

        // 筛选并挂载子节点
        List<CiesEquipTreeResponse> child = currentLevelNodes.stream().filter(node -> parent.getEquipId().equals(node.getParentEquipId())).toList();
        if (!CollectionUtils.isEmpty(child)){
            List<CiesEquipTreeResponse> sortableList = new ArrayList<>(child); // 创建可变副本
            sortableList.sort(
                    Comparator.comparing(
                            CiesEquipTreeResponse::getSort,
                            Comparator.nullsLast(Comparator.naturalOrder())
                    ).thenComparing(
                            CiesEquipTreeResponse::getEquipName,
                            Comparator.nullsLast(Comparator.naturalOrder())
                    )
            );
            child = sortableList; // 可选：重新赋值
        }
        parent.setChildren(child);
        child.forEach(item->{
            buildChildren(item, levelMap, currentLevel + 1);
        });
    }

    /**
     * 拆分历史数据用于图形展示
     * @param request
     * @return
     */
    public List<CiesHisIndicatorsDataResponse> processIndicators(CiesIndicatorHisRequest request) {
        CiesQueryIndicatorsRequest indicatorRequest = new CiesQueryIndicatorsRequest();
        indicatorRequest.setIndicatorIds(Stream.of(request.getIndicatorId())
                .collect(Collectors.toList()));
        indicatorRequest.setStartTime(request.getStartTime());
        indicatorRequest.setEndTime(request.getEndTime());
        indicatorRequest.setDataType(request.getDataType());
        Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap = client.batchQueryHisData(indicatorRequest);
        log.info("获取历史指标数据参数为：{},响应结果是：{}", JSONObject.toJSONString(indicatorRequest), stringListMap);
        if (stringListMap == null) {
            return null;
        }
        return stringListMap.get(request.getIndicatorId());
    }
}
