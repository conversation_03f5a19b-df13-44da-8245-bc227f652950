package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("cies_special_indicators")
@ApiModel(value = "CiesSpecialIndicatorsEntity对象", description = "特殊指标")
public class CiesSpecialIndicatorsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("特殊指标关联主键")
    @TableId
    private String specialIndicatorsId;

    @ApiModelProperty("业务指标名称")
    private String indicatorName;

    @ApiModelProperty("关联数据类型")
    private String relateDataType;

    @ApiModelProperty("数据名称")
    private String dataName;

    @ApiModelProperty("最新数据")
    private BigDecimal currentData;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("通道ID")
    private String cId;

    @ApiModelProperty("设备名称")
    private String equipName;

    @ApiModelProperty("设备ID")
    private String equipId;

    @ApiModelProperty("关联指标名称")
    private String relateIndicatorName;

    @ApiModelProperty("关联指标ID")
    private String relateIndicatorId;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("并网点ID")
    private String connectionPointId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
