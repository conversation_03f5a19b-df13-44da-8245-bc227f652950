package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "通道配置")
public class DcpChannelConfigResponse  implements Serializable {

    @Schema(description = "通道ID")
    private String cId;

    @Schema(description = "通道编码")
    private Integer cCode;

    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "解析方式类型")
    private String parsingType;

    @Schema(description = "下发主题")
    private String issueTopic;

    @Schema(description = "订阅主题")
    private String subscribeTopic;

    @Schema(description = "协议类型")
    private String protocolType;

    @Schema(description = "端口号")
    private Integer portNumber;

    @Schema(description = "通道类型")
    private String channelType;

    @Schema(description = "通道状态")
    private Byte channelStatus;

    @Schema(description = "项目ID")
    private String pId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;
}
