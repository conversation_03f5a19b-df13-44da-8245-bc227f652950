package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bcels.cies.repository.entity.CiesDictEntity;
import com.bcels.cies.repository.mapper.CiesDictMapper;
import com.bcels.cies.service.ICiesDictService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.enums.YesOrNo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 字典映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Service
public class CiesDictServiceImpl extends ServiceImpl<CiesDictMapper, CiesDictEntity> implements ICiesDictService {

    @Override
    public Map<String, String> findDictByDictType(String dictType) {
        QueryWrapper<CiesDictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dict_type",dictType);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        List<CiesDictEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        Map<String, String> dictMap = list.stream()
                .collect(Collectors.toMap(
                        CiesDictEntity::getDictCode,
                        CiesDictEntity::getDictDesc
                ));
        return dictMap;
    }

    @Override
    public Map<String, String> findDictByDictCode(String dictCode) {
        QueryWrapper<CiesDictEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dict_code",dictCode);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        CiesDictEntity entity = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(entity)){
            return null;
        }
        Map<String, String> dictMap = new HashMap<>();
        dictMap.put(entity.getDictCode(),entity.getDictDesc());
        return dictMap;
    }

    @Override
    public CiesDictEntity findDictByDictCodeAndName(String dictType, String dictCode) {
        QueryWrapper<CiesDictEntity> queryWrapper = new QueryWrapper<>();
        //指定字段查询
        queryWrapper.eq("dict_type", dictType)
                .eq("dict_code", dictCode)
                .eq("dr", YesOrNo.NO.getCode());
        CiesDictEntity entity = getOne(queryWrapper);
        return entity;
    }
}
