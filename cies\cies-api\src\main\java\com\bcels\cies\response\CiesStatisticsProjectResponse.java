package com.bcels.cies.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "项目信息")
public class CiesStatisticsProjectResponse implements Serializable {

    @Schema(description = "投产项目数量")
    private int operatingProjectCount;

    @Schema(description = "在建项目数量")
    private int constructingProjectCount;

    @Schema(description = "项目数量（投产状态）")
    private int totalProjectCount;

    @Schema(description = "投产项目总容量(MW)")
    private BigDecimal operatingTotalCapacity;

    @Schema(description = "在建项目总容量(MW)")
    private BigDecimal constructingTotalCapacity;

    @Schema(description = "项目总容量")
    private BigDecimal totalCapacity;

    @Schema(description = "运行项目数量")
    private int runningProjectQuantity;

    @Schema(description = "停运项目数量")
    private int stopProjectQuantity;

    @Schema(description = "项目数量（运行状态）")
    private int projectQuantity;

    @Schema(description = "布局省份数量")
    private  int coveredProvinceCount;
}
