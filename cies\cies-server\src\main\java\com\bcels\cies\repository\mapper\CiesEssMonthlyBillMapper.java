package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.repository.entity.CiesEquipInfoEntity;
import com.bcels.cies.repository.entity.CiesEssMonthlyBillEntity;
import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.request.CiesEssMonthlyBillRequest;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.bcels.cies.response.CiesEssMonthlyBillResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 设备信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Mapper
public interface CiesEssMonthlyBillMapper extends BaseMapper<CiesEssMonthlyBillEntity> {

    IPage<CiesEssMonthlyBillResponse> findForPage(@Param("page") Page<CiesEssMonthlyBillResponse> page, @Param("request") CiesEssMonthlyBillRequest request);

    IPage<CiesEssMonthlyBillResponse> historySettlementPage(@Param("page") Page<CiesEssMonthlyBillResponse> page, @Param("request") CiesEssMonthlyBillRequest request);
}
