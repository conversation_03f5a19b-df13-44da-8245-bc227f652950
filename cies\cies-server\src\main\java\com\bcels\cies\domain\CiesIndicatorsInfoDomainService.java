package com.bcels.cies.domain;


import com.bcels.cies.request.CiesIndicatorHisRequest;
import com.bcels.cies.request.CiesIndicatorsInfoListRequest;
import com.bcels.cies.request.CiesIndicatorsInfoUpdateRequest;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.bcels.cies.service.ICiesIndicatorsInfoService;
import com.zwy.common.utils.bean.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CiesIndicatorsInfoDomainService {

    @Autowired
    private ICiesIndicatorsInfoService iCiesIndicatorsInfoService;

    /**
     * 获取测点与指标（分页）
     *
     * @return
     */
    public PageResponse<CiesIndicatorsInfoResponse> findIndicatorsForPage(CiesIndicatorsInfoListRequest request) {
        return iCiesIndicatorsInfoService.findIndicatorsForPage(request);
    }

    public void updateIndicator(CiesIndicatorsInfoUpdateRequest request){
        iCiesIndicatorsInfoService.updateIndicator(request);
    }
    public CiesIndicatorsInfoResponse findIndicatorById(String indicatorId){
        return iCiesIndicatorsInfoService.findIndicatorById(indicatorId);
    }

    public List<CiesIndicatorsInfoResponse> findIndicators(String projectId){
        return iCiesIndicatorsInfoService.findIndicators(projectId);
    }
}
