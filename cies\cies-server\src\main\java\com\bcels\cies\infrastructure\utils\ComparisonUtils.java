package com.bcels.cies.infrastructure.utils;

import com.bcels.cies.response.CiesPeakValleyPriceGapResponse;
import com.zwy.common.utils.exception.BusinessException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class ComparisonUtils {
    // 复合键类（需重写equals和hashCode）
    private static class CompositeKey {
        private final String connectionPointName;
        private final String statPeriod;
        private final String timeRange;

        public CompositeKey(CiesPeakValleyPriceGapResponse response) {
            this.connectionPointName  = response.getConnectionPointName();
            this.statPeriod  = response.getStatPeriod();
            this.timeRange  = response.getTimeRange();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass())  return false;
            CompositeKey that = (CompositeKey) o;
            return Objects.equals(connectionPointName,  that.connectionPointName)  &&
                    Objects.equals(statPeriod,  that.statPeriod)  &&
                    Objects.equals(timeRange,  that.timeRange);
        }

        @Override
        public int hashCode() {
            return Objects.hash(connectionPointName,  statPeriod, timeRange);
        }
    }

    // 核心比较逻辑
    public static void checkBilledUsageConsistency(
            List<CiesPeakValleyPriceGapResponse> list1,
            List<CiesPeakValleyPriceGapResponse> list2) {

        // 构建两个Map：复合键 -> billedUsage
        Map<CompositeKey, BigDecimal> map1 = list1.stream()
                .collect(Collectors.toMap(
                        CompositeKey::new,
                        CiesPeakValleyPriceGapResponse::getBilledUsage,
                        (oldValue, newValue) -> oldValue)); // 重复键处理（保留旧值）

        Map<CompositeKey, BigDecimal> map2 = list2.stream()
                .collect(Collectors.toMap(
                        CompositeKey::new,
                        CiesPeakValleyPriceGapResponse::getBilledUsage,
                        (oldValue, newValue) ->  oldValue));

        // 检查一致性
        for (Map.Entry<CompositeKey, BigDecimal> entry : map1.entrySet())  {
            CompositeKey key = entry.getKey();
            BigDecimal billedUsage1 = entry.getValue();
            BigDecimal billedUsage2 = map2.get(key);

            if (billedUsage2 != null && !Objects.equals(billedUsage1,  billedUsage2)) {
                throw new BusinessException("修改结算信息后必须重新计算，请点击重新计算按钮重新生成结算单信息");
            }
        }
    }
}
