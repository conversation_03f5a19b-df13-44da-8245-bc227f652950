package com.bcels.cies.infrastructure.utils;

import io.swagger.annotations.ApiModelProperty;
import org.apache.poi.ss.usermodel.*;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

public class ExcelPoiParser {

    /**
     * 解析Excel到对象列表（支持.xls和.xlsx）
     * @param is 文件输入流
     * @param clazz 目标对象类型
     */
    public static <T> List<T> parse(InputStream is, Class<T> clazz) throws Exception {
        // 1. 获取注解映射关系（列名 -> 字段名）
        Map<String, String> headerMap = getHeaderMapping(clazz);

        // 2. 创建Workbook（自动识别版本）
        Workbook workbook = WorkbookFactory.create(is);
        Sheet sheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = sheet.iterator();

        // 3. 处理表头
        Row headerRow = rowIterator.next();
        Map<Integer, String> colIndexToField = new HashMap<>();
        for (Cell cell : headerRow) {
            String headerName = cell.getStringCellValue().trim();
            if (headerMap.containsKey(headerName))  {
                colIndexToField.put(cell.getColumnIndex(),  headerMap.get(headerName));
            }
        }

        // 4. 解析数据行
        List<T> result = new ArrayList<>();
        while (rowIterator.hasNext())  {
            Row row = rowIterator.next();
            T obj = clazz.getDeclaredConstructor().newInstance();

            for (Cell cell : row) {
                String fieldName = colIndexToField.get(cell.getColumnIndex());
                if (fieldName != null) {
                    setFieldValue(obj, fieldName, cell);
                }
            }
            result.add(obj);
        }

        workbook.close();
        return result;
    }

    /**
     * 反射设置字段值（类型安全处理）
     * @param obj
     * @param fieldName
     * @param cell
     * @throws Exception
     */
    private static void setFieldValue(Object obj, String fieldName, Cell cell) throws Exception {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);

        switch (cell.getCellType())  {
            case STRING:
                field.set(obj,  convertType(field.getType(),  cell.getStringCellValue()));
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell))  {
                    field.set(obj,  cell.getDateCellValue());
                } else {
                    field.set(obj,  convertType(field.getType(),  String.valueOf(cell.getNumericCellValue())));
                }
                break;
            default:
                field.set(obj,  null);
        }
    }

    /**
     * 类型转换
     * @param targetType
     * @param value
     * @return
     */
    private static Object convertType(Class<?> targetType, String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        try {
            if (targetType == BigDecimal.class) {
                return new BigDecimal(value.trim());
            } else if (targetType == Double.class || targetType == double.class) {
                return Double.parseDouble(value.trim());
            } else if (targetType == Integer.class || targetType == int.class) {
                return (int) Math.round(Double.parseDouble(value.trim()));
            } else if (targetType == String.class) {
                return value.trim();
            }
            throw new IllegalArgumentException("不支持的目标类型: " + targetType.getName());
        } catch (Exception e) {
            throw new NumberFormatException(String.format(
                    "值【%s】无法转换为%s类型", value, targetType.getSimpleName()
            ));
        }
    }

    /**
     * 获取注解映射关系
     * @param clazz
     * @return
     */
    private static Map<String, String> getHeaderMapping(Class<?> clazz) {
        Map<String, String> map = new LinkedHashMap<>();
        for (Field field : clazz.getDeclaredFields())  {
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null) {
                map.put(annotation.value(),  field.getName());
            }
        }
        return map;
    }
}