package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesEssPlanAuditEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.request.CiesAuditOperateRequest;
import com.bcels.cies.response.CiesEnergyPlanAuditResponse;
import com.bcels.cies.response.CiesEnergyPlanListResponse;
import com.zwy.common.utils.bean.PageResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 储能计划下发审核表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface ICiesEssPlanAuditService extends IService<CiesEssPlanAuditEntity> {

    PageResponse<CiesEnergyPlanListResponse> findEnergyPlan(CiesEnergyPlanListRequest request);
    
    void updatePlanAuditStatus(CiesAuditOperateRequest auditStatusRequest);

    CiesEnergyPlanListResponse findAuditByProject(String projectId,String planDate);

    List<CiesEnergyPlanListResponse> findAuditByDate(String planDate);

    List<CiesEssPlanAuditEntity> findConfirmedReview();

    CiesEnergyPlanAuditResponse findPlanAuditInfo(String projectId, String planDate);


    void updatePlanStatus();

}
