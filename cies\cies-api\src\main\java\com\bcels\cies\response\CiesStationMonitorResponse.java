package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Schema(description = "场站监控信息")
public class CiesStationMonitorResponse implements Serializable {

    @Schema(description = "指标列表")
    private List<CiesIndicatorsInfoResponse> indicators;

    @Schema(description = "设备树")
    private List<CiesEquipTreeResponse> equipTree;
}
