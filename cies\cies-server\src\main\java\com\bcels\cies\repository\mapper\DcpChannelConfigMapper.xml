<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.DcpChannelConfigMapper">

    <select id="findChannelForPoint" resultType="com.bcels.cies.response.CiesPointAndIndicatorResponse">
        select DISTINCT  c_id, channel_name
        from dcp_channel_config
        where p_id = #{projectId} and dr = 0
    </select>
    <select id="findEquip" resultType="com.bcels.cies.response.CiesPointAndIndicatorResponse">
        select d_id as equipId, device_name as equipName
        from dcp_device_info
        where p_id = #{projectId} and dr = 0
    </select>
    <select id="findEquipForPoint" resultType="com.bcels.cies.response.CiesPointAndIndicatorResponse">
        SELECT DISTINCT  di.device_name AS equipName,
               di.d_id        AS equipId
        FROM dcp_channel_config cpc
            left join dcp_channel_point_config point on  point.c_id = cpc.c_id
                 LEFT JOIN dcp_device_point_config dpc ON point.dpc_id = dpc.dpc_id and dpc.dr = 0
                 LEFT JOIN dcp_device_info di ON dpc.d_id = di.d_id AND di.dr = 0
        WHERE cpc.c_id = #{request.cId}
          and cpc.p_id = #{request.projectId}

    </select>
    <select id="findTestPoint" resultType="com.bcels.cies.response.CiesPointAndIndicatorResponse">
        SELECT
        CONCAT_WS('-',
        pro.p_code,
        cpc.c_code,
        dt.dtm_type_code,
        di.d_code,
        pt.point_code
        ) AS relateIndicatorId,pt.point_name as relateIndicatorName
        FROM dcp_channel_config cpc
        LEFT JOIN dcp_channel_point_config point ON point.c_id = cpc.c_id and point.dr=0
        LEFT JOIN dcp_device_point_config dpc ON point.dpc_id  = dpc.dpc_id and dpc.dr=0
        LEFT JOIN dcp_device_info di ON dpc.d_id = di.d_id AND di.dr  = 0
        LEFT JOIN dcp_project_info pro ON pro.p_id = di.p_id AND pro.dr  = 0
        LEFT JOIN dcp_device_type dt ON dt.dt_id  = di.dt_id and dt.dr = 0
        LEFT JOIN dcp_point_type pt ON dpc.point_id  = pt.point_id and pt.dr = 0
        <where>
            <if test="request.cid  != null and request.cid  != ''">
                AND cpc.c_id = #{request.cId}
            </if>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND cpc.p_id = #{request.projectId}
            </if>
            <if test="request.equipId  != null and request.equipId  != ''">
                AND di.d_id=#{request.equipId}
            </if>
        </where>
    </select>
    <select id="findIndicatorForOne" resultType="com.bcels.cies.response.CiesPointAndIndicatorResponse">
        select DISTINCT  one.one_id as relateIndicatorId, one.indicator_name as relateIndicatorName
        from dcp_device_info d
        left join dcp_one_indicator_config one on one.d_id = d.d_id
        <where>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND d.p_id = #{request.projectId}
            </if>
            <if test="request.equipId  != null and request.equipId  != ''">
                AND one.d_id = #{request.equipId}
            </if>
            <if test="request.oneIndicatorId  != null and request.oneIndicatorId  != ''">
                AND one.one_id = #{request.oneIndicatorId}
            </if>
        and one.dr = 0
        </where>
    </select>
    <select id="findIndicatorForTwo" resultType="com.bcels.cies.response.CiesPointAndIndicatorResponse">
        select DISTINCT  two.two_id as relateIndicatorId, two.indicator_name as relateIndicatorName
        from dcp_device_info d
        left join dcp_two_indicator_config two on two.d_id = d.d_id
        <where>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND d.p_id = #{request.projectId}
            </if>
            <if test="request.equipId  != null and request.equipId  != ''">
                and two.d_id = #{request.equipId}
            </if>
            <if test="request.twoIndicatorId  != null and request.twoIndicatorId  != ''">
                AND two.two_id = #{request.twoIndicatorId}
            </if>
        and two.dr = 0
        </where>
    </select>
    <select id="findEquipById" resultType="java.lang.String">
        select   device_name
        from dcp_device_info
        where d_id = #{equipId} and dr = 0
    </select>
    <select id="findChannelById" resultType="java.lang.String">
        select channel_name
        from dcp_channel_config
        where c_id = #{cId} and dr = 0
    </select>
    <select id="findTestPointById" resultType="com.bcels.cies.response.CiesPointAndIndicatorResponse">
        select point_name AS relateIndicatorName,
               point_id   AS relateIndicatorId
        from dcp_point_type
        where   type = '1' and point_code = #{testPointId} and dr = 0
    </select>
</mapper>
