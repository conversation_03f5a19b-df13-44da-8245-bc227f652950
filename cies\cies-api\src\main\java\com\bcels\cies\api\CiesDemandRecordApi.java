package com.bcels.cies.api;


import com.bcels.cies.request.CiesDemandListRequest;
import com.bcels.cies.request.CiesDemandRequest;
import com.bcels.cies.response.CiesDemandDistributeListResponse;
import com.bcels.cies.response.CiesDemandResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "11、场站需量下发")
@FeignClient(name = "ciesDemandRecord", path = "/ciesDemandRecord/v1")
public interface CiesDemandRecordApi {

    @Operation(summary = "需量下发列表")
    @PostMapping("query")
    ResultData<CiesDemandDistributeListResponse> findDemandList(@RequestBody CiesDemandRequest request);

    @Operation(summary = "需量下发")
    @PostMapping("save")
    ResultData<Void> saveDemandList(@RequestBody CiesDemandListRequest request);

    @Operation(summary = "需量下发记录")
    @PostMapping("page")
    ResultData<PageResponse<CiesDemandResponse>> findDemandRecordsForPage(@RequestBody CiesDemandRequest request);
}
