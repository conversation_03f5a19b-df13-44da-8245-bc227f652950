package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "并网点返回信息")
public class CiesConnectionPointResponse implements Serializable {

    @Schema(description = "并网点主键")
    private String connectionPointId;

    @Schema(description = "并网点名称")
    private String connectionPointName;

    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "关联储能柜数量")
    private Integer num;

    @Schema(description = "容量占比")
    private BigDecimal capacityRatio;

    @Schema(description = "计划起始设点ID")
    private Integer planStartId;

    @Schema(description = "需量设点ID")
    private Integer demandId;

    @Schema(description = "通道ID")
    private String channelId;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "通道是否为并网点")
    private Integer isShow;
}
