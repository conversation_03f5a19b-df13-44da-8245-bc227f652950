package com.bcels.cies.repository.mapper;

import com.bcels.cies.repository.entity.CiesMarketElecPriEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesMarketConfigRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 市场分时电价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Mapper
public interface CiesMarketElecPriMapper extends BaseMapper<CiesMarketElecPriEntity> {


    List<CiesMarketElecPriEntity> findElecPriInfoByYear(@Param("projectId") String projectId, @Param("year") Integer year);

}
