package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 市场分时阶段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Getter
@Setter
@TableName("cies_market_stage")
@ApiModel(value = "CiesMarketStageEntity对象", description = "市场分时阶段")
public class CiesMarketStageEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("阶段主键")
    @TableId
    private String stageId;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("0时-0.5时")
    @TableField("period_0005")
    private String period0005;

    @ApiModelProperty("0.5时-1时")
    @TableField("period_0510")
    private String period0510;

    @ApiModelProperty("1时-1.5时")
    @TableField("period_1015")
    private String period1015;

    @ApiModelProperty("1.5时-2时")
    @TableField("period_1520")
    private String period1520;

    @ApiModelProperty("2时-2.5时")
    @TableField("period_2025")
    private String period2025;

    @ApiModelProperty("2.5时-3时")
    @TableField("period_2530")
    private String period2530;

    @ApiModelProperty("3时-3.5时")
    @TableField("period_3035")
    private String period3035;

    @ApiModelProperty("3.5时-4时")
    @TableField("period_3540")
    private String period3540;

    @ApiModelProperty("4时-4.5时")
    @TableField("period_4045")
    private String period4045;

    @ApiModelProperty("4.5时-5时")
    @TableField("period_4550")
    private String period4550;

    @ApiModelProperty("5时-5.5时")
    @TableField("period_5055")
    private String period5055;

    @ApiModelProperty("5.5时-6时")
    @TableField("period_5560")
    private String period5560;

    @ApiModelProperty("6时-6.5时")
    @TableField("period_6065")
    private String period6065;

    @ApiModelProperty("6.5时-7时")
    @TableField("period_6570")
    private String period6570;

    @ApiModelProperty("7时-7.5时")
    @TableField("period_7075")
    private String period7075;

    @ApiModelProperty("7.5时-8时")
    @TableField("period_7580")
    private String period7580;

    @ApiModelProperty("8时-8.5时")
    @TableField("period_8085")
    private String period8085;

    @ApiModelProperty("8.5时-9时")
    @TableField("period_8590")
    private String period8590;

    @ApiModelProperty("9时-9.5时")
    @TableField("period_9095")
    private String period9095;

    @ApiModelProperty("9.5时-10时")
    @TableField("period_95100")
    private String period95100;

    @ApiModelProperty("10时-10.5时")
    @TableField("period_100105")
    private String period100105;

    @ApiModelProperty("10.5时-11时")
    @TableField("period_105110")
    private String period105110;

    @ApiModelProperty("11时-11.5时")
    @TableField("period_110115")
    private String period110115;

    @ApiModelProperty("11.5时-12时")
    @TableField("period_115120")
    private String period115120;

    @ApiModelProperty("12时-12.5时")
    @TableField("period_120125")
    private String period120125;

    @ApiModelProperty("12.5时-13时")
    @TableField("period_125130")
    private String period125130;

    @ApiModelProperty("13时-13.5时")
    @TableField("period_130135")
    private String period130135;

    @ApiModelProperty("13.5时-14时")
    @TableField("period_135140")
    private String period135140;

    @ApiModelProperty("14时-14.5时")
    @TableField("period_140145")
    private String period140145;

    @ApiModelProperty("14.5时-15时")
    @TableField("period_145150")
    private String period145150;

    @ApiModelProperty("15时-15.5时")
    @TableField("period_150155")
    private String period150155;

    @ApiModelProperty("15.5时-16时")
    @TableField("period_155160")
    private String period155160;

    @ApiModelProperty("16时-16.5时")
    @TableField("period_160165")
    private String period160165;

    @ApiModelProperty("16.5时-17时")
    @TableField("period_165170")
    private String period165170;

    @ApiModelProperty("17时-17.5时")
    @TableField("period_170175")
    private String period170175;

    @ApiModelProperty("17.5时-18时")
    @TableField("period_175180")
    private String period175180;

    @ApiModelProperty("18时-18.5时")
    @TableField("period_180185")
    private String period180185;

    @ApiModelProperty("18.5时-19时")
    @TableField("period_185190")
    private String period185190;

    @ApiModelProperty("19时-19.5时")
    @TableField("period_190195")
    private String period190195;

    @ApiModelProperty("19.5时-20时")
    @TableField("period_195200")
    private String period195200;

    @ApiModelProperty("20时-20.5时")
    @TableField("period_200205")
    private String period200205;

    @ApiModelProperty("20.5时-21时")
    @TableField("period_205210")
    private String period205210;

    @ApiModelProperty("21时-21.5时")
    @TableField("period_210215")
    private String period210215;

    @ApiModelProperty("21.5时-22时")
    @TableField("period_215220")
    private String period215220;

    @ApiModelProperty("22时-22.5时")
    @TableField("period_220225")
    private String period220225;

    @ApiModelProperty("22.5时-23时")
    @TableField("period_225230")
    private String period225230;

    @ApiModelProperty("23时-23.5时")
    @TableField("period_230235")
    private String period230235;

    @ApiModelProperty("23.5时-24时")
    @TableField("period_235240")
    private String period235240;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
