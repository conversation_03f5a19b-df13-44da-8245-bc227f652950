package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 设备信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Getter
@Setter
@TableName("cies_equip_info")
@ApiModel(value = "CiesEquipInfoEntity对象", description = "设备信息")
public class CiesEquipInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备主键")
    @TableId
    private String equipId;

    @ApiModelProperty("设备名称")
    private String equipName;

    @ApiModelProperty("等级")
    private Integer level;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("上级设备ID")
    private String parentEquipId;

    @ApiModelProperty("上级设备名称")
    private String parentEquipName;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
