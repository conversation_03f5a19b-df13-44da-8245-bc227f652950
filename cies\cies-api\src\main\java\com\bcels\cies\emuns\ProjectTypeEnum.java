package com.bcels.cies.emuns;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 项目类型枚举
 */
@Getter
@AllArgsConstructor
public enum ProjectTypeEnum {
    ENERGY_STORAGE("ENERGY_STORAGE", "储能"),
    LOAD("LOAD", "负荷"),
    PHOTOVOLTAIC("PHOTOVOLTAIC", "光伏"),
    WIND_POWER("WIND_POWER", "风电"),
    ;
    private final String code;
    private final String desc;

    private static final Map<String, String> CODE_TO_DESC = Arrays.stream(values())
            .collect(Collectors.toMap(ProjectTypeEnum::getCode,  ProjectTypeEnum::getDesc));

    private static final Map<String, String> DESC_TO_CODE = Arrays.stream(values())
            .collect(Collectors.toMap(
                    ProjectTypeEnum::getDesc,
                    ProjectTypeEnum::getCode,
                    (oldVal, newVal) -> oldVal
            ));

    // 根据code获取desc
    public static String getDescByCode(String code) {
        return CODE_TO_DESC.getOrDefault(code,  "未知类型");
    }

    /**
     * 根据desc获取code（新增方法）
     * @throws IllegalArgumentException 当desc不存在时抛出（或返回null，根据业务需求选择）
     */
    public static String getCodeByDesc(String desc) {
        Objects.requireNonNull(desc,  "描述文本不能为空");
        String code = DESC_TO_CODE.get(desc.trim());  // 去除前后空格
        if (code == null) {
            throw new IllegalArgumentException("无效的项目类型: " + desc);
        }
        return code;
    }
}
