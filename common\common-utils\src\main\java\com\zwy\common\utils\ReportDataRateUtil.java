package com.zwy.common.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static com.zwy.common.utils.DateUtil.toMinuitOfDay;

@AllArgsConstructor
@Getter
public class ReportDataRateUtil {

    public static final int length = 3;

    private static final int amMin = 450;
    private static final int amMax = 720;
    private static final int pmMin = 990;
    private static final int pmMax = 1440;

    public static final List<BigDecimal> THREE_POINT = Arrays.asList(new BigDecimal("0.98"), new BigDecimal("0.91"), new BigDecimal("0.87"), new BigDecimal("0.81"));
    public static final List<BigDecimal> FOUR_POINT = Arrays.asList(new BigDecimal("0.97"), new BigDecimal("0.93"), new BigDecimal("0.91"), new BigDecimal("0.87"), new BigDecimal("0.83"));
    public static final List<BigDecimal> FIVE_POINT = Arrays.asList(new BigDecimal("0.99"), new BigDecimal("0.95"), new BigDecimal("0.93"), new BigDecimal("0.89"), new BigDecimal("0.86"), new BigDecimal("0.82"));


    public static BigDecimal calcFloatRate(LocalDateTime now) {
        int minuitOfDay = toMinuitOfDay(now);
        int batch = initDataFloatRate(now);
        BigDecimal floatRate = null;
        switch (batch) {
            case 0:
                floatRate = reBuildSubsidiaryRote(ReportDataRateUtil.THREE_POINT, minuitOfDay);
                break;
            case 1:
                floatRate = reBuildSubsidiaryRote(ReportDataRateUtil.FOUR_POINT, minuitOfDay);
                break;
            case 2:
                floatRate = reBuildSubsidiaryRote(ReportDataRateUtil.FIVE_POINT, minuitOfDay);
                break;
            default:
                floatRate = BigDecimal.ONE;
        }
        return floatRate;
    }

    private static BigDecimal reBuildSubsidiaryRote(List<BigDecimal> list, int minuitOfDay) {
        boolean timeFlag = (minuitOfDay >= amMin && minuitOfDay <= amMax) || (minuitOfDay >= pmMin && minuitOfDay <= pmMax);
        boolean amPre = minuitOfDay >= amMin && getNodeSize(minuitOfDay, amMin) < list.size();
        boolean amLast = minuitOfDay <= amMax && getNodeSize(amMax, minuitOfDay) < list.size();
        boolean pmPre = minuitOfDay >= pmMin && getNodeSize(minuitOfDay, pmMin) < list.size();
        boolean pmLast = minuitOfDay <= pmMax && getNodeSize(pmMax, minuitOfDay) < list.size();
        BigDecimal rote = BigDecimal.ONE;
        if (timeFlag && amPre) {
            rote = list.get(getNodeSize(minuitOfDay, amMin));
        } else if (timeFlag && amLast) {
            rote = list.get(getNodeSize(amMax, minuitOfDay));
        } else if (timeFlag && pmPre) {
            rote = list.get(getNodeSize(minuitOfDay, pmMin));
        } else if (timeFlag && pmLast) {
            rote = list.get(getNodeSize(pmMax, minuitOfDay));
        } else if (timeFlag) {
            rote = list.get(list.size() - 1);
        }
        return rote;

    }

    private static int getNodeSize(int timePre, int time) {
        return (timePre - time) / 15;
    }

    public static int initDataFloatRate(LocalDateTime dateTime) {
        if (dateTime == null) {
            dateTime = LocalDateTime.now();
        }
        int day = dateTime.getDayOfYear();
        return (day + (dateTime.getHour() / 12)) % length;
    }

}
