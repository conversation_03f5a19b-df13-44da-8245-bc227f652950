package com.bcels.cies.api;

import com.bcels.cies.request.*;
import com.bcels.cies.response.*;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;
import java.util.List;

@Tag(name = "7、储能计划API")
@FeignClient(name = "storgePlan", path = "/storgePlan/v1")
public interface CiesEnergyStorgePlanApi {

    @Operation(summary = "储能计划列表")
    @PostMapping("page")
    ResultData<PageResponse<CiesEnergyPlanListResponse>> findForPage(@RequestBody CiesEnergyPlanListRequest request);

    @Operation(summary = "导出储能计划列表")
    @PostMapping("exportPlanList")
    ResultData<Void> exportEnergyPlanList(@RequestBody CiesEnergyPlanListRequest request, HttpServletResponse response) throws IOException;

    @Operation(summary = "初始化计划")
    @PostMapping("init")
    ResultData<CiesEnergyPlanDetailResponse> initEnergyPlan(@RequestBody CiesEnergyPlanListRequest request);

    @Operation(summary = "保存全场站计划")
    @PostMapping("saveStationPlan")
    ResultData<Void> saveFullStationPlan(@RequestBody CiesStationPlanRequest request);

    @Operation(summary = "重置全场站计划")
    @PostMapping("findStationPlan")
    ResultData<CiesFullStationResponse> findFullStationPlan(@RequestBody CiesQueryStationPlanRequest request);

    @Operation(summary = "保存并网点计划")
    @PostMapping("saveConnPointPlan")
    ResultData<Void> saveCollPointPlan(@RequestBody CiesConnPointPlanRequest request);

    @Operation(summary = "查询并网点计划")
    @PostMapping("findConnPointPlan")
    ResultData<CiesInitConnPlanResponse>  getConnPlan(@RequestBody CiesQueryStationPlanRequest request);

    @Operation(summary = "查看计划详情")
    @PostMapping("findPlanDetail")
    ResultData<CiesEnergyPlanDetailResponse>  findEnergyPlanDetail(@RequestBody CiesEnergyPlanListRequest request);

    @Operation(summary = "审核计划")
    @PostMapping("reviewPlan")
    ResultData<Void> reviewPlan(@RequestBody CiesAuditOperateRequest request);

    @Operation(summary = "重新计算")
    @PostMapping("recalculatePlan")
    ResultData<Void> recalculatePlan(@RequestBody CiesAuditOperateRequest request);
}
