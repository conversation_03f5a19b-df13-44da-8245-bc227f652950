<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesEssMonthlyBillMapper">

    <select id="findForPage" resultType="com.bcels.cies.response.CiesEssMonthlyBillResponse">
        select  cei.enterprise_name,
        cei.enterprise_id,
        cpi.pro_name,
        cpi.project_id,
        cpi.template_type,
        cpi.settlement_project_name,
        cemb.ess_bill_id,
        cemb.settlement_month,
        cemb.peak_valley_revenue,
        cemb.party_a_income,
        cemb.party_b_income,
        cemb.settlement_status,
        cemb.dispute_countdown,
        cemb.issue_method
        from cies_ess_monthly_bill cemb
        INNER JOIN cies_project_info cpi on cemb.project_id = cpi.project_id
        INNER JOIN cies_enterprise_info cei on cei.enterprise_id = cpi.enterprise_id
        <where>
            <if test="request.enterpriseName!=null and request.enterpriseName!=''">
                AND cei.enterprise_name = #{request.enterpriseName}
            </if>
            <if test="request.proName!=null and request.proName!=''">
                AND cpi.pro_name = #{request.proName}
            </if>
            <if test="request.projectId!=null and request.projectId!=''">
                AND cpi.project_id = #{request.projectId}
            </if>
            <if test="request.settlementStatus!=null and request.settlementStatus!=''">
                AND cemb.settlement_status = #{request.settlementStatus}
            </if>
            <if test="request.issueMethod!=null and request.issueMethod!=''">
                AND cemb.issue_method = #{request.issueMethod}
            </if>
            <if test="request.startTime != null">
                AND cemb.settlement_month &gt;= #{request.settlementStartTime}
            </if>
            <if test="request.endTime != null">
                AND cemb.settlement_month &lt;= #{request.settlementEndTime}
            </if>
            AND cemb.dr=0 AND cpi.dr=0 AND cei.dr=0
        </where>
        order by cemb.create_time desc
    </select>


    <select id="historySettlementPage" resultType="com.bcels.cies.response.CiesEssMonthlyBillResponse">
        SELECT
            cei.enterprise_name,
            cpi.project_id,
            cpi.pro_name,
            cpi.template_type,
            cpi.settlement_project_name
        FROM cies_enterprise_info cei
        INNER JOIN cies_project_info cpi ON cei.enterprise_id = cpi.enterprise_id
        where cei.dr=0 and cpi.dr=0
    </select>
</mapper>
