package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "项目统计信息")
public class CiesStatisticsProjectInfoResponse implements Serializable {

    @Schema(description = "项目数量")
    private BigDecimal totalCount;

    @Schema(description = "项目运行数量")
    private BigDecimal runningCount;

    @Schema(description = "项目停运数量")
    private BigDecimal stoppedCount;

    @Schema(description = "项目运行百分比")
    private BigDecimal runningPercentage;

    @Schema(description = "项目停运百分比")
    private BigDecimal stoppedPercentage;

    @Schema(description = "总额定容量(KWH)")
    private BigDecimal totalRatedCapacity;

    @Schema(description = "总额定功率(KW)")
    private BigDecimal totalRatedPower;

    @Schema(description = "上月甲方收益")
    private BigDecimal lastMonthPartyAIncome;

    @Schema(description = "上月乙方收益")
    private BigDecimal lastMonthPartyBIncome;

    @Schema(description = "上月峰谷价差收益")
    private BigDecimal lastMonthPeakValleyRevenue;

    @Schema(description = "本年甲方收益")
    private BigDecimal yearToDatePartyAIncome;

    @Schema(description = "本年乙方收益")
    private BigDecimal yearToDatePartyBIncome;

    @Schema(description = "本年峰谷价差收益")
    private BigDecimal yearToDatePeakValleyRevenue;

    @Schema(description = "累计甲方收益")
    private BigDecimal totalPartyAIncome;

    @Schema(description = "累计乙方收益")
    private BigDecimal totalPartyBIncome;

    @Schema(description = "累计峰谷价差收益")
    private BigDecimal totalPeakValleyRevenue;
}
