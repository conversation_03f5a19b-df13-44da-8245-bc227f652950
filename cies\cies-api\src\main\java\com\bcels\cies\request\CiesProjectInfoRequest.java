package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "项目信息")
public class CiesProjectInfoRequest extends PageRequest implements Serializable {

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "结算状态")
    private String settlementStatus;

    @Schema(description = "租户编码")
    private String tenantCode;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "项目地区-省市区")
    private String areaCode;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "项目说明")
    private String projectDesc;

    @Schema(description = "额定容量(KWH)")
    private BigDecimal ratedCapacity;

    @Schema(description = "额定功率(KW)")
    private BigDecimal ratedPower;

    @Schema(description = "SOC上限(%)")
    private BigDecimal socUpperLimit;

    @Schema(description = "SOC下限(%)")
    private BigDecimal socLowerLimit;

    @Schema(description = "储能分类")
    private String energyStorageType;

    @Schema(description = "变压器容量(KVA)")
    private BigDecimal transformerCapacity;

    @Schema(description = "用户负荷功率上限(KW)")
    private BigDecimal loadPowerUpperLimit;

    @Schema(description = "项目投资金额(万元)")
    private BigDecimal investmentAmount;

    @Schema(description = "项目投资日期")
    private String investmentDate;

    @Schema(description = "项目投产日期")
    private String operationDate;

    @Schema(description = "合同充放电效率(%)")
    private BigDecimal chargeDischargeEfficiency;

    @Schema(description = "标称功率(KW)")
    private BigDecimal nominalPower;

    @Schema(description = "变流器数量")
    private Integer converterCount;

    @Schema(description = "电池单元数量")
    private Integer batteryModuleCount;

    @Schema(description = "电池单体数量")
    private Integer batteryCellCount;

    @Schema(description = "电池规格")
    private String batterySpec;

    @Schema(description = "最大可充功率(KW)")
    private BigDecimal maxChargePower;

    @Schema(description = "最大可放功率(KW)")
    private BigDecimal maxDischargePower;

    @Schema(description = "单柜额定容量(KWH)")
    private BigDecimal singleCabinetCapacity;

    @Schema(description = "用电地区")
    private String elecArea;

    @Schema(description = "用电类型")
    private String elecType;

    @Schema(description = "行业类型")
    private String industryType;

    @Schema(description = "电压等级")
    private String voltageLevel;

    @Schema(description = "甲方企业名称")
    private String partyAName;

    @Schema(description = "乙方企业名称")
    private String partyBName;

    @Schema(description = "结算单项目名称")
    private String settlementProjectName;

    @Schema(description = "甲方分成比例(%)")
    private BigDecimal partyARatio;

    @Schema(description = "乙方分成比例(%)")
    private BigDecimal partyBRatio;

    @Schema(description = "结算单模板")
    private String templateType;

    @Schema(description = "结算单税差（%）")
    private BigDecimal taxAdjustmentRate;

    @Schema(description = "结算电表倍率")
    private BigDecimal meterMultiplier;

    @Schema(description = "结算单异议等待工作日")
    private String disputeDays;

    @Schema(description = "结算单预期付款到账时间")
    private String expectedPaymentDate;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "结算单月份")
    private String settlementMonth;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;
}
