<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesControlHisRecordsMapper">

    <select id="findControlHisRecordsForPage"
            resultType="com.bcels.cies.response.CiesControlHisRecordsListResponse">
        SELECT records.*,dict.dict_desc as commandType FROM cies_control_his_records records
                 left join cies_dict dict on dict.dict_code = records.command_type
                left join  cies_project_info pro on pro.project_id = records.project_id
        WHERE records.create_time BETWEEN
        STR_TO_DATE(#{request.startTime},  '%Y/%m/%d')
        AND
        STR_TO_DATE(#{request.endTime},  '%Y/%m/%d') + INTERVAL '1' DAY - INTERVAL '1' SECOND
        <if test="request.projectId  != null and request.projectId  != ''">
            AND records.project_id = #{request.projectId}
        </if>
        order by records.create_time desc
    </select>
</mapper>
