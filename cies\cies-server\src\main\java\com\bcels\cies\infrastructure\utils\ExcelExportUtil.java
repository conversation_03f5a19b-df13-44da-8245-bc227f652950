package com.bcels.cies.infrastructure.utils;


import com.zwy.common.utils.exception.BusinessException;
import io.swagger.annotations.ApiModelProperty;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Excel导出工具类
 * 功能：根据对象属性名自动生成表头，支持泛型List集合导出
 * 日期：2025-05-15
 */
public class ExcelExportUtil {

    public static <T> byte[] exportToExcel(List<T> dataList, String sheetName, Class<T> clazz) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 创建Sheet页
            Sheet sheet = workbook.createSheet(sheetName);

            // 生成表头
            Row headerRow = sheet.createRow(0);
            Field[] fields = clazz.getDeclaredFields();
            CellStyle headerStyle = createHeaderStyle(workbook);

            for (int i = 0; i < fields.length;  i++) {
                Cell cell = headerRow.createCell(i);
                String headerText = getHeaderText(fields[i]);
                cell.setCellValue(headerText);
                cell.setCellStyle(headerStyle);
                sheet.autoSizeColumn(i);
            }

            // 填充数据行（仅当 dataList 非空时）
            if (dataList != null && !dataList.isEmpty())  {
                CellStyle dataStyle = createDataStyle(workbook);
                for (int rowNum = 0; rowNum < dataList.size();  rowNum++) {
                    Row row = sheet.createRow(rowNum  + 1);
                    T item = dataList.get(rowNum);
                    populateRowData(row, item, fields, dataStyle);
                }
            }

            workbook.write(out);
            return out.toByteArray();
        } catch (IllegalAccessException e) {
            throw new RuntimeException("字段访问失败", e);
        }
    }

    /**
     * 填充行数据（支持常见Java类型）
     */
    private static <T> void populateRowData(Row row, T item, Field[] fields, CellStyle style)
            throws IllegalAccessException {
        for (int i = 0; i < fields.length;  i++) {
            fields[i].setAccessible(true);
            Object value = fields[i].get(item);
            Cell cell = row.createCell(i);
            cell.setCellStyle(style);

            if (value == null) {
                cell.setCellValue("");
            } else if (value instanceof Number) {
                cell.setCellValue(((Number)  value).doubleValue());
            } else if (value instanceof Date) {
                cell.setCellValue(new  SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date) value));
            } else if (value instanceof Boolean) {
                cell.setCellValue((Boolean)  value);
            } else {
                cell.setCellValue(value.toString());
            }
        }
    }

    /**
     * 创建表头样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short)  12);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        return style;
    }

    /**
     * 创建数据行样式
     */
    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN);
        return style;
    }

    /**
     * 获取字段的表头文本（优先@ApiModelProperty，其次字段名）
     */
    private static String getHeaderText(Field field) {
        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
        return annotation != null && !annotation.value().isEmpty()  ?
                annotation.value()  : field.getName();
    }
}
