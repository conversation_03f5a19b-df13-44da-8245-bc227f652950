package com.zwy.common.utils;

import lombok.extern.slf4j.*;
import org.apache.commons.lang3.*;

import java.lang.reflect.*;

/**
 * <AUTHOR>
 * 2024/5/10 16:45
 */
@Slf4j
public class GenericsUtil {


    public static void setGValue(Object object, String fieldName, Object value) {
        if(object == null || StringUtils.isEmpty(fieldName)) {
            return;
        }
        try {
            //获取父类中的fields
            Field field = findFieldInHierarchy(object,fieldName);
            assert field != null;
            field.setAccessible(true);
            field.set(object, value);
        } catch(Exception  e) {
            log.warn("操作泛型失败", e);
        }
    }


    public static Object getGValue(Object object, String fieldName) {
        if(object == null || StringUtils.isEmpty(fieldName)) {
            return null;
        }
        try {
            Field field = findFieldInHierarchy(object,fieldName);
            assert field != null;
            field.setAccessible(true);
            return field.get(object);
        } catch(Exception e) {
            log.warn("操作泛型失败", e);
        }
        return null;
    }

    private static Field findFieldInHierarchy(Object object, String fieldName) {
        Class<?> clazz = object.getClass();
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }
}
