package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.emuns.AuditStatusEnum;
import com.bcels.cies.emuns.DispatchStatusEnum;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.repository.entity.CiesEssPlanAuditEntity;
import com.bcels.cies.repository.mapper.CiesEssPlanAuditMapper;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.request.CiesAuditOperateRequest;
import com.bcels.cies.response.CiesEnergyPlanAuditResponse;
import com.bcels.cies.response.CiesEnergyPlanListResponse;
import com.bcels.cies.service.ICiesEssPlanAuditService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class CiesEssPlanAuditServiceImpl extends ServiceImpl<CiesEssPlanAuditMapper, CiesEssPlanAuditEntity> implements ICiesEssPlanAuditService {

    @Autowired
    private CiesEssPlanAuditMapper ciesEssPlanAuditMapper;

    @Override
    public PageResponse<CiesEnergyPlanListResponse> findEnergyPlan(CiesEnergyPlanListRequest request) {

        Page<CiesEnergyPlanListResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesEnergyPlanListResponse> pageResult = ciesEssPlanAuditMapper.findProjectInfoForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());

    }

    @Override
    public void updatePlanAuditStatus(CiesAuditOperateRequest auditStatusRequest) {
        UpdateWrapper<CiesEssPlanAuditEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper
                .set(auditStatusRequest.getIsAdjusted() != null, "is_adjusted", auditStatusRequest.getIsAdjusted())
                .set(StringUtils.isNotBlank(auditStatusRequest.getAuditStatus()), "audit_status", auditStatusRequest.getAuditStatus())
                .set(StringUtils.isNotBlank(auditStatusRequest.getDispatchStatus()), "dispatch_status", auditStatusRequest.getDispatchStatus())
                .set(StringUtils.isNotBlank(auditStatusRequest.getReviewBy()), "review_by", auditStatusRequest.getReviewBy())
                .set(auditStatusRequest.getReviewTime() != null, "review_time", auditStatusRequest.getReviewTime())
                .set("dr",auditStatusRequest.getDr())
                .set("update_time", LocalDateTime.now())
                .set("update_by", CiesUserContext.getCurrentUser())
                .eq(StringUtils.isNotBlank(auditStatusRequest.getProjectId()),"project_id", auditStatusRequest.getProjectId())
                .in(!CollectionUtils.isEmpty(auditStatusRequest.getPlanDateList()),"plan_date", auditStatusRequest.getPlanDateList())
                .eq(StringUtils.isNotBlank(auditStatusRequest.getDispatchAuditId()),"dispatch_audit_id",auditStatusRequest.getDispatchAuditId());

        ciesEssPlanAuditMapper.update(null, updateWrapper);
    }

    @Override
    public CiesEnergyPlanListResponse findAuditByProject(String projectId, String planDate) {
        QueryWrapper<CiesEssPlanAuditEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.eq("plan_date", planDate);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        CiesEssPlanAuditEntity entity = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        return BeanCopyUtil.copyProperties(entity, CiesEnergyPlanListResponse::new);
    }

    @Override
    public List<CiesEnergyPlanListResponse> findAuditByDate(String planDate) {
        QueryWrapper<CiesEssPlanAuditEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plan_date", planDate);
        List<CiesEssPlanAuditEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return BeanCopyUtil.copyListProperties(list, CiesEnergyPlanListResponse::new);
    }

    @Override
    public List<CiesEssPlanAuditEntity> findConfirmedReview() {
        QueryWrapper<CiesEssPlanAuditEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("audit_status", AuditStatusEnum.CONFIRMED.getCode());
        queryWrapper.eq("dispatch_status", DispatchStatusEnum.DISTRIBUTED.getCode());
        queryWrapper.eq("plan_date", LocalDate.now().plusDays(1).toString().replace("-","/"));
        return list(queryWrapper);
    }

    @Override
    public CiesEnergyPlanAuditResponse findPlanAuditInfo(String projectId, String planDate) {
        return ciesEssPlanAuditMapper.findPlanAuditInfo(projectId, planDate);
    }

    @Override
    public void updatePlanStatus() {
        UpdateWrapper<CiesEssPlanAuditEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("audit_status", AuditStatusEnum.EXPIRED.getCode())
                .set("update_time", LocalDateTime.now())
                .set("update_by", "服务器")
                .eq("audit_status", AuditStatusEnum.PENDING_REVIEW.getCode())
                .eq("plan_date", LocalDate.now().toString().replace("-","/"))
                .eq("dr", YesOrNo.NO.getCode());
        ciesEssPlanAuditMapper.update(null, updateWrapper);
    }
}
