<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesEssPlanAuditMapper">

    <select id="findProjectInfoForPage" resultType="com.bcels.cies.response.CiesEnergyPlanListResponse">
        select audit.*,enter.enterprise_name,pro.pro_name
        from cies_ess_plan_audit audit
        left join cies_project_info pro on pro.project_id=audit.project_id
        inner join cies_enterprise_info enter on enter.enterprise_id=pro.enterprise_id
        <where>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND pro.project_id  = #{request.projectId}
            </if>
            <if test="request.enterpriseName  != null and request.enterpriseName  != ''">
                AND enter.enterprise_name  = #{request.enterpriseName}
            </if>
            <if test="request.auditStatus  != null and request.auditStatus  != ''">
                AND audit.audit_status LIKE CONCAT('%', #{request.auditStatus},  '%')
            </if>
            <if test="request.dispatchStatus  != null and request.dispatchStatus  != ''">
                AND audit.dispatch_status LIKE CONCAT('%', #{request.dispatchStatus},  '%')
            </if>
            <if test="request.planStartTime  != null and request.planStartTime  != '' and request.planEndTime  != null and request.planEndTime  != ''">
            AND audit.plan_date between #{request.planStartTime} and #{request.planEndTime}
            </if>
            AND pro.project_type  = 'ENERGY_STORAGE'
        </where>
        and audit.dr = 0
        order by enter.enterprise_name,pro.pro_name,audit.plan_date,pro.project_id desc
    </select>
    <select id="findPlanAuditInfo" resultType="com.bcels.cies.response.CiesEnergyPlanAuditResponse">
       SELECT plan1.create_by   AS updateBy,
              plan1.create_time AS updateTime,
              audit.review_by   AS viewBy,
              audit.review_time AS viewTime
       FROM cies_ess_plan_audit audit
                JOIN
            cies_energy_storage_plan plan1 on plan1.plan_date = audit.plan_date and plan1.project_id = audit.project_id
                JOIN
            (SELECT plan.energy_storage_plan_id,
                    MAX(plan.create_time) AS max_create_time
             FROM cies_energy_storage_plan plan
             WHERE plan.plan_date = #{planDate}
               AND plan.project_id = #{projectId}
             GROUP BY plan.energy_storage_plan_id limit 1 ) t ON t.energy_storage_plan_id = plan1.energy_storage_plan_id
       WHERE audit.project_id = #{projectId}
         AND audit.plan_date = #{planDate}
         AND plan1.energy_storage_plan_id = t.energy_storage_plan_id
         and audit.dr = 0
    </select>
</mapper>
