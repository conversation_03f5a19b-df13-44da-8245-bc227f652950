package com.zwy.common.tools;

import com.zwy.common.utils.ElePointUtil;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.Random;

import static com.zwy.common.utils.DateUtil.toDateString;
import static com.zwy.common.utils.DateUtil.toDateTimeString;

public class GeneratorData {

    private static final String base_line_sql = "INSERT INTO `electricity`.`ele_base_line` (`uid`, `device_code`, `device_type`, `data_type`, `device_category`, `point`, `point_value`, `point_time`, `effective_date`, `invalid_date`) VALUES ('0c0dbe9f556349a99347a9bf52c48d74', 'ele_055', '1', '%s', NULL, '%s', '%s', '%s', '%s', '%s');";

    private static final String data_sql = "INSERT INTO `electricity`.`ele_meter_collect` (`uid`, `meter_code`, `data_type`, `point`, `point_time`,  `active_power`, `collect_time`, `collect_date`) VALUES ('0c0dbe9f556349a99347a9bf52c48d74', 'ele_055','1','%s', '%s', '%s', '%s', '%s');";


    public void generatorBaseLine(LocalDate generatorDate) {
        String dateString = toDateString(generatorDate);
        for (int i = 1; i <= 96; i++) {
            BigDecimal value = BigDecimal.valueOf(nextDouble(1900, 2100)).multiply(BigDecimal.valueOf(15));
            System.out.println(String.format(base_line_sql,2, i, value, toDateTimeString(ElePointUtil.findE96PointTime(i, generatorDate)), dateString, dateString));
        }
    }

    public void generatorPlanLine(LocalDate generatorDate) {
        String dateString = toDateString(generatorDate);
        for (int i = 1; i <= 96; i++) {
            BigDecimal value = BigDecimal.valueOf(nextDouble(2100, 2600)).multiply(BigDecimal.valueOf(15));
            System.out.println(String.format(base_line_sql,1, i, value, toDateTimeString(ElePointUtil.findE96PointTime(i, generatorDate)), dateString, dateString));
        }
    }

    public void generatorCollectData(LocalDate generatorDate) {
        String dateString = toDateString(generatorDate);
        for (int i = 1; i <= 96; i++) {
            BigDecimal value = BigDecimal.valueOf(nextDouble(2200, 2700)).multiply(BigDecimal.valueOf(15));
            String pointTime = toDateTimeString(ElePointUtil.findE96PointTime(i, generatorDate));
            System.out.println(String.format(data_sql, i,pointTime, value, pointTime, dateString));
        }

    }

    public double nextDouble(final int min, final int max) {
        DecimalFormat df = new DecimalFormat("#.00");
        if (max < min) {
            throw new RuntimeException("min < max");
        }
        if (min == max) {
            return min;
        }
        return Double.parseDouble(df.format(min + ((max - min) * new Random().nextDouble())));

    }

    public static void main(String[] args) {
        LocalDate generatorDate = LocalDate.now();
        GeneratorData generatorData = new GeneratorData();
//        generatorData.generatorBaseLine(generatorDate);
//        generatorData.generatorPlanLine(generatorDate);
        generatorData.generatorCollectData(generatorDate);

    }
}
