package com.zwy.common.utils;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;


public class IntegerUtils {


    public static Integer max(Integer... values) {
        List<Integer> list = Arrays.asList(values);
        return list.stream().filter(Objects::nonNull).max(Integer::compareTo).orElse(null);
    }

    public static Integer min(Integer... values) {
        List<Integer> list = Arrays.asList(values);
        return list.stream().filter(Objects::nonNull).min(Integer::compareTo).orElse(null);
    }

    public static Integer sum(Integer... values) {
        List<Integer> list = Arrays.asList(values);
        return list.stream().filter(Objects::nonNull).mapToInt(m -> m).sum();
    }


    public static <T> Integer sum(List<T> collectEntities, Function<T, Integer> map) {
        if (CollectionUtils.isEmpty(collectEntities)) {
            return 0;
        }
        return collectEntities.stream()
                .map(map)
                .filter(Objects::nonNull)
                .mapToInt(m -> m).sum();
    }


}
