package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.repository.entity.CiesEquipInfoEntity;
import com.bcels.cies.repository.mapper.CiesEquipInfoMapper;
import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.bcels.cies.response.CiesEquipTreeResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import com.bcels.cies.service.ICiesEquipInfoService;
import com.bcels.cies.service.ICiesProjectInfoService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class CiesEquipInfoServiceImpl extends ServiceImpl<CiesEquipInfoMapper, CiesEquipInfoEntity> implements ICiesEquipInfoService {


    @Autowired
    private ICiesProjectInfoService iCiesProjectInfoService;

    @Autowired
    private CiesEquipInfoMapper ciesEquipInfoMapper;

    @Override
    public PageResponse<CiesEquipInfoResponse> findEquipInfoForPage(CiesEquipInfoRequest request) {

        Page<CiesEquipInfoResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesEquipInfoResponse> pageResult = ciesEquipInfoMapper.findEquipInfoForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }

    @Override
    public void updateEquipInfo(CiesEquipInfoRequest request) {
        CiesEquipInfoEntity ciesEquipInfoEntity = BeanCopyUtil.copyProperties(request, CiesEquipInfoEntity::new);
        ciesEquipInfoEntity.setUpdateBy(CiesUserContext.getCurrentUser());
        ciesEquipInfoEntity.setUpdateTime(LocalDateTime.now());
        ciesEquipInfoEntity.setDr(YesOrNo.NO.getCode());

        UpdateWrapper<CiesEquipInfoEntity> wrapper = new UpdateWrapper<>();
        assert request.getSort() != null;
        wrapper.eq("equip_id", request.getEquipId())
                .set("dr", YesOrNo.NO.getCode())
                .set("level",request.getLevel())
                .set("sort", request.getSort())
                .set(StringUtils.isNotBlank(request.getRemark()),"remark",request.getRemark())
                .set(StringUtils.isNotBlank(request.getParentEquipId()),"parent_equip_id",request.getParentEquipId())
                .set("update_time", LocalDateTime.now())
                .set("update_by",CiesUserContext.getCurrentUser());
        ciesEquipInfoMapper.update(null, wrapper);
    }

    @Override
    public CiesEquipInfoResponse findEquipInfoById(String equipId) {
        CiesEquipInfoEntity equipInfo = this.getById(equipId);
        CiesEquipInfoResponse ciesEquipInfoResponse = BeanCopyUtil.copyProperties(equipInfo, CiesEquipInfoResponse::new);
        CiesProjectInfoResponse response = iCiesProjectInfoService.findEnterpriseByProjectId(ciesEquipInfoResponse.getProjectId());
        ciesEquipInfoResponse.setProName(response.getProName());
        return ciesEquipInfoResponse;
    }

    @Override
    public List<CiesEquipTreeResponse> findEquipByProjectId(String projectId) {
        QueryWrapper<CiesEquipInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("project_id",projectId).isNotNull("level");
        wrapper.eq("dr",YesOrNo.NO.getCode());
        List<CiesEquipInfoEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyListProperties(list,CiesEquipTreeResponse::new);
    }

    @Override
    public List<CiesEquipInfoResponse> findParentEquip(CiesEquipInfoRequest request) {
        QueryWrapper<CiesEquipInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("project_id",request.getProjectId());
        wrapper.eq("dr",YesOrNo.NO.getCode());
        wrapper.eq("level",request.getLevel()-1);
        wrapper.ne("equip_id",request.getEquipId());
        List<CiesEquipInfoEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyListProperties(list,CiesEquipInfoResponse::new);
    }

    @Override
    public void updateEquip(CiesEquipInfoEntity entity) {
        LambdaUpdateWrapper<CiesEquipInfoEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesEquipInfoEntity::getProjectId, entity.getProjectId())
                .eq(CiesEquipInfoEntity::getEquipId, entity.getEquipId())
                .set(CiesEquipInfoEntity::getEquipName, entity.getEquipName())
                .set(CiesEquipInfoEntity::getUpdateBy, entity.getUpdateBy())
                .set(CiesEquipInfoEntity::getUpdateTime, LocalDateTime.now());
        ciesEquipInfoMapper.update(null, wrapper);
    }
}
