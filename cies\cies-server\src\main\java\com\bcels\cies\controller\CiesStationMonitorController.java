package com.bcels.cies.controller;


import com.bcels.cies.api.CiesStationMonitorApi;
import com.bcels.cies.domain.CiesStationMonitorDomainService;
import com.bcels.cies.domain.CiesTaskDomainService;
import com.bcels.cies.request.CiesIndicatorHisRequest;
import com.bcels.cies.response.CiesIndicatorHis;
import com.bcels.cies.response.CiesIndicatorHisResponse;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.bcels.cies.response.CiesStationMonitorResponse;
import com.zwy.common.utils.bean.ResultData;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/ciesStationMonitor/v1")
public class CiesStationMonitorController implements CiesStationMonitorApi {

    @Autowired
    private CiesStationMonitorDomainService ciesStationMonitorDomainService;
    
    @Override
    @GetMapping("findByProjectId")
    public ResultData<CiesStationMonitorResponse> findEquipInfoById(@RequestParam("projectId") String projectId) {
        return ResultData.success(ciesStationMonitorDomainService.findStationMonitorInfo(projectId));
    }

    @Override
    @GetMapping("findByEquipId")
    public ResultData<List<CiesIndicatorsInfoResponse>> findIndicatorsByEquipId(@RequestParam("equipId") String equipId) {
        return ResultData.success(ciesStationMonitorDomainService.findIndicatorsByEquipId(equipId));
    }

    @Override
    @PostMapping("findIndicatorHis")
    public ResultData<CiesIndicatorHis> findIndicatorHis(@RequestBody CiesIndicatorHisRequest request){
        return ResultData.success(ciesStationMonitorDomainService.findIndicatorHis(request));
    }

    @Override
    @PostMapping("export")
    public ResultData<Void> export(@RequestBody CiesIndicatorHisRequest request, HttpServletResponse response) throws IOException {
        ciesStationMonitorDomainService.export(request,response);
        return ResultData.success();
    }
}
