package com.bcels.cies.infrastructure.exception;

import com.zwy.common.utils.bean.ResultData;
import com.zwy.common.utils.exception.BusinessException;
import com.zwy.common.utils.exception.CommonException;
import lombok.extern.slf4j.*;
import org.springframework.web.bind.annotation.*;


@Slf4j
@ControllerAdvice
public class DefaultExceptionHandler {

    @ResponseBody
    @ExceptionHandler(BusinessException.class)
    public ResultData handlerServerException(BusinessException ex) {
        log.error(ex.getMessage(), ex);
        if (ex.getErrorCode() != null) {
            return ResultData.fail(ex.getErrorCode());
        } else {
            return ResultData.fail(ex.getMessage());
        }
    }

    @ResponseBody
    @ExceptionHandler(CommonException.class)
    public ResultData handlerServerException(CommonException ex) {
        return ResultData.fail(ex.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    public ResultData handlerServerException(Exception ex) {
        log.error(ex.getMessage(), ex);
        return ResultData.fail(ex.getMessage());
    }

}
