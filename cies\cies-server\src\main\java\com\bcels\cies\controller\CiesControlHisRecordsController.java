package com.bcels.cies.controller;

import com.bcels.cies.api.CiesControlHisRecordsApi;
import com.bcels.cies.domain.CiesControlHisDomainService;
import com.bcels.cies.request.CiesControlHisRequest;
import com.bcels.cies.request.CiesQueryStationPlanRequest;
import com.bcels.cies.response.CiesControlHisRecordsListResponse;
import com.bcels.cies.response.CiesControlHisRecordsResponse;
import com.bcels.cies.response.CiesFullStationResponse;
import com.bcels.cies.response.CiesInitConnPlanResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

@RestController
@RequestMapping("/controlRecords/v1")
public class CiesControlHisRecordsController implements CiesControlHisRecordsApi {


    @Autowired
    private CiesControlHisDomainService ciesControlHisDomainService;

    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesControlHisRecordsListResponse>>  findControlHisRecordsForPage(@RequestBody CiesControlHisRequest request){
        return  ResultData.success(ciesControlHisDomainService.findControlHisRecordsForPage(request));
    }

    @Override
    @GetMapping("findControlHisDetail")
    public ResultData<CiesControlHisRecordsResponse>  findControlHisDetail(@RequestParam("historyRecordId") String historyRecordId){
        return  ResultData.success(ciesControlHisDomainService.findControlHisDetail(historyRecordId));
    }

    @Override
    @GetMapping("findConnHisPlan")
    public ResultData<CiesInitConnPlanResponse>  findConnHisPlan(@RequestParam("historyRecordId") String historyRecordId){
        return  ResultData.success(ciesControlHisDomainService.findConnHisPlan(historyRecordId));
    }

    @Override
    @GetMapping("findStationHisPlan")
    public ResultData<CiesFullStationResponse>  findStationHisPlan(@RequestParam("historyRecordId") String historyRecordId){
        return  ResultData.success(ciesControlHisDomainService.findStationHisPlan(historyRecordId));
    }
}
