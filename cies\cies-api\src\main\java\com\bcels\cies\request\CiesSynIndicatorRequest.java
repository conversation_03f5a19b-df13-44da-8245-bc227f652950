package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "同步数据中台指标信息")
public class CiesSynIndicatorRequest implements Serializable {

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "指标ID")
    private String indicatorId;

    @Schema(description = "设备ID")
    private String equipId;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "数据名称")
    private String dataName;

    @Schema(description = "通道ID")
    private String cId;

    @Schema(description = "设备名称")
    private String equipName;

    /**
     * 无需传输
     */
    @Schema(description = "通道名称")
    private String channelName;

    /**
     * 测点查询主键
     */
    private String cdpcId;
}
