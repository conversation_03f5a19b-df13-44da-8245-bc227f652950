package com.bcels.cies.response;


import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "场站监控-历史指标")
public class CiesIndicatorHisResponse {

    @ApiModelProperty("时间")
    private String updateTimeStr;

    @ApiModelProperty("指标名称")
    private String indicatorName;

    @ApiModelProperty("当前值")
    private BigDecimal indicatorValue;

}
