package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bcels.cies.repository.entity.CiesEsPlanRuleEntity;
import com.bcels.cies.repository.mapper.CiesEsPlanRuleMapper;
import com.bcels.cies.response.CiesConnectionPointResponse;
import com.bcels.cies.response.CiesEsPlanRuleResponse;
import com.bcels.cies.service.ICiesEsPlanRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.enums.YesOrNo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class CiesEsPlanRuleServiceImpl extends ServiceImpl<CiesEsPlanRuleMapper, CiesEsPlanRuleEntity> implements ICiesEsPlanRuleService {

    @Autowired
    private CiesEsPlanRuleMapper ciesEsPlanRuleMapper;

    @Override
    public CiesEsPlanRuleResponse findByProjectId(String projectId) {
        QueryWrapper<CiesEsPlanRuleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id ", projectId);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        CiesEsPlanRuleEntity entity = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(entity)) {
            return null;
        }
        return BeanCopyUtil.copyProperties(entity, CiesEsPlanRuleResponse::new);
    }

    @Override
    public CiesConnectionPointResponse findConnInfoById(String connectionPointId) {
        return BeanCopyUtil.copyProperties(getById(connectionPointId),CiesConnectionPointResponse::new);
    }

    @Override
    public void updateRule(CiesEsPlanRuleEntity esPlanRuleEntity) {
        LambdaUpdateWrapper<CiesEsPlanRuleEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesEsPlanRuleEntity::getRuleId,  esPlanRuleEntity.getRuleId())
                .set(CiesEsPlanRuleEntity::getTimeDimension, esPlanRuleEntity.getTimeDimension())
                .set(CiesEsPlanRuleEntity::getUpdateBy, esPlanRuleEntity.getUpdateBy())
                .set(CiesEsPlanRuleEntity::getUpdateTime, LocalDateTime.now());
        ciesEsPlanRuleMapper.update(null,  wrapper);
    }
}
