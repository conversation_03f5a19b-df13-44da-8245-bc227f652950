package com.bcels.cies.controller;

import com.bcels.cies.api.CiesEquipInfoApi;
import com.bcels.cies.domain.CiesEquipInfoDomainService;
import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import java.util.List;

@RestController
@RequestMapping("/equipInfo/v1")
public class CiesEquipInfoController implements CiesEquipInfoApi {

    @Autowired
    private CiesEquipInfoDomainService ciesEquipInfoDomainService;
    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesEquipInfoResponse>> findForPage(@RequestBody CiesEquipInfoRequest request) {
        return ResultData.success(ciesEquipInfoDomainService.findEquipForPage(request));
    }

    @Override
    @PostMapping("update")
    public ResultData<Void> updateEquipInfo(@RequestBody CiesEquipInfoRequest request) {
        ciesEquipInfoDomainService.updateEquipInfo(request);
        return ResultData.success();
    }

    @Override
    @GetMapping("findById")
    public ResultData<CiesEquipInfoResponse> findEquipInfoById(@RequestParam("equipId") String equipId) {
        return ResultData.success(ciesEquipInfoDomainService.findEquipInfoById(equipId));
    }

    @Override
    @PostMapping("findParentEquip")
    public ResultData<List<CiesEquipInfoResponse>> findParentEquip(CiesEquipInfoRequest request) {
        return ResultData.success(ciesEquipInfoDomainService.findParentEquip(request));
    }
}
