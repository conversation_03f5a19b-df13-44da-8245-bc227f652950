package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@TableName("cies_valley_power_config")
@ApiModel(value = "CiesValleyPowerConfigEntity对象", description = "深谷电量配置表")
public class CiesValleyPowerConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("配置主键")
    @TableId
    private String configId;

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("时间段")
    private String timeRangeName;

    @ApiModelProperty("储能结算主键")
    private String essBillId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
