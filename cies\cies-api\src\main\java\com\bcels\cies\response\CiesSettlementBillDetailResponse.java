package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zwy.common.utils.annotation.Dict;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@Schema(description = "结算单详情")
public class CiesSettlementBillDetailResponse implements Serializable {

    private String essBillId;

    @Schema(description = "结算月份")
    private String settlementMonth;

    @Schema(description = "结算状态")
    @Dict(code = "SETTLEMENT_BILL_STATUS")
    private String settlementStatus;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "用户结算附件地址")
    private String userAttachmentUrl;

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "结算单项目名称")
    private String settlementProjectName;

    @Schema(description = "开具方式")
    private String issueMethod;

    @Schema(description = "申诉原因")
    private String disputeReason;

    @Schema(description = "申诉倒计时")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime disputeCountdown;
    
    @Schema(description = "峰谷价差总收益（元）")
    private BigDecimal peakValleyRevenue;

    @Schema(description = "甲方分成比例(%)")
    private BigDecimal partyARatio;

    @Schema(description = "乙方分成比例(%)")
    private BigDecimal partyBRatio;

    @Schema(description = "甲方分成金额(元)")
    private BigDecimal partyAAmount;

    @Schema(description = "乙方分成金额(元)")
    private BigDecimal partyBAmount;

    @Schema(description = "税差计算比例")
    private BigDecimal taxAdjustmentRate;

    @Schema(description = "税差金额(元)")
    private BigDecimal taxAdjustmentAmount;

    @Schema(description = "是否单独计算深谷")
    private Integer isCalDeep;

    @Schema(description = "甲方收益（元）")
    private BigDecimal partyAIncome;

    @Schema(description = "乙方收益（元）")
    private BigDecimal partyBIncome;

    @Schema(description = "登记结算单PDF附件地址")
    private String pdfAttachmentUrl;

    @Schema(description = "登记结算单Excel/Word附件地址")
    private String officeAttachmentUrl;

    @Schema(description = "结算单明细")
    private List<CiesPeakValleyPriceGapResponse> billList;

    @Schema(description = "深谷日期")
    private String deepDate;

    @Schema(description = "深谷时间段")
    private String deepTime;

    @Schema(description = "深谷表头")
    private String[] title;

    @Schema(description = "深谷数据行")
    private String [][] data;
}

