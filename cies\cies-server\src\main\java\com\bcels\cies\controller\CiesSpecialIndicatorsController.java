package com.bcels.cies.controller;

import com.bcels.cies.api.CiesSpecialIndicatorsInfoApi;
import com.bcels.cies.domain.CiesSpecialIndicatorsDomainService;
import com.bcels.cies.request.*;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.bcels.cies.response.CiesPointAndIndicatorResponse;
import com.bcels.cies.response.CiesSpecialIndicatorsResponse;
import com.bcels.cies.service.ICiesSpecialIndicatorsService;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import java.util.List;

@RestController
@RequestMapping("/ciesSpecialIndicators/v1")
public class CiesSpecialIndicatorsController implements CiesSpecialIndicatorsInfoApi {


    @Autowired
    private CiesSpecialIndicatorsDomainService ciesSpecialIndicatorsDomainService;

    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesSpecialIndicatorsResponse>> findForPage(@RequestBody CiesSpecialIndicatorsListRequest request) {
        return ResultData.success(ciesSpecialIndicatorsDomainService.findIndicatorsForPage(request));
    }

    @Override
    @PostMapping("update")
    public ResultData<Void> updateSpecialIndicator(@RequestBody CiesSpecialIndicatorsUpdateRequest request){
        ciesSpecialIndicatorsDomainService.updateSpecialIndicator(request);
        return ResultData.success();
    }

    @Override
    @GetMapping("findById")
    public ResultData<CiesSpecialIndicatorsResponse> findSpecialIndicatorById(@RequestParam("specialIndicatorsId") String specialIndicatorsId) {
        return ResultData.success(ciesSpecialIndicatorsDomainService.findSpecialIndicatorById(specialIndicatorsId));
    }

    @Override
    @PostMapping("delete")
    public ResultData<Void> deleteIndicatorsById(@RequestBody CiesSpecialIndicatorsUpdateRequest request){
        ciesSpecialIndicatorsDomainService.deleteIndicatorsById(request);
        return ResultData.success();
    }


    @GetMapping("findChannelForPoint")
    public ResultData<List<CiesPointAndIndicatorResponse>> findChannelForPoint(@RequestParam("projectId") String projectId) {
        return ResultData.success(ciesSpecialIndicatorsDomainService.findChannelForPoint(projectId));
    }

    @GetMapping("findEquip")
    public ResultData<List<CiesPointAndIndicatorResponse>> findEquip(@RequestParam("projectId") String projectId) {
        return ResultData.success(ciesSpecialIndicatorsDomainService.findEquip(projectId));
    }

    @PostMapping("findEquipForPoint")
    public ResultData<List<CiesPointAndIndicatorResponse>> findEquipForPoint(@RequestBody CiesPointAndIndicatorRequest request) {
        return ResultData.success(ciesSpecialIndicatorsDomainService.findEquipForPoint(request));
    }
    @PostMapping("findTestPoint")
    public ResultData<List<CiesPointAndIndicatorResponse>> findTestPoint(@RequestBody CiesPointAndIndicatorRequest request) {
        return ResultData.success(ciesSpecialIndicatorsDomainService.findTestPoint(request));
    }
    @PostMapping("findIndicatorForOne")
    public ResultData<List<CiesPointAndIndicatorResponse>> findIndicatorForOne(@RequestBody CiesPointAndIndicatorRequest request) {
        return ResultData.success(ciesSpecialIndicatorsDomainService.findIndicatorForOne(request));
    }
    @PostMapping("findIndicatorForTwo")
    public ResultData<List<CiesPointAndIndicatorResponse>> findIndicatorForTwo(@RequestBody CiesPointAndIndicatorRequest request) {
        return ResultData.success(ciesSpecialIndicatorsDomainService.findIndicatorForTwo(request));
    }
}
