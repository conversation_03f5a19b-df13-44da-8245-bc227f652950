package com.bcels.cies.api;


import com.bcels.cies.request.CiesControlHisRequest;
import com.bcels.cies.request.CiesQueryStationPlanRequest;
import com.bcels.cies.response.CiesControlHisRecordsListResponse;
import com.bcels.cies.response.CiesControlHisRecordsResponse;
import com.bcels.cies.response.CiesFullStationResponse;
import com.bcels.cies.response.CiesInitConnPlanResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "10、控制历史记录API")
@FeignClient(name = "controlRecords", path = "/controlRecords/v1")
public interface CiesControlHisRecordsApi {

    @Operation(summary = "控制记录列表")
    @PostMapping("page")
    ResultData<PageResponse<CiesControlHisRecordsListResponse>> findControlHisRecordsForPage(@RequestBody CiesControlHisRequest request);

    @Operation(summary = "控制记录详情")
    @GetMapping("findControlHisDetail")
    @Parameters({
            @Parameter(name = "historyRecordId", description = "历史记录ID", required = true)
    })
    ResultData<CiesControlHisRecordsResponse>  findControlHisDetail(@RequestParam("historyRecordId") String historyRecordId);

    @Operation(summary = "历史并网点计划详情")
    @PostMapping("findConnHisPlan")
    @Parameters({
            @Parameter(name = "historyRecordId", description = "历史记录ID", required = true)
    })
    ResultData<CiesInitConnPlanResponse>  findConnHisPlan(@RequestParam("historyRecordId") String historyRecordId);

    @Operation(summary = "历史全场站计划详情")
    @PostMapping("findStationHisPlan")
    @Parameters({
            @Parameter(name = "historyRecordId", description = "历史记录ID", required = true)
    })
    ResultData<CiesFullStationResponse>  findStationHisPlan(@RequestParam("historyRecordId") String historyRecordId);
}
