package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@TableName("cies_operation_log")
@ApiModel(value = "CiesOperationLogEntity对象", description = "操作记录表")
public class CiesOperationLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("操作记录主键")
    @TableId
    private String logId;

    @ApiModelProperty("变更内容")
    private String changeContent;

    @ApiModelProperty("反馈内容")
    private String feedbackContent;

    @ApiModelProperty("变更时间")
    private LocalDateTime changeTime;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("储能结算主键")
    private String essBillId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
