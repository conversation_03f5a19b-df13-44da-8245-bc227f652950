package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesDictEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 字典映射表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface ICiesDictService extends IService<CiesDictEntity> {

      Map<String,String> findDictByDictType(String dictType);

      Map<String,String> findDictByDictCode(String dictCode);

      CiesDictEntity findDictByDictCodeAndName(String dict_type,String dictCode);
}
