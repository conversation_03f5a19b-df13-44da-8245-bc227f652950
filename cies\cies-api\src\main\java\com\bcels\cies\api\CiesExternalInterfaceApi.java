package com.bcels.cies.api;

import com.bcels.cies.request.CiesSynDeleteRequest;
import com.bcels.cies.request.CiesSynEquipRequest;
import com.bcels.cies.request.CiesSynIndicatorRequest;
import com.bcels.cies.request.CiesSynProjectRequest;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "11、数据中台同步数据API")
@FeignClient(name = "tranData", path = "/tranData/v1")
public interface CiesExternalInterfaceApi {

    @Operation(summary = "同步项目信息")
    @PostMapping("synProject")
    ResultData<Void> synProjectInfo(@RequestBody CiesSynProjectRequest request);

    @Operation(summary = "同步设备信息")
    @PostMapping("synEquipInfo")
    ResultData<Void> synEquipInfo(@RequestBody CiesSynEquipRequest request);

    @Operation(summary = "同步指标信息")
    @PostMapping("synIndicator")
    ResultData<Void> synIndicatorInfo(@RequestBody CiesSynIndicatorRequest request);

    @Operation(summary = "同步删除信息")
    @PostMapping("synDeleteRecord")
    ResultData<Void> synDeleteRecord(@RequestBody CiesSynDeleteRequest request);
}
