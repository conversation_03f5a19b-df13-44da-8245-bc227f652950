package com.bcels.cies.infrastructure.config;

import com.bcels.cies.infrastructure.utils.MarketApiUtil;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MarketApiConfig {

    @Value("${market.api.appId}")
    private String appId;

    @Value("${market.api.appSecret}")
    private String appSecret;


    @PostConstruct
    public void initConfigUtils() {
        MarketApiUtil.init(appId, appSecret);
    }
}
