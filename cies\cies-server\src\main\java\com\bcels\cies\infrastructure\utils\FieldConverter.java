package com.bcels.cies.infrastructure.utils;

import com.bcels.cies.emuns.PeriodTypeEnum;

import java.lang.reflect.Field;

/**
 * 映射时段枚举字典工具类
 */
public class FieldConverter {
    private static final String PREFIX = "period";
    public static void convertFields(Object obj) throws IllegalAccessException {
        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (isTargetField(field.getName()))  {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value instanceof String) {
                    String code = PeriodTypeEnum.getCodeByDesc((String)  value);
                    field.set(obj,  code);
                }
            }
        }
    }

    private static boolean isTargetField(String fieldName) {
        return fieldName.toLowerCase().startsWith(PREFIX);
    }
}