package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "深谷配置表")
public class CiesValleyPowerConfigResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private String configId;

    @Schema(description ="月份")
    private String month;

    @Schema(description ="日期")
    private String date;

    @Schema(description ="时间段")
    private String timeRangeName;

    @Schema(description ="储能结算主键")
    private String essBillId;

    @Schema(description ="创建人")
    private String createBy;

    @Schema(description ="创建时间")
    private LocalDateTime createTime;

    @Schema(description ="更新人")
    private String updateBy;

    @Schema(description ="更新时间")
    private LocalDateTime updateTime;

    @Schema(description ="是否删除 0否 1是")
    private Integer dr;
}
