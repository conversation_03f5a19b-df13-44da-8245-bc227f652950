package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
@Schema(description = "用户代购电-保存电价列表")
public class CiesMarketElecPriRecordRequest {

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "电价列表")
    private List<CiesMarketElecPriRequest> list;
}
