package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("cies_demand_distribute_record")
@ApiModel(value = "CiesDemandDistributeRecordEntity对象", description = "需量下发记录")
public class CiesDemandDistributeRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("需量下发主键")
    @TableId
    private String demandDistributeId;

    @ApiModelProperty("需量值")
    private BigDecimal distributeValue;

    @ApiModelProperty("并网点名称")
    private String connectionPointName;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("指令下发")
    private String commandType;

    @ApiModelProperty("并网点id")
    private String connectionPointId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
