package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@TableName("cies_ess_monthly_bill")
@ApiModel(value = "CiesEssMonthlyBillEntity对象", description = "储能月度结算表")
public class CiesEssMonthlyBillEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("储能结算主键")
    @TableId
    private String essBillId;

    @ApiModelProperty("结算月份")
    private String settlementMonth;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("峰谷价差总收益（元）")
    private BigDecimal peakValleyRevenue;

    @ApiModelProperty("甲方收益（元）")
    private BigDecimal partyAIncome;

    @ApiModelProperty("乙方收益（元）")
    private BigDecimal partyBIncome;

    @ApiModelProperty("结算状态")
    private String settlementStatus;

    @ApiModelProperty("开具方式")
    private String issueMethod;

    @ApiModelProperty("说明")
    private String remark;

    @ApiModelProperty("结算单项目名称")
    private String settlementProjectName;

    @ApiModelProperty("申诉原因")
    private String disputeReason;

    @ApiModelProperty("申诉时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime disputeCountdown;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("用户结算附件地址")
    private String userAttachmentUrl;

    @ApiModelProperty("登记结算单PDF附件地址")
    private String pdfAttachmentUrl;

    @ApiModelProperty("登记结算单Excel/Word附件地址")
    private String officeAttachmentUrl;

    @ApiModelProperty("是否单独计算深谷")
    private Integer isCalDeep;

    @ApiModelProperty("甲方分成比例(%)")
    private BigDecimal partyARatio;

    @ApiModelProperty("乙方分成比例(%)")
    private BigDecimal partyBRatio;

    @ApiModelProperty("甲方分成金额(元)")
    private BigDecimal partyAAmount;

    @ApiModelProperty("乙方分成金额(元)")
    private BigDecimal partyBAmount;

    @ApiModelProperty("税差计算比例")
    private BigDecimal taxAdjustmentRate;

    @ApiModelProperty("税差金额(元)")
    private BigDecimal taxAdjustmentAmount;

    @ApiModelProperty("结算电表倍率")
    private BigDecimal meterMultiplier;

    @ApiModelProperty("审核人")
    private String reviewer;

    @ApiModelProperty("审核时间")
    private LocalDateTime reviewTime;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
