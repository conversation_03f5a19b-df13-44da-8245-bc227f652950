package com.bcels.cies.controller;

import com.bcels.cies.api.CiesDemandRecordApi;
import com.bcels.cies.domain.CiesDemandRecordDomainService;
import com.bcels.cies.request.CiesDemandListRequest;
import com.bcels.cies.request.CiesDemandRequest;
import com.bcels.cies.response.CiesDemandDistributeListResponse;
import com.bcels.cies.response.CiesDemandResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;

import java.util.List;


@RestController
@RequestMapping("/ciesDemandRecord/v1")
public class CiesDemandDistributeRecordController implements CiesDemandRecordApi {

@Autowired
private CiesDemandRecordDomainService ciesDemandRecordDomainService;


    @Override
    @PostMapping("query")
    public ResultData<CiesDemandDistributeListResponse> findDemandList(@RequestBody CiesDemandRequest request){
        return ResultData.success(ciesDemandRecordDomainService.findDemandList(request));
    }

    @Override
    @PostMapping("save")
    public ResultData<Void> saveDemandList(@RequestBody CiesDemandListRequest request){
        ciesDemandRecordDomainService.saveDemandList(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesDemandResponse>> findDemandRecordsForPage(@RequestBody CiesDemandRequest request){
        return ResultData.success(ciesDemandRecordDomainService.findDemandRecordsForPage(request));
    }

}
