package com.bcels.cies.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.repository.entity.CiesEssMonthlyBillEntity;
import com.bcels.cies.repository.entity.CiesOperationLogEntity;
import com.bcels.cies.request.CiesEssMonthlyBillRequest;
import com.bcels.cies.request.CiesOperationLogRequest;
import com.bcels.cies.response.CiesEssMonthlyBillResponse;
import com.bcels.cies.response.CiesOperationLogResponse;
import com.zwy.common.utils.bean.PageResponse;

import java.util.List;

/**
 * <p>
 * 设备信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface ICiesOperationLogService extends IService<CiesOperationLogEntity> {

    PageResponse<CiesOperationLogResponse> findOperationLog(CiesOperationLogRequest request);

    void saveOpeationLog(CiesOperationLogEntity ciesOperationLog);
}
