package com.bcels.cies.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.repository.entity.CiesIndicatorsInfoEntity;
import com.bcels.cies.repository.entity.CiesSpecialIndicatorsEntity;
import com.bcels.cies.request.CiesSpecialIndicatorsListRequest;
import com.bcels.cies.request.CiesSpecialIndicatorsUpdateRequest;
import com.bcels.cies.response.CiesSpecialIndicatorsResponse;
import com.zwy.common.utils.bean.PageResponse;

import java.util.List;

/**
 * <p>
 * 特殊指标 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface ICiesSpecialIndicatorsService extends IService<CiesSpecialIndicatorsEntity> {

    PageResponse<CiesSpecialIndicatorsResponse> findSpecialIndicatorsForPage(CiesSpecialIndicatorsListRequest request);

    void updateSpecialIndicator(CiesSpecialIndicatorsUpdateRequest request);

    CiesSpecialIndicatorsResponse findSpecialIndicatorById(String specialIndicatorsId);

    void deleteIndicatorsById(CiesSpecialIndicatorsUpdateRequest request);

    CiesSpecialIndicatorsResponse findSpecialIndicator(CiesSpecialIndicatorsUpdateRequest request);

    List<CiesSpecialIndicatorsResponse> findSpecialIndicatorList(List<String> indicatorName, String projectId);

    List<CiesSpecialIndicatorsResponse> findSpecialIndicators();

    void updateCurrentData(CiesSpecialIndicatorsEntity entity);

    void updateIndicatorName(CiesSpecialIndicatorsEntity entity);

    List<CiesSpecialIndicatorsResponse> findConnSpecialIndicators(String connPointId);
}
