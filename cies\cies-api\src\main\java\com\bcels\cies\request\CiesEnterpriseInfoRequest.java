package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "企业信息")
public class CiesEnterpriseInfoRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "企业主键")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "登记开始时间")
    private String startTime;

    @Schema(description = "登记结束时间")
    private String endTime;

    @Schema(description = "说明")
    private String enterpriseDes;

    @Schema(description = "企业下项目数量")
    private Integer projectNum;

    @Schema(description = "项目调用标识")
    private Boolean flag;
}
