<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zwy</groupId>
    <artifactId>common</artifactId>
    <packaging>pom</packaging>
    <version>1.1-SNAPSHOT</version>
    <modules>
<!--        <module>common-bean</module>-->
        <module>common-utils</module>
<!--        <module>common-bean-gateway</module>-->
<!--        <module>common-bean-role</module>-->
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <logback_version>1.1.2</logback_version>
        <slf4j.version>1.7.6</slf4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.bcels.cies</groupId>
                <artifactId>cies-dependencies</artifactId>
                <version>2.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
<!--            <dependency>-->
<!--                <artifactId>common-bean</artifactId>-->
<!--                <groupId>com.zwy</groupId>-->
<!--                <version>1.0-SNAPSHOT</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback_version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

<!--    <profiles>-->
<!--        <profile>-->
<!--            <id>dev</id>-->
<!--            <properties>-->
<!--                <env>dev</env>-->
<!--                <repositories.url>https://mvn.vpptech.cn/repository/test-releases</repositories.url>-->
<!--                <snapshot.repositories.url>https://mvn.vpptech.cn/repository/test-snapshots</snapshot.repositories.url>-->
<!--            </properties>-->
<!--        </profile>-->
<!--        <profile>-->
<!--            <id>test</id>-->
<!--            <properties>-->
<!--                <env>test</env>-->
<!--                <repositories.url>https://mvn.vpptech.cn/repository/test-releases</repositories.url>-->
<!--                <snapshot.repositories.url>https://mvn.vpptech.cn/repository/test-snapshots</snapshot.repositories.url>-->
<!--            </properties>-->
<!--        </profile>-->
<!--        <profile>-->
<!--            <id>prod</id>-->
<!--            <properties>-->
<!--                <env>prod</env>-->
<!--                <repositories.url>https://mvn.vpptech.cn/repository/prod-releases</repositories.url>-->
<!--                <snapshot.repositories.url>https://mvn.vpptech.cn/repository/prod-snapshots</snapshot.repositories.url>-->
<!--            </properties>-->
<!--        </profile>-->
<!--    </profiles>-->

<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>zwy-releases</id>-->
<!--            <name>maven-releases</name>-->
<!--            <url>${repositories.url}</url>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>zwy-snapshots</id>-->
<!--            <name>maven-snapshots</name>-->
<!--            <url>${snapshot.repositories.url}</url>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->
</project>