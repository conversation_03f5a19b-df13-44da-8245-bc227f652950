package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesConnectionPointEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.response.CiesConnectionPointResponse;

import java.util.List;

/**
 * <p>
 * 并网点信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface ICiesConnectionPointService extends IService<CiesConnectionPointEntity> {

    List<CiesConnectionPointResponse> findByChannelId(List<String> channelIds);

    List<CiesConnectionPointResponse> findConnByProjectIds(List<String> projectIds);

    List<CiesConnectionPointResponse> findByrHisRecordId(String hisRecordId);

    CiesConnectionPointResponse findConnByConnPointId(String connPointId);

    List<CiesConnectionPointResponse> findConnInfoByIds(List<String> connIds);


}
