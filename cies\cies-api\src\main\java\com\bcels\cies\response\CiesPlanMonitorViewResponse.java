package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@Schema(description = "场站监控展示")
public class CiesPlanMonitorViewResponse implements Serializable {

    @Schema(description = "时间集合")
    private List<String> time;

    @Schema(description = "计划功率集合")
    private List<BigDecimal> planPower;

    @Schema(description = "储能站有功功率集合")
    private List<BigDecimal> storageStation;

    @Schema(description = "工厂用电有功功率集合")
    private List<BigDecimal> factoryElecActivePower;

    @Schema(description = "工厂总有功功率集合")
    private List<BigDecimal> factoryElecTotalPower;

    @Schema(description = "储能站SOC集合")
    private List<BigDecimal> storageStationSOC;
}
