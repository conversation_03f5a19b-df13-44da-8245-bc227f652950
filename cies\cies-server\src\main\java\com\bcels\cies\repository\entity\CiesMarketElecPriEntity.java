package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 市场分时电价
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Getter
@Setter
@TableName("cies_market_elec_pri")
@ApiModel(value = "CiesMarketElecPriEntity对象", description = "市场分时电价")
public class CiesMarketElecPriEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("电价主键")
    @TableId
    private String elecPriId;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("月份")
    private String month;

    @ApiModelProperty("尖峰电量电价")
    private BigDecimal peakPeriodPrice;

    @ApiModelProperty("高峰电量电价")
    private BigDecimal highPeriodPrice;

    @ApiModelProperty("平时电量电价")
    private BigDecimal flatPeriodPrice;

    @ApiModelProperty("低谷电量电价")
    private BigDecimal valleyPeriodPrice;

    @ApiModelProperty("深谷电量电价")
    private BigDecimal deepValleyPeriodPrice;

    @ApiModelProperty("需量电价(元/千瓦·月)")
    private BigDecimal demandPrice;

    @ApiModelProperty("容量电价(元/千伏安·月)")
    private BigDecimal capacityPrice;

    @ApiModelProperty("项目id")
    private String projectId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
