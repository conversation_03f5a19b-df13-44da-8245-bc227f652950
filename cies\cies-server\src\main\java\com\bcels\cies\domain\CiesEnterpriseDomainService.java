package com.bcels.cies.domain;


import com.bcels.cies.emuns.ProjectTypeEnum;
import com.bcels.cies.repository.entity.CiesEnterpriseInfoEntity;
import com.bcels.cies.request.CiesEnterpriseInfoRequest;
import com.bcels.cies.response.CiesEnterpriseInfoResponse;
import com.bcels.cies.service.ICiesEnterpriseInfoService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.bean.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class CiesEnterpriseDomainService {

    @Autowired
    private ICiesEnterpriseInfoService iCiesEnterpriseInfoService;

    public PageResponse<CiesEnterpriseInfoResponse> findEnterpriseForPage(CiesEnterpriseInfoRequest request) {
        return iCiesEnterpriseInfoService.findEnterpriseForPage(request);
    }


    public void updateEnterpriseInfo(CiesEnterpriseInfoRequest request){
        iCiesEnterpriseInfoService.updateEnterpriseInfo(request);
    }

    public void addEnterpriseInfo(CiesEnterpriseInfoRequest request){
        iCiesEnterpriseInfoService.addEnterpriseInfo(request);
    }

    public List<CiesEnterpriseInfoResponse> findEnterprise(String enterpriseName) {
       return iCiesEnterpriseInfoService.findEnterprise(enterpriseName);
    }

    public List<CiesEnterpriseInfoResponse> findEnergyEnterprise(String enterpriseName) {
        return iCiesEnterpriseInfoService.findEnergyEnterprise(ProjectTypeEnum.ENERGY_STORAGE.getCode(),enterpriseName);
    }

    public CiesEnterpriseInfoResponse findEnterpriseById(String enterpriseId) {
        CiesEnterpriseInfoEntity entity = iCiesEnterpriseInfoService.getById(enterpriseId);
        return BeanCopyUtil.copyProperties(entity, CiesEnterpriseInfoResponse::new);
    }
}
