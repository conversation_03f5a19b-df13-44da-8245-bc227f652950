package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "深谷数据")
public class CiesDeepResponse implements Serializable {

    @Schema(description = "深谷日期")
    private String deepDate;

    @Schema(description = "深谷时间段")
    private String deepTime;

    @Schema(description = "深谷表头")
    private String title;

    @Schema(description = "深谷数据行")
    private String data;

    @Schema(description = "深谷配置主键")
    private String configId;

    @Schema(description = "深谷明细主键")
    private String configDetailId;
}
