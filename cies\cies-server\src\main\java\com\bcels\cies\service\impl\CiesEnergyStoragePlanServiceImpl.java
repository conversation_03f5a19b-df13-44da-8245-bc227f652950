package com.bcels.cies.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bcels.cies.repository.entity.CiesEnergyStoragePlanEntity;
import com.bcels.cies.repository.mapper.CiesEnergyStoragePlanMapper;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.response.CiesEnergyStoragePlanResponse;
import com.bcels.cies.service.ICiesEnergyStoragePlanService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;

/**
 * <p>
 * 储能计划表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class CiesEnergyStoragePlanServiceImpl extends ServiceImpl<CiesEnergyStoragePlanMapper, CiesEnergyStoragePlanEntity> implements ICiesEnergyStoragePlanService {


    @Autowired
    private CiesEnergyStoragePlanMapper ciesEnergyStoragePlanMapper;

    /**
     * 查询全场站计划
     * @param request
     * @return
     */
    @Override
    public List<CiesEnergyStoragePlanResponse> findStationEnergyPlan(CiesEnergyPlanListRequest request) {
        QueryWrapper<CiesEnergyStoragePlanEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(!StringUtils.isEmpty(request.getProjectId()), "project_id", request.getProjectId());
        wrapper.eq(!StringUtils.isEmpty(request.getPlanDate()), "plan_date", request.getPlanDate());
        wrapper.eq(!StringUtils.isEmpty(request.getHistoryRecordId()), "history_record_id", request.getHistoryRecordId());
        wrapper.eq(!StringUtils.isEmpty(request.getConnectionPointId()), "connection_point_id", request.getConnectionPointId());
        List<CiesEnergyStoragePlanEntity> list = list(wrapper);
        List<CiesEnergyStoragePlanEntity> sortPlans = list.stream().sorted(Comparator.comparing(obj -> LocalTime.parse(obj.getStartTime()))).toList();
        return BeanCopyUtil.copyListProperties(sortPlans,CiesEnergyStoragePlanResponse::new);
    }

    /**
     * 查询项目下所有的并网点计划
     * @param request
     * @return
     */
    @Override
    public List<CiesEnergyStoragePlanResponse> findAllConnPointPlan(CiesEnergyPlanListRequest request) {
        List<CiesEnergyStoragePlanEntity> entities = ciesEnergyStoragePlanMapper.findAllConnPointPlan(request);
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }
        return BeanCopyUtil.copyListProperties(entities, CiesEnergyStoragePlanResponse::new);
    }

    @Override
    public List<CiesEnergyStoragePlanResponse> findConnPlanByConnIds(List<String> connIds, String planDate) {

        QueryWrapper<CiesEnergyStoragePlanEntity> wrapper = new QueryWrapper<>();
        wrapper.in(!CollectionUtils.isEmpty(connIds), "connection_point_id", connIds);
        wrapper.isNull("project_id");
        wrapper.isNull("history_record_id");
        wrapper.eq(!StringUtils.isEmpty(planDate), "plan_date",planDate);
        List<CiesEnergyStoragePlanEntity> list = list(wrapper);
        List<CiesEnergyStoragePlanEntity> sortPlans = list.stream().sorted(Comparator.comparing(obj -> LocalTime.parse(obj.getStartTime()))).toList();
        return BeanCopyUtil.copyListProperties(sortPlans,CiesEnergyStoragePlanResponse::new);
    }

    @Override
    public List<CiesEnergyStoragePlanResponse> findConfirmedPlan(List<String> projectIdList, String date) {
        List<CiesEnergyStoragePlanEntity> confirmedPlan = ciesEnergyStoragePlanMapper.findConfirmedPlan(projectIdList, date);
        if (CollectionUtils.isEmpty(confirmedPlan)) {
            return null;
        }
        return BeanCopyUtil.copyListProperties(confirmedPlan, CiesEnergyStoragePlanResponse::new);
    }

    /**
     * 批量删除计划
     * @param request
     */
    @Override
    public void batchDelete(CiesEnergyPlanListRequest request) {
        QueryWrapper<CiesEnergyStoragePlanEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(!StringUtils.isEmpty(request.getProjectId()),"project_id",request.getProjectId());
        wrapper.in(!CollectionUtils.isEmpty(request.getPlanDateList()),"plan_date",request.getPlanDateList());
        wrapper.eq(!StringUtils.isEmpty(request.getConnectionPointId()),"connection_point_id",request.getConnectionPointId());
        remove(wrapper);
    }
}
