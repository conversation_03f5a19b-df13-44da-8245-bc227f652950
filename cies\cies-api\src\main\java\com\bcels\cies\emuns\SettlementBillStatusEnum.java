package com.bcels.cies.emuns;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结算单状态
 */
@Getter
@AllArgsConstructor
public enum SettlementBillStatusEnum {
    PENDING_DISPATCH("PENDING_DISPATCH", "待下发"),
    PENDING_REVIEW("PENDING_REVIEW", "待审核"),
    APPEALED("APPEALED", "已申诉"),
    CONFIRMED("CONFIRMED", "已确认"),
    ISSUED("ISSUED", "已开具"),
    REVIEW_REJECTED("REVIEW_REJECTED", "审核驳回"),
            ;
    private final String code;
    private final String desc;

    private static final Map<String, String> CODE_TO_DESC = Arrays.stream(values())
            .collect(Collectors.toMap(SettlementBillStatusEnum::getCode,  SettlementBillStatusEnum::getDesc));

    private static final Map<String, String> DESC_TO_CODE = Arrays.stream(values())
            .collect(Collectors.toMap(
                    SettlementBillStatusEnum::getDesc,
                    SettlementBillStatusEnum::getCode,
                    (oldVal, newVal) -> oldVal
            ));

    // 根据code获取desc
    public static String getDescByCode(String code) {
        return CODE_TO_DESC.getOrDefault(code,  "未知类型");
    }

}
