package com.zwy.common.utils;


import com.zwy.common.utils.enums.ElePointTypeEnum;
import com.zwy.common.utils.exception.ParamException;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

public class ElePointUtil {


    public static LocalDateTime findE96PointTime(int point, LocalDate pointDate) {
        return findPointTime(ElePointTypeEnum.E96, point, pointDate);
    }

    public static LocalDateTime findPointTime(ElePointTypeEnum pointType, int point, LocalDate pointDate) {
        if (pointDate == null || pointType == null) {
            throw new ParamException();
        }
        return pointDate.atStartOfDay().plusMinutes((long) (point - 1) * pointType.getMinuteInterval());
    }

    public static LocalTime findPointTime(ElePointTypeEnum pointType, int point) {
        if (pointType == null) {
            throw new ParamException();
        }
        if (point == 1 || point == pointType.getPoint()) {
            return LocalTime.of(0, 0, 0);
        }
        return LocalTime.ofSecondOfDay(point * pointType.getMinuteInterval() * 60L);
    }

    public static LocalTime findE96PointTime(int point) {
        return findPointTime(ElePointTypeEnum.E96, point);
    }

    public static LocalTime findE96NormalPointTime(int point) {
        return findNormalPointTime(ElePointTypeEnum.E96, point);
    }

    private static LocalTime findNormalPointTime(ElePointTypeEnum pointType, int point) {
        if (pointType == null) {
            throw new ParamException();
        }
        if (point == pointType.getPoint()) {
            return LocalTime.of(0, 0, 0);
        }
        return LocalTime.ofSecondOfDay(point * pointType.getMinuteInterval() * 60L);
    }

    public static LocalDateTime findE96MarketPointTime(int point, LocalDate pointDate) {
        return findMarketPointTime(ElePointTypeEnum.E96, point, pointDate);
    }

    public static LocalDateTime findMarketPointTime(ElePointTypeEnum pointType, int point, LocalDate pointDate) {
        if (pointDate == null || pointType == null) {
            throw new ParamException();
        }
        if (point == pointType.getPoint()) {
            pointDate = pointDate.minusDays(1);
        }
        return pointDate.atStartOfDay().plusMinutes((long) point * pointType.getMinuteInterval());
    }

    public static Integer findE96Point(LocalDateTime pointTime) {
        return findPoint(ElePointTypeEnum.E96, pointTime);
    }


    public static Integer findPoint(ElePointTypeEnum pointTypeEnum, LocalDateTime pointTime) {
        int hour = pointTime.getHour();
        int minute = pointTime.getMinute();
        int point = (hour * 60 + minute) / pointTypeEnum.getMinuteInterval();
        return point + 1;
    }

    public static Integer findPoint(int pointIntervalSecond, LocalDateTime pointTime) {
        int hour = pointTime.getHour();
        int minute = pointTime.getMinute();
        int second = pointTime.getSecond();
        int point = (hour * 3600 + minute * 60 + second) / pointIntervalSecond;
        return point + 1;
    }


    public static Integer findE96Point(LocalTime pointTime) {
        return findPoint(ElePointTypeEnum.E96, pointTime);
    }

    public static Integer findPoint(ElePointTypeEnum pointTypeEnum, LocalTime pointTime) {
        int hour = pointTime.getHour();
        int minute = pointTime.getMinute();
        int point = (hour * 60 + minute) / pointTypeEnum.getMinuteInterval();
        return point + 1;
    }

    public static LocalDate getPointTimeInDate(LocalDateTime pointTime) {
        if (Objects.isNull(pointTime)) {
            return LocalDate.now().atStartOfDay().toLocalDate();
        }
        return pointTime.minusMinutes(ElePointTypeEnum.E96.getMinuteInterval()).toLocalDate().atStartOfDay().toLocalDate();
    }

    /**
     * 点位对应的时间点(00:15为第一个点)
     *
     * @param point
     * @return
     */
    public static String getPointTimeByPoint(int point) {
        return DateTimeFormatter.ofPattern("HH:mm").format(findMarketPointTime(ElePointTypeEnum.E96, point, LocalDate.now()));
    }

    /**
     * 点位对应的时间点(00:00为第一个点)
     *
     * @param point
     * @return
     */
    public static String getPointTimeByZeroPoint(int point) {
        point = point - 1;
        return DateTimeFormatter.ofPattern("HH:mm").format(findMarketPointTime(ElePointTypeEnum.E96, point, LocalDate.now()));
    }

    public static LocalDateTime findE288PointTime(int point, LocalDate date) {
        return findPointTime(ElePointTypeEnum.E288, point,date);
    }
    public static Integer findE288Point(LocalDateTime pointTime) {
        return findPoint(ElePointTypeEnum.E288, pointTime);
    }

    public static Integer findE1440Point(LocalDateTime pointTime) {
        return findPoint(ElePointTypeEnum.E1440, pointTime);
    }

    public static LocalDateTime findE1440PointTime(int point, LocalDate date) {
        return findPointTime(ElePointTypeEnum.E1440, point,date);
    }
}
