package com.bcels.cies.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.repository.entity.CiesMarketElecPriEntity;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesMarketElecPriRecordRequest;
import com.bcels.cies.response.CiesMarketElecPriResponse;

import java.util.List;

/**
 * <p>
 * 市场分时电价 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface ICiesMarketElecPriService extends IService<CiesMarketElecPriEntity> {

    CiesMarketElecPriEntity findElecPriInfo(String month,String projectId);

    List<CiesMarketElecPriResponse> findElecPriInfoByYear(CiesMarketConfigRequest request);

    void deleteElecPri(CiesMarketElecPriRecordRequest request);

}
