package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.repository.entity.CiesIndicatorsInfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesIndicatorsInfoListRequest;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 测点与指标信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Mapper
public interface CiesIndicatorsInfoMapper extends BaseMapper<CiesIndicatorsInfoEntity> {


    IPage<CiesIndicatorsInfoResponse> findProjectInfoForPage(@Param("page") Page<CiesIndicatorsInfoResponse> page, @Param("request") CiesIndicatorsInfoListRequest reques);


    CiesIndicatorsInfoResponse  findIndicatorById(String indicatorById);
}
