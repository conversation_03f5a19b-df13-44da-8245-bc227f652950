package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "测点与指标")
public class CiesIndicatorsInfoResponse implements Serializable {

    @Schema(description = "指标主键")
    private String indicatorId;

    @Schema(description = "企业")
    private String enterpriseName;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "关联设备名称")
    private String relatedEquipName;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "数据名称")
    private String dataName;

    @Schema(description = "页面展示名称")
    private String pageDisplayName;

    @Schema(description = "分类内排序")
    private Integer sort;

    @Schema(description = "是否显示(0:不显示 1:显示)")
    private Integer isShow;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "最新数据")
    private BigDecimal currentValue;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "设备主键")
    private String equipId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;
}
