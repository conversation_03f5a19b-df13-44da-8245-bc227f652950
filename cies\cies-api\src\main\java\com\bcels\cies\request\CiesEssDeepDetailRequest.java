package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "深谷信息")
public class CiesEssDeepDetailRequest extends PageRequest implements Serializable {

    @Schema(description = "储能结算主键")
    private String essBillId;

    @Schema(description = "深谷日期")
    private String deepDate;

    @Schema(description = "深谷时间段")
    private String deepTime;

    @Schema(description = "深谷数据")
    private String deepDetail;

    @Schema(description = "深谷标题")
    private String deepTitle;

}
