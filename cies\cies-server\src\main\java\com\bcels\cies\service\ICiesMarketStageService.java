package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesMarketStageEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesMarketElecPriRequest;
import com.bcels.cies.request.CiesMarketStageRecordRequest;
import com.bcels.cies.request.CiesMarketStageRequest;
import com.bcels.cies.response.CiesMarketElecPriResponse;
import com.bcels.cies.response.CiesMarketStageResponse;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 市场分时阶段 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface ICiesMarketStageService extends IService<CiesMarketStageEntity> {

    List<CiesMarketStageEntity> findMarketStageList(String month, String projectId);

    List<CiesMarketStageResponse> findStageInfoByYear(CiesMarketConfigRequest request);

    void deleteStage(CiesMarketStageRecordRequest request);

}
