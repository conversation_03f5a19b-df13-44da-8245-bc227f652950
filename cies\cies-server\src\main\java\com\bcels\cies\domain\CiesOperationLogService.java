package com.bcels.cies.domain;


import com.bcels.cies.repository.entity.CiesOperationLogEntity;
import com.bcels.cies.request.CiesOperationLogRequest;
import com.bcels.cies.response.CiesOperationLogResponse;
import com.bcels.cies.service.ICiesOperationLogService;
import com.zwy.common.utils.bean.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class CiesOperationLogService {

    @Autowired
    private ICiesOperationLogService iCiesOperationLogService;


    public PageResponse<CiesOperationLogResponse> findOperationLog(CiesOperationLogRequest request) {
        return iCiesOperationLogService.findOperationLog(request);
    }

    public void saveOpeationLog(CiesOperationLogEntity ciesOperationLog) {
        iCiesOperationLogService.saveOpeationLog(ciesOperationLog);
    }

}
