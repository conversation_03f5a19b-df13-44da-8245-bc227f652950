<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.bcels.cies</groupId>
	<artifactId>cies</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>cies</name>
	<description></description>
	<modules>
		<module>cies-api</module>
		<module>cies-server</module>
	</modules>

	<properties>
		<java.version>17</java.version>
		<java.encoding>UTF-8</java.encoding>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.bcels.cies</groupId>
				<artifactId>cies-dependencies</artifactId>
				<version>2.0.0-SNAPSHOT</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>8</source>
					<target>8</target>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<env>dev</env>
				<nacos.server-addr>http://**************:8848</nacos.server-addr>
				<nacos.username>nacos</nacos.username>
				<nacos.password>nacos1207</nacos.password>
				<nacos.namespace>50a5e917-ec69-42a7-b7a7-ee390e6e2827</nacos.namespace>
			</properties>
		</profile>
		<profile>
			<id>uat</id>
			<properties>
				<env>uat</env>
				<nacos.server-addr>nacossvc.default:8848</nacos.server-addr>
				<nacos.namespace>bcels-cies</nacos.namespace>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<env>prod</env>
				<nacos.server-addr>mse-00839952-nacos-ans.mse.aliyuncs.com:8848</nacos.server-addr>
				<nacos.namespace>bcels-cies</nacos.namespace>
			</properties>
		</profile>
	</profiles>

</project>
