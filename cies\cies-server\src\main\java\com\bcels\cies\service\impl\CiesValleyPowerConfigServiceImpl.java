package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bcels.cies.repository.entity.CiesValleyPowerConfigEntity;
import com.bcels.cies.repository.mapper.CiesValleyPowerConfigMapper;
import com.bcels.cies.response.CiesValleyPowerConfigResponse;
import com.bcels.cies.service.ICiesValleyPowerConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <p>
 * 深谷电量配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-26
 */
@Service
public class CiesValleyPowerConfigServiceImpl extends ServiceImpl<CiesValleyPowerConfigMapper, CiesValleyPowerConfigEntity> implements ICiesValleyPowerConfigService {
    @Override
    public CiesValleyPowerConfigResponse queryValleyPowerConfig(String essBillId) {

        QueryWrapper<CiesValleyPowerConfigEntity>  queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ess_bill_id",essBillId);
        CiesValleyPowerConfigEntity one = getOne(queryWrapper);
        if (ObjectUtils.isEmpty(one)){
            return null;
        }
        return BeanCopyUtil.copyProperties(one,CiesValleyPowerConfigResponse::new);
    }
}
