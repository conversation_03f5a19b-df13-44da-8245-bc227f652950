package com.bcels.cies.api;

import com.alibaba.fastjson.JSONArray;
import com.bcels.cies.request.CiesConnectionPointRequest;
import com.bcels.cies.request.CiesEsPlanRuleRequest;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.response.CiesConnectionPointResponse;
import com.bcels.cies.response.CiesEsPlanRuleResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import com.bcels.cies.response.CiesStatisticsProjectResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "2、项目管理API")
@FeignClient(name = "projectInfo", path = "/projectInfo/v1")
public interface CiesProjectInfoApi {

    @Operation(summary = "项目列表")
    @PostMapping("page")
    ResultData<PageResponse<CiesProjectInfoResponse>> findForPage(@RequestBody CiesProjectInfoRequest request);

    @Operation(summary = "储能计划-项目列表")
    @PostMapping("findMarKetForPage")
    ResultData<PageResponse<CiesProjectInfoResponse>> findMarKetForPage(@RequestBody CiesProjectInfoRequest request);

    @Operation(summary = "查看详情")
    @GetMapping("findById")
    @Parameters({
            @Parameter(name = "projectId", description = "项目Id", required = true)
    })
    ResultData<CiesProjectInfoResponse> getProjectInfById(@RequestParam("projectId") String projectId);

    @Operation(summary = "项目信息更新")
    @PostMapping("update")
    ResultData<Void> updateProjectInfo(@RequestBody CiesProjectInfoRequest request);

    @Operation(summary = "根据项目ID查询下发规则")
    @GetMapping("findRuleByProjectId")
    @Parameters({
            @Parameter(name = "projectId", description = "项目Id", required = true)
    })
    ResultData<CiesEsPlanRuleResponse> findEsPlanRuleByProjectId(@RequestParam("projectId") String projectId);

    @Operation(summary = "并网点更新")
    @PostMapping("updateConnPoint")
    ResultData<Void> updateConnPoint(@RequestBody CiesConnectionPointRequest request);

    @Operation(summary = "下发规则更新")
    @PostMapping("updateRule")
    ResultData<Void> updateRule(@RequestBody CiesEsPlanRuleRequest request);

    @Operation(summary = "查询企业下项目")
    @PostMapping("query")
    ResultData<List<CiesProjectInfoResponse>> findProjectByEnterpriseId(@RequestBody CiesProjectInfoRequest request);

    @Operation(summary = "查询企业下储能项目")
    @PostMapping("queryEnergyProject")
    ResultData<List<CiesProjectInfoResponse>> findEnergyProjectByEnterpriseId(@RequestBody CiesProjectInfoRequest request);


    @Operation(summary = "根据并网点ID查询并网点信息")
    @GetMapping("findConnInfoById")
    @Parameters({
            @Parameter(name = "connectionPointId", description = "并网点ID", required = true)
    })
    ResultData<CiesConnectionPointResponse> findConnInfoById(@RequestParam("connectionPointId") String connectionPointId);

    @Operation(summary = "查询所有市场省市")
    @GetMapping("findArea")
    ResultData<List<String>> findArea();


    @Operation(summary = "获取用电类型电压等级")
    @GetMapping("getElectricityType")
    @Parameters({
            @Parameter(name = "regionName", description = "区域名称", required = true)
    })
    ResultData<JSONArray> getElectricityType(@RequestParam("regionName") String regionName);


    @Operation(summary = "工商业储能统计（首页）")
    @GetMapping("statisticsProject")
    ResultData<CiesStatisticsProjectResponse> statisticsProject();
}
