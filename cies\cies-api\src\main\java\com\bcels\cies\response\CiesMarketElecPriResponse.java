package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "用户代购电-电价列表")
public class CiesMarketElecPriResponse implements Serializable {

    @Schema(description = "电价主键")
    private String elecPriId;

    @Schema(description = "月份")
    private String month;

    @Schema(description = "尖峰电量电价")
    private BigDecimal peakPeriodPrice;

    @Schema(description = "高峰电量电价")
    private BigDecimal highPeriodPrice;

    @Schema(description = "平时电量电价")
    private BigDecimal flatPeriodPrice;

    @Schema(description = "低谷电量电价")
    private BigDecimal valleyPeriodPrice;

    @Schema(description = "深谷电量电价")
    private BigDecimal deepValleyPeriodPrice;

    @Schema(description = "需量电价(元/千瓦·月)")
    private BigDecimal demandPrice;

    @Schema(description = "容量电价(元/千伏安·月)")
    private BigDecimal capacityPrice;

    @Schema(description = "项目id")
    private String projectId;
}
