package com.bcels.cies.client;

import com.alibaba.fastjson.JSONObject;
import com.bcels.cies.request.CiesQueryIndicatorsRequest;
import com.bcels.cies.response.CiesHisIndicatorsDataResponse;
import com.bcels.cies.response.CiesLatestIndicatorsDataResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(name = "data-platform", url = "${api.data-platform.path}")
public interface CiesInternalInterfaceClient{

    @Operation(summary = "批量获取指标历史数据")
    @PostMapping("batchQueryHisData")
    Map<String,List<CiesHisIndicatorsDataResponse>> batchQueryHisData(@RequestBody CiesQueryIndicatorsRequest request);

    @Operation(summary = "批量获取指标实时数据")
    @PostMapping("batchQueryData")
    Map<String,List<CiesHisIndicatorsDataResponse>> batchQueryData(@RequestBody CiesQueryIndicatorsRequest request);

    @Operation(summary = "下发储能计划")
    @PostMapping("distributePlan")
    void distributePlan(@RequestBody JSONObject jsonObject);

    @Operation(summary = "下发需量记录")
    @PostMapping("distributeDemand")
    void distributeDemand(@RequestBody JSONObject jsonObject);
}
