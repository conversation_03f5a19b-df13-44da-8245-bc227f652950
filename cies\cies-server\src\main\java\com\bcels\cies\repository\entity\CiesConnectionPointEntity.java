package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 并网点信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("cies_connection_point")
@ApiModel(value = "CiesConnectionPointEntity对象", description = "并网点信息")
public class CiesConnectionPointEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("并网点主键")
    @TableId
    private String connectionPointId;

    @ApiModelProperty("并网点名称")
    private String connectionPointName;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("通道ID")
    private String channelId;

    @ApiModelProperty("排序")
    private Integer sortOrder;

    @ApiModelProperty("关联储能柜数量")
    private Integer num;

    @ApiModelProperty("容量占比")
    private BigDecimal capacityRatio;

    @ApiModelProperty("计划起始设点ID")
    private Integer planStartId;

    @ApiModelProperty("需量设点ID")
    private Integer demandId;

    @ApiModelProperty("历史记录ID")
    private String historyRecordId;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;

    @ApiModelProperty("通道是否为并网点 0否 1是")
    private Integer isShow;
}
