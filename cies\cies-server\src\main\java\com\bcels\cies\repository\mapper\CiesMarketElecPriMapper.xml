<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesMarketElecPriMapper">

    <select id="findElecPriInfoByYear" resultType="com.bcels.cies.repository.entity.CiesMarketElecPriEntity">
        select *
        from cies_market_elec_pri
        where project_id = #{projectId} and year = #{year}
        ORDER BY CAST(REPLACE(month, '月', '') AS UNSIGNED)
    </select>
</mapper>
