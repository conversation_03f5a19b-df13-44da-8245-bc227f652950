package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "特殊指标")
public class CiesSpecialIndicatorsListRequest extends PageRequest implements Serializable {


    @Schema(description = "特殊指标关联主键")
    private String specialIndicatorsId;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "数据类型")
    private String relateDataType;

    @Schema(description = "数据名称")
    private String dataName;

    @Schema(description = "业务指标名称")
    private String indicatorName;
}
