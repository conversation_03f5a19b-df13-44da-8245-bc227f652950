package com.bcels.cies.controller;

import com.bcels.cies.api.CiesIndicatorsInfoApi;
import com.bcels.cies.domain.CiesIndicatorsInfoDomainService;
import com.bcels.cies.request.CiesIndicatorHisRequest;
import com.bcels.cies.request.CiesIndicatorsInfoListRequest;
import com.bcels.cies.request.CiesIndicatorsInfoUpdateRequest;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ciesIndicators/v1")
public class CiesIndicatorsInfoController implements CiesIndicatorsInfoApi {


    @Autowired
    private CiesIndicatorsInfoDomainService ciesIndicatorsInfoDomainService;
    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesIndicatorsInfoResponse>> findForPage(@RequestBody CiesIndicatorsInfoListRequest request) {
        return ResultData.success(ciesIndicatorsInfoDomainService.findIndicatorsForPage(request));
    }

    @Override
    @PostMapping("update")
    public ResultData<Void> updateIndicator(@RequestBody CiesIndicatorsInfoUpdateRequest request){
        ciesIndicatorsInfoDomainService.updateIndicator(request);
        return ResultData.success();
    }

    @Override
    @GetMapping("findById")
    public ResultData<CiesIndicatorsInfoResponse> findIndicatorById(@RequestParam("indicatorId") String indicatorId) {
        return ResultData.success(ciesIndicatorsInfoDomainService.findIndicatorById(indicatorId));
    }

    @Override
    @GetMapping("findIndicators")
    public ResultData<List<CiesIndicatorsInfoResponse>> findIndicators(@RequestParam("projectId") String projectId) {
        return ResultData.success(ciesIndicatorsInfoDomainService.findIndicators(projectId));
    }
}
