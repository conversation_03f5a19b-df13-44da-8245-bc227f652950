spring:
  application:
    name: bcels-cies
  profiles:
    active: @env@
  cloud:
    nacos:
      discovery:
        server-addr: @nacos.server-addr@
#        username: @nacos.username@
#        password: @nacos.password@
        namespace: @nacos.namespace@
      config:
        server-addr: @nacos.server-addr@
#        username: @nacos.username@
#        password: @nacos.password@
        namespace: @nacos.namespace@
        # 配置文件格式
        file-extension: yml
        group: DEFAULT_GROUP
        shared-configs:
          - ${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        refresh-enabled: true
  data:
    redis:
      host: ${spring.data.redis.host}
      port: ${spring.data.redis.port}
      password: ${spring.data.redis.password}
      database: ${spring.data.redis.database}
      timeout: 6000  # 连接超时时长（毫秒）
      lettuce:
        pool:
          max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
          max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-idle: 10      # 连接池中的最大空闲连接
          min-idle: 5       # 连接池中的最小空闲连接
