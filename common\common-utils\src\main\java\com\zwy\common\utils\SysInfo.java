package com.zwy.common.utils;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryUsage;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.Optional;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.function.Predicate;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

public class SysInfo {
	private SysInfo() {
	}

	public static final MemoryUsage loadHeap() {
		return ManagementFactory.getMemoryMXBean().getHeapMemoryUsage();
	}

	public static final int loadAvailableCore() {
		return ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors();
	}

	public static final double loadSystemLoad() {
		return ManagementFactory.getOperatingSystemMXBean().getSystemLoadAverage();
	}

	public static final long loadJVMPid() {
		String processName = ManagementFactory.getRuntimeMXBean().getName();
		String processID = processName.substring(0, processName.indexOf('@'));
		return Long.parseLong(processID);
	}

	private static class EnumerationIterator<T> implements Iterator<T> {
		private Enumeration<T> enumeration;

		private EnumerationIterator(Enumeration<T> enumeration) {
			this.enumeration = enumeration;
		}

		@Override
		public boolean hasNext() {
			return enumeration.hasMoreElements();
		}

		@Override
		public T next() {
			return enumeration.nextElement();
		}
	}

	private static <T> Stream<T> buildStream(Enumeration<T> enumeration) {
		return StreamSupport.stream(
				Spliterators.spliteratorUnknownSize(new EnumerationIterator<T>(enumeration), Spliterator.IMMUTABLE),
				false);
	}

	private static final String loadIP(Predicate<InetAddress> predicate) {
		try {
			return buildStream(NetworkInterface.getNetworkInterfaces()).filter((NetworkInterface ni) -> {
				try {
					return !ni.isVirtual() && ni.isUp();
				} catch (SocketException e) {
					return false;
				}
			}).map(NetworkInterface::getInetAddresses).map(SysInfo::buildStream)
					.map(addressStream -> addressStream.filter(predicate).findAny()).filter(Optional::isPresent)
					.map(Optional::get).map(InetAddress::getHostAddress).findAny().orElse(null);
		} catch (SocketException e) {
			return null;
		}
	}

	public static final String loadLocalIPV4() {
		return loadIP((InetAddress address) -> address instanceof Inet4Address && address.isSiteLocalAddress());
	}

	public static final String loadNetIPV4() {
		return loadIP((InetAddress address) -> address instanceof Inet4Address && !address.isSiteLocalAddress()
				&& !address.isLoopbackAddress());
	}
}
