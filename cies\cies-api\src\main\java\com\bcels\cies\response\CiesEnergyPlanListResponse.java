package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "储能计划管理-列表")
public class CiesEnergyPlanListResponse implements Serializable {


    @Schema(description = "企业")
    private String enterpriseName;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "储能计划下发审核主键")
    private String dispatchAuditId;

    @Schema(description = "计划日期")
    private String planDate;

    @Schema(description = "计划充电时间段")
    private String plannedChargingTime;

    @Schema(description = "计划放电时间段")
    private String plannedDischargingTime;

    @Schema(description = "计划状态")
    private String auditStatus;

    @Schema(description = "下发状态")
    private String dispatchStatus;

    @Schema(description = "本日充电电量(kwh)")
    private BigDecimal dailyChargeEnergy;

    @Schema(description = "本日放电电量(kwh)")
    private BigDecimal dailyDischargeEnergy;

    @Schema(description = "本日充电成本（元）")
    private BigDecimal dailyChargeCost;

    @Schema(description = "本日放电收入（元）")
    private BigDecimal dailyDischargeIncome;

    @Schema(description = "本日充放电利润（元）")
    private BigDecimal dailyProfit;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "是否调整")
    private Integer isAdjusted;

}
