package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.repository.entity.CiesPeakValleyPriceGapEntity;
import com.bcels.cies.request.CiesPeakValleyPriceGapRequest;
import com.bcels.cies.response.CiesPeakValleyPriceGapResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 储能峰谷价差信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Mapper
public interface CiesPeakValleyPriceGapMapper extends BaseMapper<CiesPeakValleyPriceGapEntity> {

    List<CiesPeakValleyPriceGapResponse> queryPriceGap(@Param("request") CiesPeakValleyPriceGapRequest request);

}

