<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesValleyConfigDetailsMapper">

    <select id="queryDeepInfo" resultType="com.bcels.cies.response.CiesDeepResponse">
        select config.config_id        as configId,
               config.date             as deepDate,
               config.time_range_name  as deepTime,
               detail.config_detail_id as configDetailId,
               detail.deep_data        as data,
               detail2.deep_data       as title
        from cies_valley_power_config config
                 left join cies_valley_config_details detail
                           on detail.config_id = config.config_id and detail.data_type = 'DEEP_DATA'
                 left join cies_valley_config_details detail2
                           on detail2.config_id = config.config_id and detail2.data_type = 'DEEP_TITLE'
        where cies_valley_power_config.config.ess_bill_id = #{essBillId}
    </select>
    <select id="queryDeepInfoByDataType" resultType="com.bcels.cies.response.CiesDeepResponse">
        select config.config_id        as configId,
               config.date             as deepDate,
               config.time_range_name  as deepTime,
               detail.config_detail_id as configDetailId,
               detail.deep_data        as data,
               detail2.deep_data       as title
        from cies_valley_power_config config
                 left join cies_valley_config_details detail
                           on detail.config_id = config.config_id and detail.data_type = #{dataType}
        where cies_valley_power_config.config.ess_bill_id = #{essBillId}
    </select>
</mapper>
