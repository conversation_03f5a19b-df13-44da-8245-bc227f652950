package com.bcels.cies.controller;

import com.bcels.cies.api.CiesExternalInterfaceApi;
import com.bcels.cies.domain.CiesDataMiddlePlatformService;
import com.bcels.cies.request.CiesSynDeleteRequest;
import com.bcels.cies.request.CiesSynEquipRequest;
import com.bcels.cies.request.CiesSynIndicatorRequest;
import com.bcels.cies.request.CiesSynProjectRequest;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/tranData/v1")
public class CiesDataMiddlePlatformController implements CiesExternalInterfaceApi {

    @Autowired
    private CiesDataMiddlePlatformService ciesDataMiddlePlatformService;

    @Override
    @PostMapping("synProject")
    public ResultData<Void> synProjectInfo(@RequestBody CiesSynProjectRequest request) {
        ciesDataMiddlePlatformService.synProjectInfo(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("synEquipInfo")
    public ResultData<Void> synEquipInfo(@RequestBody CiesSynEquipRequest request) {
        ciesDataMiddlePlatformService.synEquipInfo(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("synIndicator")
    public ResultData<Void> synIndicatorInfo(@RequestBody CiesSynIndicatorRequest request) {
        ciesDataMiddlePlatformService.synIndicatorInfo(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("synDeleteRecord")
    public ResultData<Void> synDeleteRecord(@RequestBody CiesSynDeleteRequest request) {
        ciesDataMiddlePlatformService.synDeleteRecord(request);
        return ResultData.success();
    }
}
