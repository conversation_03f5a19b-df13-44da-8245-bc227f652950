package com.zwy.common.utils;

public class BinaryUtil {

    // 生成指定位数的二进制字符串
    public static String generateBinaryString(int length) {
        StringBuilder binaryString = new StringBuilder();
        for (int i = 0; i < length; i++) {
            binaryString.append(i >> 7 & 1);
        }
        return binaryString.toString();
    }

    // 将传入二进制字符串，指定位数改为1
    public static String generateBinaryString(String binaryStr, int num) {
        StringBuilder stringBuilder = new StringBuilder(binaryStr);
        stringBuilder.replace(num -1 , num, "1");
        return stringBuilder.toString();
    }

    // 生成指定位数的二进制字符串，指定位数为1
    public static String generateBinaryString(int length, int num) {
        StringBuilder binaryString = new StringBuilder();
        for (int i = 0; i < length; i++) {
            if (i == num - 1) {
                binaryString.append(1);
            } else {
                binaryString.append(i >> 7 & 1);
            }
        }
        return binaryString.toString();
    }

    // 将传入的long类型数值，转换成指定长度的二进制字符串
    public static String corverToBinaryString(Long value, Integer bitCount) {
        String binaryString = Long.toBinaryString(value);
        while (binaryString.length() < bitCount) {
            binaryString = "0" + binaryString;
        }
        return binaryString;
    }
}
