package com.bcels.cies.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CiesPlanMonitorListExport implements Serializable {

    @ApiModelProperty("时间")
    private String time;

    @ApiModelProperty("计划功率 ")
    private BigDecimal planPower;

    @ApiModelProperty("储能站有功功率")
    private BigDecimal storageStation;

    @ApiModelProperty("工厂用电有功功率")
    private BigDecimal factoryElecActivePower;

    @ApiModelProperty("工厂总有功功率")
    private BigDecimal factoryElecTotalPower;

    @ApiModelProperty("储能站SOC")
    private BigDecimal storageStationSOC;
}
