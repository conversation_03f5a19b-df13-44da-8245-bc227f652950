package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Map;

@Data
@ToString
@Schema(description = "储能计划")
public class CiesEnergyPlanDetailResponse {

    @Schema(description = "项目信息")
    private CiesProjectInfoResponse project;

    @Schema(description = "市场信息")
    private CiesMarketListResponse market;

    @Schema(description = "市场图形")
    private Map<Double, BigDecimal> graphics;

    @Schema(description = "储能计划明细")
    private CiesFullStationResponse plan;

    @Schema(description = "审核信息")
    private CiesEnergyPlanAuditResponse viewInfo;
}
