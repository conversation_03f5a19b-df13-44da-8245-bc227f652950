package com.bcels.cies.api;

import com.bcels.cies.request.CiesIndicatorHisRequest;
import com.bcels.cies.response.CiesIndicatorHis;
import com.bcels.cies.response.CiesIndicatorHisResponse;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.bcels.cies.response.CiesStationMonitorResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.util.List;

@Tag(name = "9、场站监控API")
@FeignClient(name = "ciesStationMonitor", path = "/ciesStationMonitor/v1")

public interface CiesStationMonitorApi {

    @Operation(summary = "场站监控查询")
    @GetMapping("findByProjectId")
    @Parameters({
            @Parameter(name = "projectId", description = "项目ID", required = true)
    })
    ResultData<CiesStationMonitorResponse> findEquipInfoById(@RequestParam("projectId") String projectId);

    @Operation(summary = "指标查询")
    @GetMapping("findByEquipId")
    @Parameters({
            @Parameter(name = "equipId", description = "设备ID", required = true)
    })
    ResultData<List<CiesIndicatorsInfoResponse>> findIndicatorsByEquipId(@RequestParam("equipId") String equipId);

    @Operation(summary = "指标历史查询")
    @PostMapping("findIndicatorHis")
    ResultData<CiesIndicatorHis> findIndicatorHis(@RequestBody CiesIndicatorHisRequest request);

    @Operation(summary = "指标历史导出")
    @PostMapping("export")
    ResultData<Void> export(@RequestBody CiesIndicatorHisRequest request, HttpServletResponse response) throws IOException;
}
