package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("cies_control_his_records")
@ApiModel(value = "CiesControlHisRecordsEntity对象", description = "控制历史记录")
public class CiesControlHisRecordsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("历史记录ID")
    @TableId
    private String historyRecordId;

    @ApiModelProperty("指令下发类型")
    private String commandType;

    @ApiModelProperty("控制模式")
    private String controlMode;

    @ApiModelProperty("控制状态")
    private String controlStatus;

    @ApiModelProperty("指令功率(kw)")
    private BigDecimal commandPower;

    @ApiModelProperty("计划时间")
    private String planDate;

    @ApiModelProperty("时间维度")
    private String timeDimension;

    @ApiModelProperty("下发规则")
    private String dispatchRule;

    @ApiModelProperty("当日覆盖规则")
    private String dailyOverrideRule;

    @ApiModelProperty("是否调整")
    private Integer isAdjusted;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
