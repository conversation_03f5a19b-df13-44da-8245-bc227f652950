package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcels.cies.repository.entity.CiesControlHisRecordsEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesControlHisRequest;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.response.CiesControlHisRecordsListResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 控制历史记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Mapper
public interface CiesControlHisRecordsMapper extends BaseMapper<CiesControlHisRecordsEntity> {

    IPage<CiesControlHisRecordsListResponse> findControlHisRecordsForPage(@Param("page") IPage<CiesControlHisRecordsListResponse> page, @Param("request") CiesControlHisRequest request);

}
