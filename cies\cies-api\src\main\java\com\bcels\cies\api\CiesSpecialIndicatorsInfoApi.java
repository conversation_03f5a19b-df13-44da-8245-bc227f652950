package com.bcels.cies.api;

import com.bcels.cies.request.CiesPointAndIndicatorRequest;
import com.bcels.cies.request.CiesSpecialIndicatorsListRequest;
import com.bcels.cies.request.CiesSpecialIndicatorsUpdateRequest;
import com.bcels.cies.response.CiesPointAndIndicatorResponse;
import com.bcels.cies.response.CiesSpecialIndicatorsResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "5、业务指标API")
@FeignClient(name = "ciesSpecialIndicators", path = "/ciesSpecialIndicators/v1")
public interface CiesSpecialIndicatorsInfoApi {

    @Operation(summary = "特殊指标列表")
    @PostMapping("page")
    ResultData<PageResponse<CiesSpecialIndicatorsResponse>> findForPage(@RequestBody CiesSpecialIndicatorsListRequest request);

    @Operation(summary = "特殊指标编辑")
    @PostMapping("update")
    ResultData<Void> updateSpecialIndicator(@RequestBody CiesSpecialIndicatorsUpdateRequest request);

    @Operation(summary = "查询单个特殊指标信息")
    @GetMapping("findById")
    @Parameters({
            @Parameter(name = "specialIndicatorsId", description = "业务指标ID", required = true)
    })
    ResultData<CiesSpecialIndicatorsResponse> findSpecialIndicatorById(@RequestParam("specialIndicatorsId") String specialIndicatorsId);

    @Operation(summary = "删除单个特殊指标")
    @GetMapping("delete")
    @Parameters({
            @Parameter(name = "specialIndicatorsId", description = "业务指标ID", required = true)
    })
    ResultData<Void> deleteIndicatorsById(@RequestBody CiesSpecialIndicatorsUpdateRequest request);

    @Operation(summary = "查询通道（数据中台）")
    @PostMapping("findChannelForPoint")
    ResultData<List<CiesPointAndIndicatorResponse>> findChannelForPoint(@RequestParam("projectId") String projectId);

    @Operation(summary = "查询测点设备（数据中台）")
    @GetMapping("findEquip")
    @Parameters({
            @Parameter(name = "projectId", description = "项目ID", required = true)
    })
    ResultData<List<CiesPointAndIndicatorResponse>> findEquip(@RequestParam("projectId") String projectId);

    @Operation(summary = "查询指标设备（数据中台）")
    @PostMapping("findEquipForPoint")
    ResultData<List<CiesPointAndIndicatorResponse>> findEquipForPoint(@RequestBody CiesPointAndIndicatorRequest request);

    @Operation(summary = "查询测点（数据中台）")
    @PostMapping("findTestPoint")
    ResultData<List<CiesPointAndIndicatorResponse>> findTestPoint(@RequestBody CiesPointAndIndicatorRequest request);

    @Operation(summary = "查询一次指标（数据中台）")
    @PostMapping("findIndicatorForOne")
    ResultData<List<CiesPointAndIndicatorResponse>> findIndicatorForOne(@RequestBody CiesPointAndIndicatorRequest request);

    @Operation(summary = "查询二次指标（数据中台）")
    @PostMapping("findIndicatorForTwo")
    ResultData<List<CiesPointAndIndicatorResponse>> findIndicatorForTwo(@RequestBody CiesPointAndIndicatorRequest request);
}
