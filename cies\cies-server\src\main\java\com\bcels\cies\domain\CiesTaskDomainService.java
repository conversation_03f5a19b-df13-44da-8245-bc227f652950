package com.bcels.cies.domain;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import com.bcels.cies.client.CiesInternalInterfaceClient;
import com.bcels.cies.emuns.*;
import com.bcels.cies.infrastructure.config.OssConfig;
import com.bcels.cies.infrastructure.utils.SettlementBillUtil;
import com.bcels.cies.repository.entity.*;
import com.bcels.cies.request.CiesAuditOperateRequest;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.request.CiesEssMonthlyBillIncomeRequest;
import com.bcels.cies.request.CiesQueryIndicatorsRequest;
import com.bcels.cies.response.*;
import com.bcels.cies.service.*;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.IdGenUtil;
import com.zwy.common.utils.enums.YesOrNo;
import com.zwy.common.utils.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bcels.cies.emuns.ElectricityPriceTypeEnum.STANDARD_NO_DEEP_VALLEY;

@Slf4j
@Service
public class CiesTaskDomainService {

    @Autowired
    private ICiesEnergyStoragePlanService iCiesEnergyStoragePlanService;

    @Autowired
    private ICiesConnectionPointService iCiesConnectionPointService;

    @Autowired
    private CiesEnergyPlanDomainService ciesEnergyPlanDomainService;

    @Autowired
    private ICiesEssPlanAuditService iCiesEssPlanAuditService;

    @Autowired
    private ICiesEsPlanRuleService iCiesEsPlanRuleService;

    @Autowired
    private ICiesControlHisRecordsService iCiesControlHisRecordsService;

    @Resource
    private CiesInternalInterfaceClient client;

    @Autowired
    private ICiesIndicatorsInfoService iCiesIndicatorsInfoService;

    @Autowired
    private ICiesSpecialIndicatorsService iCiesSpecialIndicatorsService;

    @Autowired
    private ICiesProjectInfoService iCiesProjectInfoService;

    @Autowired
    private ICiesEssMonthlyBillService iCiesEssMonthlyBillService;

    @Autowired
    private ICiesMarketElecPriService iCiesMarketElecPriService;

    @Autowired
    private ICiesPeakValleyPriceGapService iCiesPeakValleyPriceGapService;

    @Autowired
    private ICiesPeakValleySummaryService iCiesPeakValleySummaryService;

    @Autowired
    private OssConfig ossConfig;

    @Value("${mftcc.aliyun.oss.billUrl}")
    private String billUrl;

    /**
     * 拷贝生成第二天的储能计划
     */
    @Transactional
    public void copyPlan() {
        try {
            // 查询当天是否有计划，按照当天有计划的项目拷贝计划到第二天
            CiesEnergyPlanListRequest request = new CiesEnergyPlanListRequest();
            String today = LocalDate.now().toString().replace("-", "/");
            String tomorrow = LocalDate.now().plusDays(1).toString().replace("-", "/");
            request.setPlanDate(today);
            List<CiesEnergyStoragePlanResponse> oldPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
            Map<String, List<CiesEnergyStoragePlanResponse>> stationPlanMap = oldPlan.stream().filter(item -> item.getProjectId() != null).collect(Collectors.groupingBy(
                    CiesEnergyStoragePlanResponse::getProjectId
            ));
            // 拷贝全场站计划
            List<String> projectIds = new ArrayList<>();
            stationPlanMap.forEach((projectId, planList) -> {
                request.setPlanDate(tomorrow);
                request.setProjectId(projectId);
                List<CiesEnergyStoragePlanResponse> plan1 = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
                if (CollectionUtils.isEmpty(plan1)) {
                    List<CiesEnergyStoragePlanEntity> ciesEnergyStoragePlanEntities = BeanCopyUtil.copyListProperties(planList, CiesEnergyStoragePlanEntity::new);
                    ciesEnergyStoragePlanEntities.forEach(plan -> {
                                plan.setPlanDate(tomorrow);
                                plan.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
                                plan.setCreateTime(LocalDateTime.now());
                                plan.setCreateBy("服务器");
                            }
                    );
                    iCiesEnergyStoragePlanService.saveBatch(ciesEnergyStoragePlanEntities);
                    projectIds.add(projectId);
                }
            });
            //拷贝并网点计划
            projectIds.forEach(projectId -> {
                // 同步完计划，同步初始化审核表
                // 计划下发审核表初始化
                CiesEssPlanAuditEntity audit = new CiesEssPlanAuditEntity();
                audit.setPlanDate(tomorrow);
                audit.setProjectId(projectId);
                audit.setIsAdjusted(YesOrNo.NO.getCode());
                audit.setCreateTime(LocalDateTime.now());
                audit.setCreateBy("服务器");
                audit.setDispatchAuditId(IdGenUtil.genUniqueId());
                audit.setAuditStatus(AuditStatusEnum.PENDING_REVIEW.getCode());
                audit.setDispatchStatus(DispatchStatusEnum.DISTRIBUTED.getCode());
                iCiesEssPlanAuditService.save(audit);

                // 拷贝并网点计划
                List<String> projectIdList = new ArrayList<>();
                projectIdList.add(projectId);
                List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(projectIdList);
                if (CollectionUtils.isEmpty(connInfo)){
                    return;
                }
                connInfo.sort(Comparator.comparing(CiesConnectionPointResponse::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));
                List<String> list2 = connInfo.stream().map(CiesConnectionPointResponse::getConnectionPointId).toList();
                List<CiesEnergyStoragePlanResponse> planList = iCiesEnergyStoragePlanService.findConnPlanByConnIds(list2, today);
                List<CiesEnergyStoragePlanEntity> entities = BeanCopyUtil.copyListProperties(planList, CiesEnergyStoragePlanEntity::new);
                entities.forEach(item -> {
                    item.setPlanDate(tomorrow);
                    item.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
                    item.setCreateTime(LocalDateTime.now());
                    item.setCreateBy("服务器");
                });
                iCiesEnergyStoragePlanService.saveBatch(entities);
            });
        } catch (Exception e) {
            log.info("批量拷贝第二日储能计划失败，失败原因是：{}", e.getMessage());
            throw new BusinessException("批量拷贝第二日储能计划失败，失败原因是：" + e.getMessage());
        }
    }

    /**
     * 下发储能计划
     */
    @Transactional
    public void distributePlan() {
        try {
            // 获取审核状态为“已确认”并网点计划
            List<CiesEssPlanAuditEntity> confirmedReview = iCiesEssPlanAuditService.findConfirmedReview();
            List<String> projectIdList = confirmedReview.stream().map(CiesEssPlanAuditEntity::getProjectId).toList();
            String tomorrow = LocalDate.now().plusDays(1).toString().replace("-", "/");

            // 获取项目对应并网点集合
            List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(projectIdList);
            // 获取并网点和通道id的映射关系
            Map<String, String> connectionPointToChannelMap = new HashMap<>();
            // 遍历集合
            for (CiesConnectionPointResponse item : connInfo) {
                String connectionPointId = item.getConnectionPointId();
                String channelId = item.getChannelId();
                connectionPointToChannelMap.put(connectionPointId, channelId);
            }
            connInfo.sort(Comparator.comparing(CiesConnectionPointResponse::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));
            Map<String, List<String>> connMap = connInfo.stream().collect(Collectors.groupingBy(
                    CiesConnectionPointResponse::getProjectId,
                    Collectors.mapping(
                            CiesConnectionPointResponse::getConnectionPointId,
                            Collectors.toList()
                    )
            ));

            // 获取项目-并网点信息集合
            Map<String, List<CiesConnectionPointResponse>> connListInfo = connInfo.stream().collect(Collectors.groupingBy(
                    CiesConnectionPointResponse::getProjectId
            ));

            // 获取已审核通过的项目以及计划
            List<CiesEnergyStoragePlanResponse> connPlanList = iCiesEnergyStoragePlanService.findConfirmedPlan(projectIdList, tomorrow);
            Map<String, List<CiesEnergyStoragePlanResponse>> planMap = connPlanList.stream().collect(Collectors.groupingBy(
                    CiesEnergyStoragePlanResponse::getConnectionPointId
            ));

            // 项目-并网点-计划
            Map<String, Map<String, List<CiesEnergyStoragePlanResponse>>> planListMap = mergeMaps(connMap, planMap);

            // 根据项目id查询对应的下发规则
            projectIdList.forEach(projectId -> {
                CiesEsPlanRuleResponse rule = iCiesEsPlanRuleService.findByProjectId(projectId);
                if (ObjectUtils.isEmpty(rule)) {
                    return;
                }
                CiesEnergyPlanListResponse auditInfo = iCiesEssPlanAuditService.findAuditByProject(projectId, tomorrow);

                // 如果是闭环需要下发
                if (CommandDispatchEnum.CLOSED_LOOP.getCode().equals(rule.getCommandType())) {
                    // 最终下发计划
                    JSONObject finalPlan = new JSONObject();
                    // 并网点计划
                    JSONArray jsonObject1 = new JSONArray();
                    // 按照不同时间维度对计划进行组装
                    Map<String, List<CiesEnergyStoragePlanResponse>> connPlan = planListMap.get(projectId);
                    connPlan.forEach((connPointId, plan) -> {
                        List<CiesEnergyStoragePlanResponse> resultPlan;
                        // 按照不同时间维度对计划进行组装
                        resultPlan = spiltPlan(plan, rule);
                        // 根据下发规则替换实际下发计划的计划功率值
                        coverPowerByRule(resultPlan, connPointId, rule);
                        JSONObject jsonObject = assemblyIssuePlan(resultPlan, connPointId, connectionPointToChannelMap, rule);
                        jsonObject1.add(jsonObject);
                    });
                    finalPlan.put("plan_lst", jsonObject1);
                    finalPlan.put("projectId", projectId);
                    log.info("定时任务下发项目：{}下发计划数据：{}", projectId, finalPlan.toJSONString());
                    // 下发到数据中台
                    client.distributePlan(finalPlan);
                }
                // 保存下发记录
                String hisRecordId = IdGenUtil.genUniqueId();
                CiesControlHisRecordsEntity entity = BeanCopyUtil.copyProperties(rule, CiesControlHisRecordsEntity::new);
                entity.setHistoryRecordId(hisRecordId);
                entity.setControlMode("自动模式");
                entity.setIsAdjusted(auditInfo.getIsAdjusted());
                entity.setCreateBy("服务器");
                entity.setCreateTime(LocalDateTime.now());
                iCiesControlHisRecordsService.save(entity);
                // 保存历史并网点信息
                List<CiesConnectionPointResponse> ciesConnectionPointResponses = connListInfo.get(projectId);
                List<CiesConnectionPointEntity> ciesConnectionPointEntities = BeanCopyUtil.copyListProperties(ciesConnectionPointResponses, CiesConnectionPointEntity::new);
                ciesConnectionPointEntities.forEach(conn -> {
                    String connPointId = IdGenUtil.genUniqueId();
                    // 保存历史并网点计划
                    List<CiesEnergyStoragePlanResponse> ciesEnergyStoragePlan = planMap.get(conn.getConnectionPointId());
                    List<CiesEnergyStoragePlanEntity> connPlan = BeanCopyUtil.copyListProperties(ciesEnergyStoragePlan, CiesEnergyStoragePlanEntity::new);
                    connPlan.forEach(plan -> {
                        plan.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
                        plan.setConnectionPointId(connPointId);
                        plan.setCreateBy("服务器");
                        plan.setCreateTime(LocalDateTime.now());
                    });
                    iCiesEnergyStoragePlanService.saveBatch(connPlan);
                    conn.setConnectionPointId(connPointId);
                    conn.setHistoryRecordId(hisRecordId);
                    conn.setCreateBy("服务器");
                    conn.setCreateTime(LocalDateTime.now());

                });
                iCiesConnectionPointService.saveBatch(ciesConnectionPointEntities);
                // 保存历史全场站计划
                CiesEnergyPlanListRequest request = new CiesEnergyPlanListRequest();
                request.setProjectId(projectId);
                request.setPlanDate(tomorrow);
                List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
                List<CiesEnergyStoragePlanEntity> ciesEnergyStoragePlanEntities = BeanCopyUtil.copyListProperties(stationEnergyPlan, CiesEnergyStoragePlanEntity::new);
                ciesEnergyStoragePlanEntities.forEach(plan -> {
                    plan.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
                    plan.setHistoryRecordId(hisRecordId);
                    plan.setCreateBy("服务器");
                    plan.setCreateTime(LocalDateTime.now());
                    plan.setProjectId(null);
                });
                iCiesEnergyStoragePlanService.saveBatch(ciesEnergyStoragePlanEntities);
                // 更新审核状态为已下发
                CiesAuditOperateRequest auditStatusRequest = new CiesAuditOperateRequest();
                auditStatusRequest.setDispatchStatus(DispatchStatusEnum.ISSUED.getCode());
                auditStatusRequest.setProjectId(projectId);
                List<String> list = new ArrayList<>();
                list.add(tomorrow);
                auditStatusRequest.setPlanDateList(list);
                auditStatusRequest.setDr(YesOrNo.NO.getCode());
                iCiesEssPlanAuditService.updatePlanAuditStatus(auditStatusRequest);
            });
        }catch (Exception e){
            log.info("定时任务批量下发储能计划失败，失败原因：{}", e.getMessage());
            throw new BusinessException("定时任务批量下发储能计划失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * 重新计算计划利润，成本
     */
    public void calculateProfit() {
        String calDate = LocalDate.now().minusDays(1).toString().replace("-", "/");
        List<CiesEnergyPlanListResponse> auditByDateList = iCiesEssPlanAuditService.findAuditByDate(calDate);
        if (auditByDateList == null) {
            return;
        }
        auditByDateList.forEach(audit -> {
            try {
                ciesEnergyPlanDomainService.recalculatePlan(
                        audit.getDispatchAuditId(),
                        audit.getProjectId(),
                        calDate
                );
            } catch (BusinessException e) {
                log.error(" 定时任务重新计算利润-项目 {} 利润计算失败: {}",
                        audit.getProjectId(), e.getMessage());
            }
        });
    }

    public void queryLatestIndicatorData() {
        try {
            // 同步指标与测点最新数据
            List<CiesIndicatorsInfoResponse> indicators = iCiesIndicatorsInfoService.findIndicators(null);
            if (CollectionUtils.isEmpty(indicators)){
                return;
            }
            List<String> idList = indicators.stream().map(CiesIndicatorsInfoResponse::getIndicatorId).distinct().toList();
            CiesQueryIndicatorsRequest queryRequest = new CiesQueryIndicatorsRequest();
            queryRequest.setIndicatorIds(idList);
            Map<String, List<CiesHisIndicatorsDataResponse>> result = client.batchQueryData(queryRequest);
            log.info("获取实时数据测点与指标，参数为：{}，返回结果：{}", idList,result);
            if (result != null) {
                result.forEach((key, value) -> {
                    CiesIndicatorsInfoEntity entity = new CiesIndicatorsInfoEntity();
                    entity.setIndicatorId(key);
                    entity.setCurrentValue(value.get(0).getIndicatorValue());
                    entity.setUpdateTime(value.get(0).getUpdateTime());
                    entity.setUpdateBy("服务器");
                    iCiesIndicatorsInfoService.updateCurrentData(entity);
                });
            }

            // 同步特殊指标最新数据
            List<CiesSpecialIndicatorsResponse> specialIndicators = iCiesSpecialIndicatorsService.findSpecialIndicators();
            Map<String, String> map = convertToMap(specialIndicators);
            List<String> specialIds = map.values().stream().distinct().filter(Objects::nonNull).toList();
            queryRequest.setIndicatorIds(specialIds);
            Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap = client.batchQueryData(queryRequest);
            log.info("获取业务指标的实时数据测点与指标，参数为：{}，返回结果：{}", specialIds,stringListMap);
            if (stringListMap != null) {
                stringListMap.forEach((key, value) -> {
                    CiesSpecialIndicatorsEntity entity = new CiesSpecialIndicatorsEntity();
                    entity.setRelateIndicatorId(key);
                    entity.setCurrentData(value.get(0).getIndicatorValue());
                    entity.setUpdateTime(value.get(0).getUpdateTime());
                    entity.setUpdateBy("服务器");
                    iCiesSpecialIndicatorsService.updateCurrentData(entity);
                });
            }
        } catch (Exception e) {
            log.info("同步最新测点与指标数据失败,失败原因：{}", e.getMessage());
            throw new BusinessException("同步最新测点与指标数据失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * 修改审核状态为"已过期"
     */
    public void updatePlanStatus() {
        try {
            iCiesEssPlanAuditService.updatePlanStatus();
        } catch (Exception e) {
            log.info("批量更新计划审核状态为已过期失败，失败原因是：{}", e.getMessage());
            throw new BusinessException("批量更新计划审核状态为已过期失败，失败原因是：" + e.getMessage());
        }
    }


    /**
     * 修改审核状态为"已过期"
     */
    @Transactional
    public void generateSettlementBill() {
        try {
            // 查询所有储能项目
            List<CiesProjectInfoResponse> allProject = iCiesProjectInfoService.findAllProject();
            // 生成结算单记录
            List<CiesEssMonthlyBillEntity> list = generateSettlementBillRecord(allProject);
            // 遍历结算单生成对应的储能峰谷价差信息
            List<CiesSettlementBillGenerateResponse> responseList = generatePeakValleyPriceGap(list);
            // 生成结算单
            generateSettlementBillFile(responseList);
        } catch (Exception e) {
            log.error("批量生成结算单失败，失败原因是：{}", e.getMessage(), e);
            throw new BusinessException("批量生成结算单失败，失败原因是：" + e.getMessage(),e);
        }
    }


    public void generateSettlementBillFile(List<CiesSettlementBillGenerateResponse> responseList) {
        try {
            for (CiesSettlementBillGenerateResponse response : responseList) {
                if (CollectionUtils.isEmpty(response.getBillList())){
                    continue;
                }
                // 生成结算单excel
                byte[] bytes = SettlementBillUtil.generateBillExcel(response);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                // 上传exel到oss
                String excelFileName = billUrl + LocalDateTime.now().format(formatter) + ".xlsx";
                String fileName1 = ossConfig.writeOss(excelFileName, bytes);
                // excel转化为pdf
                byte[] pdfBytes = SettlementBillUtil.convertExcelToPdfBytes(bytes);
                String pdfFileName = billUrl+LocalDateTime.now().format(formatter)  + ".pdf";
                String fileName2 = ossConfig.writeOss(pdfFileName, pdfBytes);
                CiesEssMonthlyBillIncomeRequest request = new CiesEssMonthlyBillIncomeRequest();
                request.setOfficeAttachmentUrl(fileName1);
                request.setPdfAttachmentUrl(fileName2);
                request.setUpdateBy("服务器");
                request.setEssBillId(response.getEssBillId());
                iCiesEssMonthlyBillService.updateMonthlyBill(request);
            }
        }catch (Exception e){
            log.error("定时任务生成结算单文件失败",e);
        }
    }
    /**
     * 储能峰谷价差信息落库
     * @param list
     */
    public List<CiesSettlementBillGenerateResponse> generatePeakValleyPriceGap(List<CiesEssMonthlyBillEntity> list) {
        List<CiesSettlementBillGenerateResponse> responseList = new ArrayList<>();
        // 初始化峰谷价差总收益（元）
        BigDecimal peakValleyRevenue = BigDecimal.ZERO;
        for (CiesEssMonthlyBillEntity entity : list) {
            List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceResponse = new ArrayList<>();
            LocalDateTime startTime = entity.getStartTime();
            LocalDateTime endTime = entity.getEndTime();
            // 查询项目倍率
            CiesProjectInfoEntity byId = iCiesProjectInfoService.getById(entity.getProjectId());
            // 线下结算无需执行后续逻辑
            if (ElectricityPriceTypeEnum.OFFLINE_CALCULATION.getCode().equals(byId.getTemplateType())) {
                continue;
            }
            BigDecimal magnification = byId.getMeterMultiplier() != null ? byId.getMeterMultiplier() : BigDecimal.ZERO;
            // 查询项目指定月电价
            CiesMarketElecPriEntity elecPriInfo = iCiesMarketElecPriService.findElecPriInfo(entity.getSettlementMonth().replace("-","/"), entity.getProjectId());
            // 根据项目id获取并网点信息，按照并网点维度虎丘峰谷价差数据
            List<String> projectIds = new ArrayList<>();
            projectIds.add(entity.getProjectId());
            List<CiesConnectionPointResponse> connByProjectIds = iCiesConnectionPointService.findConnByProjectIds(projectIds);
            if (CollectionUtils.isEmpty(connByProjectIds)) {
                continue;
            }
            for (CiesConnectionPointResponse connectionPointResponse : connByProjectIds) {
                // 获取对应并网点下的实时指标数据
                List<CiesSpecialIndicatorsResponse> connSpecialIndicators = iCiesSpecialIndicatorsService.findConnSpecialIndicators(connectionPointResponse.getConnectionPointId());
                if (CollectionUtils.isEmpty(connSpecialIndicators)) {
                    continue;
                }
                // 正向分项合计
                BigDecimal positiveTotalAmount = BigDecimal.ZERO;
                // 反向分项合计
                BigDecimal reverseTotalAmount = BigDecimal.ZERO;
                List<CiesPeakValleyPriceGapEntity> gapEntityList = new ArrayList<>();
                for (CiesSpecialIndicatorsResponse indicator : connSpecialIndicators) {
                    CiesPeakValleyPriceGapEntity gapEntity = new CiesPeakValleyPriceGapEntity();
                    gapEntity.setPriceGapId(IdGenUtil.genUniqueId());
                    gapEntity.setTimeRange(indicator.getIndicatorName().substring(indicator.getIndicatorName().lastIndexOf("电量") - 1, indicator.getIndicatorName().lastIndexOf("电量")));
                    gapEntity.setStatPeriod(indicator.getIndicatorName().contains("正向有功") ? "充电（正向有功）" : "放电（反向有功）");
                    // 查询上月读数
                    CiesQueryIndicatorsRequest request = new CiesQueryIndicatorsRequest();
                    List<String> indicators = Stream.of(indicator.getRelateIndicatorId()).collect(Collectors.toList());
                    request.setIndicatorIds(indicators);
                    request.setStartTime(startTime);
                    request.setEndTime(startTime.plusMinutes(1));
                    request.setDataType(indicator.getRelateDataType());
                    Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap = client.batchQueryHisData(request);
                    if (stringListMap != null) {
                        CiesHisIndicatorsDataResponse ciesHisIndicatorsDataResponses = stringListMap.get(indicator.getRelateIndicatorId()).get(0);
                        gapEntity.setLastMonthRead(ciesHisIndicatorsDataResponses.getIndicatorValue() != null ? ciesHisIndicatorsDataResponses.getIndicatorValue() : BigDecimal.ZERO);
                    }
                    // 查询当月读数
                    CiesQueryIndicatorsRequest request1 = new CiesQueryIndicatorsRequest();
                    List<String> indicators1 = Stream.of(indicator.getRelateIndicatorId()).collect(Collectors.toList());
                    request1.setIndicatorIds(indicators1);
                    request1.setStartTime(endTime);
                    request1.setEndTime(endTime.plusMinutes(1));
                    request1.setDataType(indicator.getRelateDataType());
                    Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap1 = client.batchQueryHisData(request);
                    if (stringListMap1 != null) {
                        CiesHisIndicatorsDataResponse ciesHisIndicatorsDataResponses1 = stringListMap1.get(indicator.getRelateIndicatorId()).get(0);
                        gapEntity.setCurrentMonthRead(ciesHisIndicatorsDataResponses1.getIndicatorValue() != null ? ciesHisIndicatorsDataResponses1.getIndicatorValue() : BigDecimal.ZERO);
                    }
                    // 计算抄见电量
                    BigDecimal power = (gapEntity.getCurrentMonthRead().subtract(gapEntity.getLastMonthRead())).multiply(magnification);
                    gapEntity.setMeteredUsage(power);
                    // 计算结算电量
                    gapEntity.setBilledUsage(power);
                    // 获取电量
                    BigDecimal elecPrice = getElecPrice(gapEntity.getTimeRange(), elecPriInfo);
                    gapEntity.setUnitPrice(elecPrice);
                    gapEntity.setAmount(elecPrice.multiply(gapEntity.getBilledUsage()).setScale(2, RoundingMode.HALF_UP));
                    gapEntity.setConnectionPointId(connectionPointResponse.getConnectionPointId());
                    gapEntityList.add(gapEntity);
                    // 正向有功合计
                    if (gapEntity.getStatPeriod().contains("正向有功")) {
                        positiveTotalAmount = positiveTotalAmount.add(gapEntity.getAmount());
                    } else {
                        // 反向有功合计
                        reverseTotalAmount = reverseTotalAmount.add(gapEntity.getAmount());
                    }
                }
                if (ElectricityPriceTypeEnum.STANDARD_WITH_DEEP_VALLEY.getCode().equals(byId.getTemplateType()) || ElectricityPriceTypeEnum.WITH_TAX_DIFF_AND_DEEP_VALLEY.getCode().equals(byId.getTemplateType())){
                    // 初始化两笔深谷数据
                    generateDeepRecord(connectionPointResponse, gapEntityList);
                }
                iCiesPeakValleyPriceGapService.saveBatch(gapEntityList);
                List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceList = BeanCopyUtil.copyListProperties(gapEntityList, CiesPeakValleyPriceGapResponse::new);
                for (CiesPeakValleyPriceGapResponse priceGapResponse : ciesPeakValleyPriceList) {
                    if (priceGapResponse.getStatPeriod().contains("正向有功")) {
                        priceGapResponse.setTotalAmount(positiveTotalAmount);
                    } else {
                        priceGapResponse.setTotalAmount(reverseTotalAmount);
                    }
                    priceGapResponse.setConnectionPointName(connectionPointResponse.getConnectionPointName());
                    priceGapResponse.setMeterMultiplier(byId.getMeterMultiplier());
                }
                // 保存峰谷价差分项合计
                savePeakValleySummary(connectionPointResponse.getConnectionPointId(), entity.getEssBillId(), positiveTotalAmount, reverseTotalAmount);
                // 计算峰谷价差总收益（元） = 反向有功合计 - 正向有功合计
                peakValleyRevenue = peakValleyRevenue.add(reverseTotalAmount.subtract(positiveTotalAmount));
                ciesPeakValleyPriceResponse.addAll(ciesPeakValleyPriceList);
            }
            // 计算收益
            CiesSettlementBillGenerateResponse response = calIncome(peakValleyRevenue, entity.getProjectId(), entity.getEssBillId());
            CiesPeakValleyPriceGapResponse priceGapResponse = new CiesPeakValleyPriceGapResponse();
            priceGapResponse.setConnectionPointName("测试并网点");
            ciesPeakValleyPriceResponse.add(priceGapResponse);
            response.setBillList(ciesPeakValleyPriceResponse);
            response.setRemark(entity.getRemark());
            String monthDesc = entity.getSettlementMonth().replace("/", "年") + "月";
            response.setBillTitle("储能项目结算单-" + monthDesc);
            response.setTemplateType(byId.getTemplateType());
            response.setEssBillId(entity.getEssBillId());
            responseList.add(response);
        }
        return responseList;
    }

    /**
     * 初始化深谷数据
     * @param connectionPointResponse
     * @param gapEntityList
     */
    private void generateDeepRecord(CiesConnectionPointResponse connectionPointResponse, List<CiesPeakValleyPriceGapEntity> gapEntityList) {
        // 公共字段值
        BigDecimal zero = BigDecimal.ZERO;

        // 第一条记录（正向）
        CiesPeakValleyPriceGapEntity positive = new CiesPeakValleyPriceGapEntity();
        positive.setPriceGapId(IdGenUtil.genUniqueId());
        positive.setConnectionPointId(connectionPointResponse.getConnectionPointId());
        positive.setStatPeriod("充电（正向有功）");
        positive.setTimeRange("深谷");
        positive.setLastMonthRead(zero);
        positive.setCurrentMonthRead(zero);
        positive.setMeteredUsage(zero);
        positive.setBilledUsage(zero);
        positive.setUnitPrice(zero);
        positive.setAmount(zero);

        CiesPeakValleyPriceGapEntity negative = new CiesPeakValleyPriceGapEntity();
        negative.setConnectionPointId(connectionPointResponse.getConnectionPointId());
        negative.setPriceGapId(IdGenUtil.genUniqueId());
        negative.setStatPeriod("放电（反向有功）");
        negative.setTimeRange("深谷");
        negative.setLastMonthRead(zero);
        negative.setCurrentMonthRead(zero);
        negative.setMeteredUsage(zero);
        negative.setBilledUsage(zero);
        negative.setUnitPrice(zero);
        negative.setAmount(zero);
        gapEntityList.add(positive);
        gapEntityList.add(negative);
    }

    /**
     * 计算收益
     *
     * @param peakValleyRevenue
     * @param projectId
     * @param billId
     */
    private CiesSettlementBillGenerateResponse calIncome(BigDecimal peakValleyRevenue, String projectId, String billId) {
        CiesEssMonthlyBillIncomeRequest request = new CiesEssMonthlyBillIncomeRequest();
        // 获取项目的项目信息
        CiesProjectInfoEntity projectInfo = iCiesProjectInfoService.getById(projectId);
        String templateType = projectInfo.getTemplateType();
        // 甲方收益/甲方分成金额（元）】
        BigDecimal partyAIncome = peakValleyRevenue.multiply(projectInfo.getPartyARatio().divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);
        request.setPartyAIncome(partyAIncome);
        // 乙方收益/乙方分成金额（元）
        BigDecimal partyBIncome = peakValleyRevenue.multiply(projectInfo.getPartyBRatio().divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);
        request.setPartyBIncome(partyBIncome);
        if (ElectricityPriceTypeEnum.WITH_TAX_DIFF_AND_DEEP_VALLEY.getCode().equals(templateType)) {
            request.setPartyAAmount(partyAIncome);
            request.setPartyBAmount(partyBIncome);
            // 税差金额(元) = 【乙方分成金额（元）】*【税差计算比例】
            BigDecimal taxAdjustmentAmount = partyBIncome.multiply(projectInfo.getTaxAdjustmentRate().divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);
            // 甲方收益(元)
            partyAIncome = partyAIncome.add(taxAdjustmentAmount);
            request.setPartyAIncome(partyAIncome);
            // 乙方收益(元)
            partyBIncome = partyBIncome.subtract(taxAdjustmentAmount);
            request.setPartyBIncome(partyBIncome);
            // 税差金额
            request.setTaxAdjustmentRate(projectInfo.getTaxAdjustmentRate());
            request.setTaxAdjustmentAmount(taxAdjustmentAmount);
        }
        request.setPartyARatio(projectInfo.getPartyARatio());
        request.setPartyBRatio(projectInfo.getPartyBRatio());
        request.setUpdateBy("服务器");
        request.setEssBillId(billId);
        iCiesEssMonthlyBillService.updateMonthlyBill(request);
        CiesSettlementBillGenerateResponse response = BeanCopyUtil.copyProperties(request, CiesSettlementBillGenerateResponse::new);
        response.setProjectName(projectInfo.getProName());
        response.setPartyAName(projectInfo.getPartyAName());
        response.setPartyBName(projectInfo.getPartyBName());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy 年M月d日");
        String chineseDate = LocalDate.now().format(formatter);
        response.setGenerateDate(chineseDate);
        return response;
    }
    /**
     * 保存峰谷价差分项合计
     * @param connPointId
     * @param billId
     * @param positiveTotalAmount
     * @param reverseTotalAmount
     */
    private void savePeakValleySummary(String connPointId, String billId, BigDecimal positiveTotalAmount, BigDecimal reverseTotalAmount) {
        // 保存峰谷价差分项合计
        List<CiesPeakValleySummaryEntity> list = new ArrayList<>();
        List<String> statPeriod = Stream.of("充电（正向有功）", "放电（反向有功）").toList();
        for (String stat : statPeriod) {
            CiesPeakValleySummaryEntity entity = new CiesPeakValleySummaryEntity();
            entity.setId(IdGenUtil.genUniqueId());
            entity.setStatPeriod(stat);
            entity.setConnectionPointId(connPointId);
            entity.setEssBillId(billId);
            entity.setCreateBy("服务器");
            entity.setCreateTime(LocalDateTime.now());
            if (stat.contains("正向有功")) {
                entity.setTotalAmount(positiveTotalAmount);
            } else {
                entity.setTotalAmount(reverseTotalAmount);
            }
            list.add(entity);
        }
        iCiesPeakValleySummaryService.saveBatch(list);
    }

    private BigDecimal getElecPrice(String timeRange,CiesMarketElecPriEntity elecPriInfo){
        switch (timeRange) {
            case "峰": // 峰时段
                return elecPriInfo.getHighPeriodPrice()  != null ? elecPriInfo.getHighPeriodPrice()  : BigDecimal.ZERO;
            case "平": // 平时段
                return elecPriInfo.getFlatPeriodPrice()  != null ? elecPriInfo.getFlatPeriodPrice()  : BigDecimal.ZERO;
            case "谷": // 谷时段
                return elecPriInfo.getValleyPeriodPrice()  != null ? elecPriInfo.getValleyPeriodPrice()  : BigDecimal.ZERO;
            case "尖": // 尖峰时段
                return elecPriInfo.getPeakPeriodPrice()  != null ? elecPriInfo.getPeakPeriodPrice()  : BigDecimal.ZERO;
            default:
                return BigDecimal.ZERO;

        }

    }


    /**
     * 生成结算单记录
     *
     * @param allProject
     */
    public List<CiesEssMonthlyBillEntity> generateSettlementBillRecord(List<CiesProjectInfoResponse> allProject) {
        if (CollectionUtils.isEmpty(allProject)) {
            return Collections.emptyList();
        }
        // 计算当月一号
        LocalDate today = LocalDate.now().withDayOfMonth(1);
        // 获取上个月一号
        LocalDate lastDay = today.minusMonths(1);
        // 获取结算月份
        String month = String.format("%d-%02d", lastDay.getYear(), lastDay.getMonthValue());
        // 获取开始时间和结束时间
        LocalDateTime startTime = lastDay.atTime(LocalTime.MIN);
        LocalDateTime endTime = today.atTime(LocalTime.MIN);
        List<CiesEssMonthlyBillEntity> list = new ArrayList<>();

        for (CiesProjectInfoResponse project : allProject) {
            CiesEssMonthlyBillEntity billEntity = new CiesEssMonthlyBillEntity();
            // 获取说明变量
            DateTimeFormatter chineseFormatter = DateTimeFormatter.ofPattern("yyyy年M月dd日 HH:mm:ss");
            String chineseStartTime = startTime.format(chineseFormatter);
            String chineseEndTime = endTime.format(chineseFormatter);
            String monthDesc = month.replace("-", "年") + "月";
            CiesProjectInfoEntity projectInfo = iCiesProjectInfoService.getById(project.getProjectId());
            // 如果储能项目结算单信息未填写不生成结算单
            if (StringUtils.isEmpty(projectInfo.getPartyAName())){
                continue;
            }
            String template;
            String result = "";
            billEntity.setIssueMethod(BillingMethodEnum.PLATFORM_ISSUE.getCode());
            if (ElectricityPriceTypeEnum.OFFLINE_CALCULATION.getCode().equals(projectInfo.getTemplateType())) {
                billEntity.setIssueMethod(BillingMethodEnum.OFFLINE_ISSUE.getCode());
                // 线下无需做逻辑处理
            } else if (ElectricityPriceTypeEnum.WITH_TAX_DIFF_AND_DEEP_VALLEY.getCode().equals(projectInfo.getTemplateType())) {
                // 含税模版文字需要更新
                // 原始字符串模板(含税)
                template = "（1）以下数据为从%s - %s的结算数据。\n" +
                        "（2）电价为企业%s份供电局电费单中各时段的电能电费单价、输配电费单价、基金及附加费单价、市场化分摊电费单价、系统运行费单价之和。\n" +
                        "（3）峰谷价差总收益=放电电金额之和-充电金额之和；甲方分成金额=峰谷价差总收益*甲方分成比例；乙方分成金额=峰谷价差总收益*乙方分成比例；\n" +
                        "税差金额=乙方分成金额*税差计算比例；甲方收益=甲方分成比例+税差金额；乙方收益=乙方分成金额-税差金额。";
                // 动态替换时间变量
                result = String.format(template, chineseStartTime, chineseEndTime, monthDesc);
            } else {
                // 原始字符串模板
                template = "（1）以下数据为从%s - %s的结算数据。\n" +
                        "（2）电价为企业%s份供电局电费单中各时段的电能电费单价、输配电费单价、基金及附加费单价、市场化分摊电费单价、系统运行费单价之和。\n" +
                        "（3）峰谷价差总收益=放电电金额之和-充电金额之和；甲方收益=峰谷价差总收益*甲方分成比例；乙方收益=峰谷价差总收益*乙方分成比例。";
                // 动态替换时间变量
                result = String.format(template, chineseStartTime, chineseEndTime, monthDesc);
            }
            billEntity.setEssBillId(IdGenUtil.genUniqueId());
            billEntity.setProjectId(project.getProjectId());
            billEntity.setSettlementMonth(month);
            billEntity.setSettlementStatus(SettlementBillStatusEnum.PENDING_DISPATCH.getCode());
            billEntity.setStartTime(startTime);
            billEntity.setEndTime(endTime);
            billEntity.setRemark(result);
            billEntity.setCreateTime(LocalDateTime.now());
            billEntity.setCreateBy("服务器");
            list.add(billEntity);
        }
        iCiesEssMonthlyBillService.saveBatch(list);
        return list;
    }

        /**
         * 合并为项目-并网点-计划关系
         *
         * @param projectConnMap
         * @param connPlanMap
         * @return
         */
    Map<String, Map<String, List<CiesEnergyStoragePlanResponse>>> mergeMaps(Map<String, List<String>> projectConnMap,
                                                                            Map<String, List<CiesEnergyStoragePlanResponse>> connPlanMap) {

        Map<String, Map<String, List<CiesEnergyStoragePlanResponse>>> result = new HashMap<>();

        for (Map.Entry<String, List<String>> projectEntry : projectConnMap.entrySet()) {
            Map<String, List<CiesEnergyStoragePlanResponse>> innerMap = new HashMap<>();

            for (String connId : projectEntry.getValue()) {
                if (connPlanMap.containsKey(connId)) {
                    innerMap.put(connId, connPlanMap.get(connId));
                }
            }

            if (!innerMap.isEmpty()) {
                result.put(projectEntry.getKey(), innerMap);
            }
        }

        return result;
    }

    private List<CiesEnergyStoragePlanResponse> spiltPlan(List<CiesEnergyStoragePlanResponse> plan, CiesEsPlanRuleResponse rule) {
        List<CiesEnergyStoragePlanResponse> sortedPlan = plan.stream().sorted(Comparator.comparing(CiesEnergyStoragePlanResponse::getStartTime)).toList();
        List<CiesEnergyStoragePlanResponse> result;
        boolean flag = is15MinuteMultiple(sortedPlan.get(0).getEndTime(), sortedPlan.get(0).getStartTime());

        if (TimeDimensionEnum.MINUTES_15.getCode().equals(rule.getTimeDimension())) {
            if (flag) {
                result = splitTo15MinIntervals(sortedPlan);
            } else {
                throw new BusinessException("并网点计划：" + sortedPlan.get(0).getConnectionPointId() + "时间维度发生变更，请管理员处理");
            }
        } else {
            if (flag) {
                throw new BusinessException("并网点计划：" + sortedPlan.get(0).getConnectionPointId() + "时间维度发生变更，请管理员处理");
            } else {
                result = splitToMinuteIntervals1(sortedPlan);
            }
        }
        return result;
    }


    /**
     * 判断是否时间间隔是15分钟的倍数
     *
     * @param time1
     * @param time2
     * @return
     */
    private boolean is15MinuteMultiple(String time1, String time2) {
        LocalTime t1 = LocalTime.parse(time1);
        LocalTime t2 = LocalTime.parse(time2);
        long minutesBetween = ChronoUnit.MINUTES.between(t1, t2);
        return minutesBetween % 15 == 0 && minutesBetween != 0;
    }

    /**
     * 原始15分钟维度拆分为15分钟计划
     *
     * @param originalPlans
     * @return
     */
    private List<CiesEnergyStoragePlanResponse> splitTo15MinIntervals(List<CiesEnergyStoragePlanResponse> originalPlans) {
        List<CiesEnergyStoragePlanResponse> result = new ArrayList<>();

        for (CiesEnergyStoragePlanResponse plan : originalPlans) {
            LocalTime start = LocalTime.parse(plan.getStartTime());
            LocalTime end = LocalTime.parse(plan.getEndTime());

            // 处理跨天情况（end <= start表示跨天）
            boolean isCrossDay = !end.isAfter(start);
            LocalTime currentStart = start;
            do {
                LocalTime currentEnd = currentStart.plusMinutes(15);

                // 如果当前段跨越午夜，需要截断到end时间
                if (isCrossDay && currentEnd.isBefore(currentStart))  {
                    currentEnd = end;
                }

                CiesEnergyStoragePlanResponse splitPlan = BeanCopyUtil.copyProperties(plan,  CiesEnergyStoragePlanResponse::new);
                splitPlan.setStartTime(currentStart.toString());
                splitPlan.setEndTime(currentEnd.toString());

                result.add(splitPlan);
                currentStart = currentEnd;

            } while (currentStart.isBefore(end)  || (isCrossDay && !currentStart.equals(end)));
        }
        return result;
    }

    /**
     * 原始1分钟维度拆分为1分钟计划
     *
     * @param originalPlans
     * @return
     */
    private List<CiesEnergyStoragePlanResponse> splitToMinuteIntervals1(List<CiesEnergyStoragePlanResponse> originalPlans) {
        return originalPlans.stream()
                .flatMap(plan -> {
                    LocalTime start = LocalTime.parse(plan.getStartTime());
                    LocalTime end = LocalTime.parse(plan.getEndTime());

                    // 处理跨天场景（end <= start时表示跨天）
                    boolean isCrossDay = !end.isAfter(start);
                    long durationMinutes = isCrossDay
                            ? ChronoUnit.MINUTES.between(start,  LocalTime.MAX) + 1  // 23:59→00:00算1分钟
                            + ChronoUnit.MINUTES.between(LocalTime.MIN,  end)
                            : ChronoUnit.MINUTES.between(start,  end);

                    // 生成时间流（自动处理跨天）
                    return Stream.iterate(start,  time -> {
                                LocalTime next = time.plusMinutes(1);
                                return next.equals(LocalTime.MIN)  ? null : next; // 00:00时终止
                            })
                            .limit(durationMinutes)
                            .map(minuteStart -> {
                                CiesEnergyStoragePlanResponse splitPlan = BeanCopyUtil.copyProperties(plan,  CiesEnergyStoragePlanResponse::new);
                                splitPlan.setStartTime(minuteStart.toString());
                                splitPlan.setEndTime(
                                        minuteStart.equals(LocalTime.MAX)
                                                ? LocalTime.MIN.toString()   // 23:59→00:00
                                                : minuteStart.plusMinutes(1).toString()
                                );
                                return splitPlan;
                            });
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据下发规则替换设点和计划功率
     *
     * @param originalPlans
     * @param connPointId
     * @param rule
     */
    private void coverPowerByRule(List<CiesEnergyStoragePlanResponse> originalPlans, String connPointId, CiesEsPlanRuleResponse rule) {
        CiesConnectionPointResponse connPointInfo = iCiesConnectionPointService.findConnByConnPointId(connPointId);
        String dispatchRule = rule.getDispatchRule();
        if (connPointInfo.getNum() == null || connPointInfo.getNum() == 0) {
            originalPlans.forEach(item -> item.setPlannedPower(BigDecimal.ZERO));
            return;
        }
        for (CiesEnergyStoragePlanResponse plan : originalPlans) {
            if (DispatchRuleEnum.EQUAL_POINT_DISPATCH.getCode().equals(dispatchRule)) {
                plan.setPlannedPower(plan.getPlannedPower().divide(new BigDecimal(connPointInfo.getNum()),
                        4,
                        RoundingMode.HALF_UP));
            }
        }
    }


    public Map<String, String> convertToMap(List<CiesSpecialIndicatorsResponse> specialIndicators) {
        Map<String, String> resultMap = new HashMap<>();

        if (specialIndicators == null || specialIndicators.isEmpty())  {
            return resultMap;
        }

        for (CiesSpecialIndicatorsResponse indicator : specialIndicators) {
            String key = indicator.getSpecialIndicatorsId();
            String value;

            if ("测点".equals(indicator.getRelateDataType()))  {
                // 格式: cId-equipId-relateIndicatorId
                value = String.join("-",
                        indicator.getCId(),
                        indicator.getEquipId(),
                        indicator.getRelateIndicatorId()
                );
            } else {
                // 其他类型直接取relateIndicatorId
                value = indicator.getRelateIndicatorId();
            }

            resultMap.put(key,  value);
        }
        return resultMap;
    }

    /**
     * 组装单个并网点计划报文（下发数据中台）
     * @param coverResultPlan
     * @param connId
     * @param map
     * @return
     */
    private JSONObject assemblyIssuePlan(List<CiesEnergyStoragePlanResponse> coverResultPlan, String connId, Map<String, String> map,CiesEsPlanRuleResponse rule) {
        JSONObject result = new JSONObject();
        List<BigDecimal> power = new ArrayList<>();
        for (CiesEnergyStoragePlanResponse item : coverResultPlan) {
            JSONObject jsonObject = new JSONObject();
            if (EnergyStateEnum.CHARGE.getCode().equals(item.getChargeDischargeType())){
                power.add(item.getPlannedPower().negate());
            }else{
                power.add(item.getPlannedPower());
            }
            jsonObject.put("chargeDischargeType", item.getChargeDischargeType());
        }
        result.put("val_array", power);
        // 获取初始时间
        LocalDate localDate = LocalDate.parse(coverResultPlan.get(0).getPlanDate().replace("/","-"));
        LocalDateTime midnight = localDate.atStartOfDay();
        // 获取毫秒数（系统默认时区）
        long milliseconds = midnight.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        result.put("start_timestamp", milliseconds);
        result.put("channelId", map.get(connId));
        CiesConnectionPointEntity byId = iCiesConnectionPointService.getById(connId);
        if (byId.getPlanStartId() != null) {
            result.put("planStartId", byId.getPlanStartId());
        }
        // 时间维度15min对应3  1min对应5
        result.put("plan_type", TimeDimensionEnum.MINUTES_15.getCode().equals(rule.getTimeDimension()) ? "3" : "5");
        return result;
    }
}
