package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;


@Data
@ToString
@Schema(description = "用户代购电-电价详情")
public class CiesMarketElecPriListResponse {

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "用电地区")
    private String elecArea;

    @Schema(description = "用电分类")
    private String elecType;

    @Schema(description = "行业类型")
    private String industryType;

    @Schema(description = "电压等级")
    private String voltageLevel;

    @Schema(description = "电价列表")
    private List<CiesMarketElecPriResponse> priResponseList;
}
