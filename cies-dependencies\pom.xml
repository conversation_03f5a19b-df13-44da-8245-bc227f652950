<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.bcels.cies</groupId>
    <artifactId>cies-dependencies</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>cies-dependencies</name>
    <properties>
        <java.version>17</java.version>
        <axis2.version>1.7.9</axis2.version>
        <guava.version>18.0</guava.version>
        <fastjson.version>1.2.83</fastjson.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-lang.version>3.9</commons-lang.version>
        <concurrentlinkedhashmap-lru.version>1.4.2</concurrentlinkedhashmap-lru.version>
        <swagger.version>2.9.2</swagger.version>
        <!--build-->
        <java.source.version>17</java.source.version>
        <java.target.version>17</java.target.version>
        <java.encoding>UTF-8</java.encoding>
        <maven.compiler.version>3.11.0</maven.compiler.version>
        <lombok.version>1.18.34</lombok.version>
        <recoup.version>4.5.2-03</recoup.version>
        <junit.version>4.12</junit.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <hibernate-validator.version>6.0.20.Final</hibernate-validator.version>
        <javax.validation-api.version>2.0.1.Final</javax.validation-api.version>


        <!--版本号-->
        <spring-security-rsa.version>1.1.5</spring-security-rsa.version>
        <google.guava.version>33.3.1-jre</google.guava.version>
        <easyexcel.version>3.3.4</easyexcel.version>
        <redisson.version>3.40.2</redisson.version>
        <arthas.version>3.7.2</arthas.version>
        <springdoc.version>2.6.0</springdoc.version>
        <fastjson.version>2.0.53</fastjson.version>
        <jsonwebtoken.version>0.12.6</jsonwebtoken.version>
        <woodstox-core.version>7.1.0</woodstox-core.version>
        <com.tencent.kona.version>1.0.11</com.tencent.kona.version>
        <browscap.version>1.4.4</browscap.version>
        <geoip2.version>4.2.1</geoip2.version>
        <screw-core.version>1.0.5</screw-core.version>
        <org.mapstruct.version>1.6.3</org.mapstruct.version>
        <java-otp.version>0.4.0</java-otp.version>
        <failsafe.version>3.3.2</failsafe.version>
        <bouncycastle.version>1.70</bouncycastle.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <qiniu-java-sdk.version>7.17.0</qiniu-java-sdk.version>
        <tencentcloud-sdk-java.version>3.1.1135</tencentcloud-sdk-java.version>
        <alibaba-dingtalk-service-sdk.version>2.0.0</alibaba-dingtalk-service-sdk.version>
        <dingtalk-sdk.version>2.1.77</dingtalk-sdk.version>
        <aliyun-sdk.version>3.1.0</aliyun-sdk.version>
        <spring-cloud-context.version>4.2.0</spring-cloud-context.version>
        <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
        <passay.version>1.6.6</passay.version>
        <spring-boot.version>3.2.12</spring-boot.version>
        <aliyun-sdk-oss.version>3.18.1</aliyun-sdk-oss.version>
        <tencent-cos-java-sdk.version>5.6.238.2</tencent-cos-java-sdk.version>
        <minio-java-sdk.version>8.5.14</minio-java-sdk.version>
        <cron-utils.version>9.2.1</cron-utils.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <nanoid.version>4.0.1</nanoid.version>
        <zxing.version>3.5.3</zxing.version>
        <reflections.version>0.10.2</reflections.version>
        <commons-io.version>2.18.0</commons-io.version>
        <commons-net.version>3.11.1</commons-net.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <commons-text.version>1.13.0</commons-text.version>
        <commons-codec.version>1.17.1</commons-codec.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <googlecode-libphonenumber.version>8.13.52</googlecode-libphonenumber.version>
        <easysdk-kernel.version>1.0.12</easysdk-kernel.version>
        <salvation2.version>3.0.1</salvation2.version>
        <software.amazon.awssdk.version>2.29.34</software.amazon.awssdk.version>
        <ip2region.version>2.7.0</ip2region.version>
        <caffeine.version>3.1.8</caffeine.version>
        <dom4j.version>2.1.4</dom4j.version>
        <!--插件版本-->
        <apt-maven-plugin.version>1.1.3</apt-maven-plugin.version>
        <formatter-maven-plugin.version>2.24.1</formatter-maven-plugin.version>
        <impsort-maven-plugin.version>1.12.0</impsort-maven-plugin.version>
        <license-maven-plugin.version>4.6</license-maven-plugin.version>
        <exec-maven-plugin>3.5.0</exec-maven-plugin>
        <jib-maven-plugin.version>3.4.4</jib-maven-plugin.version>
        <frontend-maven-plugin.version>1.15.0</frontend-maven-plugin.version>
        <spring-security-kerberos.version>2.0.0</spring-security-kerberos.version>
        <dingtalk-stream-client.version>1.3.2</dingtalk-stream-client.version>
    </properties>
    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2023.0.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2023.0.1.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>3.2.11</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.zwy</groupId>-->
<!--                <artifactId>common-bean</artifactId>-->
<!--                <version>1.0-SNAPSHOT</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.zwy</groupId>
                <artifactId>common-utils</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>3.1.0</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>3.5.11</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>3.5.11</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.11</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>3.5.11</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>3.5.11</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.30</version>
                <optional>true</optional>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.1.20</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.17</version>
            </dependency>

            <dependency>
                <groupId>org.apache.cxf</groupId>
                <artifactId>cxf-spring-boot-starter-jaxws</artifactId>
                <version>3.6.3</version>
            </dependency>
            <dependency>
                <artifactId>cxf-core</artifactId>
                <groupId>org.apache.cxf</groupId>
                <version>3.6.3</version>
            </dependency>

            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>3.3.0</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.concurrentlinkedhashmap</groupId>
                <artifactId>concurrentlinkedhashmap-lru</artifactId>
                <version>${concurrentlinkedhashmap-lru.version}</version>
            </dependency>

            <!-- Java High Level REST Client -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${javax.validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.22</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>1.9.6</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>3.3.0</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>1.71</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.13.0</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.4.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.4.1</version>
                <exclusions>
                    <exclusion>
                        <artifactId>httpclient</artifactId>
                        <groupId>org.apache.httpcomponents</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>2.1.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.10</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
                <scope>compile</scope>
            </dependency>

            <!-- amqp 1.0 qpid client -->
            <dependency>
                <groupId>org.apache.qpid</groupId>
                <artifactId>qpid-jms-client</artifactId>
                <version>0.57.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.12</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-kernel</artifactId>
                <version>1.7.9</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-transport-http</artifactId>
                <version>1.7.9</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-transport-local</artifactId>
                <version>1.7.9</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-corba</artifactId>
                <version>1.7.9</version>
            </dependency>

            <dependency>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-jaxws</artifactId>
                <version>1.7.9</version>
                <exclusions>
                    <exclusion>
                        <artifactId>geronimo-annotation_1.0_spec</artifactId>
                        <groupId>org.apache.geronimo.specs</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.zwy</groupId>
                <artifactId>influxdb-spring-boot-starter</artifactId>
                <version>2.0.2</version>
            </dependency>

            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-core-jakarta</artifactId>
                <version>2.2.22</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>2.2.22</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-models-jakarta</artifactId>
                <version>2.2.22</version>
            </dependency>

            <!-- caffeine -->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <!-- ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <!-- libphonenumber -->
            <dependency>
                <groupId>com.googlecode.libphonenumber</groupId>
                <artifactId>libphonenumber</artifactId>
                <version>${googlecode-libphonenumber.version}</version>
            </dependency>
            <!--salvation2-->
            <dependency>
                <groupId>com.shapesecurity</groupId>
                <artifactId>salvation2</artifactId>
                <version>${salvation2.version}</version>
            </dependency>
            <!-- zxing -->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>javase</artifactId>
                <version>${zxing.version}</version>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <!-- commons-codec -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cronutils</groupId>
                <artifactId>cron-utils</artifactId>
                <version>${cron-utils.version}</version>
            </dependency>
            <!-- dom4j -->
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>${dom4j.version}</version>
            </dependency>
            <!-- dingtalk -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>${dingtalk-sdk.version}</version>
            </dependency>
            <!--passay-->
            <dependency>
                <groupId>org.passay</groupId>
                <artifactId>passay</artifactId>
                <version>${passay.version}</version>
            </dependency>
            <!-- transmittable-thread-local -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <!-- spring-cloud-context -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-context</artifactId>
                <version>${spring-cloud-context.version}</version>
            </dependency>
            <!--   browscap     -->
            <dependency>
                <groupId>com.blueconic</groupId>
                <artifactId>browscap-java</artifactId>
                <version>${browscap.version}</version>
            </dependency>
            <!-- geoip2 -->
            <dependency>
                <groupId>com.maxmind.geoip2</groupId>
                <artifactId>geoip2</artifactId>
                <version>${geoip2.version}</version>
            </dependency>
            <!--guava-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.guava.version}</version>
            </dependency>
            <!-- fastjson -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension-spring6</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--springdoc-->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <!--mapstruct-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.eatthepath</groupId>
                <artifactId>java-otp</artifactId>
                <version>${java-otp.version}</version>
            </dependency>
            <dependency>
                <groupId>dev.failsafe</groupId>
                <artifactId>failsafe</artifactId>
                <version>${failsafe.version}</version>
            </dependency>
            <!--  bouncycastle -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcutil-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>
            <!--JWT-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jsonwebtoken.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jsonwebtoken.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jsonwebtoken.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- jackson-dataformat-xml -->

            <dependency>
                <groupId>com.fasterxml.woodstox</groupId>
                <artifactId>woodstox-core</artifactId>
                <version>${woodstox-core.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
        </plugins>
    </build>

<!--    <profiles>-->
<!--        <profile>-->
<!--            <id>test</id>-->
<!--            <properties>-->
<!--                <env>test</env>-->
<!--                <repositories.url>https://mvn.vpptech.cn/repository/test-releases</repositories.url>-->
<!--                <snapshot.repositories.url>https://mvn.vpptech.cn/repository/test-snapshots</snapshot.repositories.url>-->
<!--            </properties>-->
<!--        </profile>-->
<!--        <profile>-->
<!--            <id>prod</id>-->
<!--            <properties>-->
<!--                <env>prod</env>-->
<!--                <repositories.url>https://mvn.vpptech.cn/repository/prod-releases</repositories.url>-->
<!--                <snapshot.repositories.url>https://mvn.vpptech.cn/repository/prod-snapshots</snapshot.repositories.url>-->
<!--            </properties>-->
<!--        </profile>-->
<!--    </profiles>-->


<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>zwy-releases</id>-->
<!--            <name>maven-releases</name>-->
<!--            <url>${repositories.url}</url>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>zwy-snapshots</id>-->
<!--            <name>maven-snapshots</name>-->
<!--            <url>${snapshot.repositories.url}</url>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->
</project>
