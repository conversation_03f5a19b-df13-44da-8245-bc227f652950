package com.bcels.cies.infrastructure.config;

/**
 * 用户上下文工具类（基于ThreadLocal实现线程隔离）
 */
public class CiesUserContext {
    // 使用ThreadLocal存储当前线程的用户信息
    private static final ThreadLocal<String> CURRENT_USER = new ThreadLocal<>();

    /**
     * 设置当前用户
     */
    public static void setCurrentUser(String userName) {
        CURRENT_USER.set(userName);
    }

    /**
     * 获取当前用户
     */
    public static String getCurrentUser() {
        return CURRENT_USER.get();
    }

    /**
     * 清除当前用户（防止内存泄漏）
     */
    public static void clear() {
        CURRENT_USER.remove();
    }
}