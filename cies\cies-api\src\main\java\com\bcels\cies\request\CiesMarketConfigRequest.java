package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "用户代购电检索条件")
public class CiesMarketConfigRequest extends PageRequest implements Serializable {

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "用电地区")
    private String elecArea;

    @Schema(description = "用电类型")
    private String elecType;

    @Schema(description = "电压等级")
    private String voltageLevel;

    @Schema(description = "行业类型")
    private String industryType;

    @Schema(description = "年-月")
    private String month;

    @Schema(description = "年")
    private Integer year;

    @Schema(description = "应用全年：1,应用本月：2")
    private String useType;

    @Schema(description = "上传文件名称")
    private String fileName;

    @Schema(description = "数据记录id")
    private String sourceId;

    @Schema(description = "目标记录id")
    private String targetId;
}
