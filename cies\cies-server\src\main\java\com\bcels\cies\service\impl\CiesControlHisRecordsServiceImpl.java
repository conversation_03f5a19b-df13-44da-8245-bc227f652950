package com.bcels.cies.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.repository.entity.CiesControlHisRecordsEntity;
import com.bcels.cies.repository.mapper.CiesControlHisRecordsMapper;
import com.bcels.cies.request.CiesControlHisRequest;
import com.bcels.cies.response.CiesControlHisRecordsListResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import com.bcels.cies.service.ICiesControlHisRecordsService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.bean.PageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class CiesControlHisRecordsServiceImpl extends ServiceImpl<CiesControlHisRecordsMapper, CiesControlHisRecordsEntity> implements ICiesControlHisRecordsService {


    @Autowired
    private CiesControlHisRecordsMapper ciesControlHisRecordsMapper;
    @Override
    public PageResponse<CiesControlHisRecordsListResponse> findControlHisRecordsForPage(CiesControlHisRequest request) {
        Page<CiesControlHisRecordsListResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesControlHisRecordsListResponse> pageResult = ciesControlHisRecordsMapper.findControlHisRecordsForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }

    @Override
    public List<CiesControlHisRecordsListResponse> findControlHisRecord(String projectId, LocalDateTime current) {
        QueryWrapper<CiesControlHisRecordsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.gt("create_time",current);
        queryWrapper.orderByAsc("create_time").last("LIMIT 1");
        List<CiesControlHisRecordsEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return BeanCopyUtil.copyListProperties(list, CiesControlHisRecordsListResponse::new);
    }
}
