package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.repository.entity.CiesEsPlanRuleEntity;
import com.bcels.cies.repository.mapper.CiesConnectionPointMapper;
import com.bcels.cies.response.CiesConnectionPointResponse;
import com.bcels.cies.service.ICiesConnectionPointService;
import com.bcels.cies.repository.entity.CiesConnectionPointEntity;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.enums.YesOrNo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Comparator;
import java.util.List;

/**
 * <p>
 * 并网点信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Service
public class CiesConnectionPointServiceImpl extends ServiceImpl<CiesConnectionPointMapper, CiesConnectionPointEntity> implements ICiesConnectionPointService {


    @Autowired
    private CiesConnectionPointMapper ciesConnectionPointMapper;

    @Override
    public List<CiesConnectionPointResponse> findByChannelId(List<String> channelIds) {
        QueryWrapper<CiesConnectionPointEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("channel_id ",channelIds);
        queryWrapper.isNull("history_record_id");
        List<CiesConnectionPointEntity> pointEntities=  list(queryWrapper);

        if (CollectionUtils.isEmpty(pointEntities)) {
            return null;
        }
        return BeanCopyUtil.copyListProperties(pointEntities, CiesConnectionPointResponse::new);
    }
    @Override
    public List<CiesConnectionPointResponse> findConnByProjectIds(List<String> projectIds) {
       return ciesConnectionPointMapper.findConnByProjectIds(projectIds);
    }

    @Override
    public List<CiesConnectionPointResponse> findByrHisRecordId(String hisRecordId) {
        QueryWrapper<CiesConnectionPointEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("history_record_id ",hisRecordId);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        List<CiesConnectionPointEntity> pointEntities=  list(queryWrapper);

        if (CollectionUtils.isEmpty(pointEntities)) {
            return null;
        }
        pointEntities.sort(Comparator.comparingInt(CiesConnectionPointEntity::getSortOrder));
        return BeanCopyUtil.copyListProperties(pointEntities, CiesConnectionPointResponse::new);
    }

    @Override
    public CiesConnectionPointResponse findConnByConnPointId(String connPointId) {
        QueryWrapper<CiesConnectionPointEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("connection_point_id ",connPointId);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        return BeanCopyUtil.copyProperties(getOne(queryWrapper),CiesConnectionPointResponse::new);
    }

    @Override
    public List<CiesConnectionPointResponse> findConnInfoByIds(List<String> connIds) {
        QueryWrapper<CiesConnectionPointEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("connection_point_id ", connIds);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        List<CiesConnectionPointEntity> list = list(queryWrapper);
        return BeanCopyUtil.copyListProperties(list, CiesConnectionPointResponse::new);
    }
}
