package com.zwy.common.utils.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class PageResponse<T> implements Serializable {

    private long total;

    private long page;

    private long pageSize;

    private List<T> pageData;
}
