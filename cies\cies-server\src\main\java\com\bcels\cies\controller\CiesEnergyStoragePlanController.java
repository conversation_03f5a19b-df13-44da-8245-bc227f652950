package com.bcels.cies.controller;

import com.bcels.cies.api.CiesEnergyStorgePlanApi;
import com.bcels.cies.domain.CiesEnergyPlanDomainService;
import com.bcels.cies.request.*;
import com.bcels.cies.response.*;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/storgePlan/v1")
public class CiesEnergyStoragePlanController implements CiesEnergyStorgePlanApi {

    @Autowired
    private CiesEnergyPlanDomainService ciesEnergyPlanDomainService;
    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesEnergyPlanListResponse>> findForPage(@RequestBody CiesEnergyPlanListRequest request) {
        return ResultData.success(ciesEnergyPlanDomainService.findEnergyPlanForPage(request));
    }

    @PostMapping("exportPlanList")
    public ResultData<Void> exportEnergyPlanList(@RequestBody CiesEnergyPlanListRequest request, HttpServletResponse response) throws IOException {
        ciesEnergyPlanDomainService.exportEnergyPlanList(request,response);
        return ResultData.success();
    }

    @PostMapping("init")
    public ResultData<CiesEnergyPlanDetailResponse> initEnergyPlan(@RequestBody CiesEnergyPlanListRequest request) {
        return ResultData.success(ciesEnergyPlanDomainService.initEnergyPlan(request));
    }

    @PostMapping("saveStationPlan")
    public ResultData<Void> saveFullStationPlan(@RequestBody CiesStationPlanRequest request) {
        ciesEnergyPlanDomainService.saveFullStationPlan(request);
        return ResultData.success();
    }

    @PostMapping("findStationPlan")
    public ResultData<CiesFullStationResponse> findFullStationPlan(@RequestBody CiesQueryStationPlanRequest request) {
        CiesFullStationResponse fullStationPlan = ciesEnergyPlanDomainService.findFullStationPlan(request);
        return ResultData.success(fullStationPlan);
    }

    @PostMapping("saveConnPointPlan")
    public ResultData<Void> saveCollPointPlan(@RequestBody CiesConnPointPlanRequest request) {
        ciesEnergyPlanDomainService.saveCollPointPlan(request);
        return ResultData.success();
    }
    @PostMapping("findConnPointPlan")
    public ResultData<CiesInitConnPlanResponse>  getConnPlan(@RequestBody CiesQueryStationPlanRequest request){
        return ResultData.success(ciesEnergyPlanDomainService.getConnPlan(request));
    }

    @PostMapping("findPlanDetail")
    public ResultData<CiesEnergyPlanDetailResponse>  findEnergyPlanDetail(@RequestBody CiesEnergyPlanListRequest request){
        return ResultData.success(ciesEnergyPlanDomainService.findEnergyPlanDetail(request));
    }

    @PostMapping("reviewPlan")
    public ResultData<Void> reviewPlan(@RequestBody CiesAuditOperateRequest request) {
        ciesEnergyPlanDomainService.reviewPlan(request);
        return ResultData.success();
    }

    @PostMapping("recalculatePlan")
    public ResultData<Void> recalculatePlan(@RequestBody CiesAuditOperateRequest request){
        ciesEnergyPlanDomainService.batchRecalculatePlan(request);
        return ResultData.success();
    }
}
