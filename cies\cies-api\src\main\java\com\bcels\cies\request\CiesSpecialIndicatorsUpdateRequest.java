package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "特殊指标")
public class CiesSpecialIndicatorsUpdateRequest extends PageRequest implements Serializable {

    @Schema(description = "特殊指标关联主键")
    private String specialIndicatorsId;

    @Schema(description = "关联数据类型")
    private String relateDataType;

    @Schema(description = "通道名称")
    private String channelName;

    @Schema(description = "通道ID")
    private String cId;

    @Schema(description = "设备名称")
    private String equipName;

    @Schema(description = "设备ID")
    private String equipId;

    @Schema(description = "关联指标名称")
    private String relateIndicatorName;

    @Schema(description = "关联指标ID")
    private String relateIndicatorId;

    @Schema(description = "业务指标名称")
    private String indicatorName;

    @Schema(description = "项目主键")
    private String projectId;
}
