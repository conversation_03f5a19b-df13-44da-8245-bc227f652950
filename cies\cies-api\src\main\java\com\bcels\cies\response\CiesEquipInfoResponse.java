package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "设备信息")
public class CiesEquipInfoResponse implements Serializable {

    @Schema(description = "设备主键")
    private String equipId;

    @Schema(description = "设备名称")
    private String equipName;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "等级")
    private String level;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "上级设备ID")
    private String parentEquipId;

    @Schema(description = "上级设备名称")
    private String parentEquipName;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;
}
