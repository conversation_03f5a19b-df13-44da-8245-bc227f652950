package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "测点与指标")
public class CiesIndicatorsInfoUpdateRequest extends PageRequest implements Serializable {


    @Schema(description = "指标主键")
    private String indicatorId;

    
    @Schema(description = "页面展示名称")
    private String pageDisplayName;

    @Schema(description = "分类内排序")
    private Integer sort;

    @Schema(description = "是否显示(0:不显示 1:显示)")
    private Integer isShow;

    @Schema(description = "单位")
    private String unit;
}
