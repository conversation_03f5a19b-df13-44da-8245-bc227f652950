package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Schema(description = "储能计划检索条件")
public class CiesEnergyPlanListRequest extends PageRequest implements Serializable {

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "企业id")
    private String enterpriseId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "计划审核状态")
    private String auditStatus;

    @Schema(description = "下发状态")
    private String dispatchStatus;

    @Schema(description = "计划开始日期")
    private String planStartTime;

    @Schema(description = "计划结束日期")
    private String planEndTime;

    @Schema(description = "并网点id")
    private String connectionPointId;

    @Schema(description = "审核历史id")
    private String historyRecordId;

    @Schema(description = "计划日期")
    private String planDate;

    @Schema(description = "计划日期列表")
    private List<String> planDateList;
}
