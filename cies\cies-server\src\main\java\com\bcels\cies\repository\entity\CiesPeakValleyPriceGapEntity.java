package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@TableName("cies_peak_valley_price_gap")
@ApiModel(value = "CiesPeakValleyPriceGapEntity对象", description = "储能峰谷价差信息")
public class CiesPeakValleyPriceGapEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId
    private String priceGapId;

    @ApiModelProperty("统计阶段")
    private String statPeriod;

    @ApiModelProperty("时段")
    private String timeRange;

    @ApiModelProperty("上月读数")
    private BigDecimal lastMonthRead;

    @ApiModelProperty("本月读数")
    private BigDecimal currentMonthRead;

    @ApiModelProperty("抄见电量（kwh）")
    private BigDecimal meteredUsage;

    @ApiModelProperty("结算电量（kwh）")
    private BigDecimal billedUsage;

    @ApiModelProperty("电价（元/kwh）")
    private BigDecimal unitPrice;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("特殊指标关联主键")
    private String specialIndicatorsId;

    @ApiModelProperty("并网点主键")
    private String connectionPointId;

    @ApiModelProperty("储能结算主键")
    private String essBillId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}

