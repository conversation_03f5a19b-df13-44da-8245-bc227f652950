package com.bcels.cies.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimeRange {
    private LocalTime start;
    private LocalTime end;

    /**
     * 重写日期转化格式
     * @return
     */
    @Override
    public String toString() {
        return String.format("%02d:%02d-%02d:%02d",
                start.getHour(),  start.getMinute(),
                end.getHour(),  end.getMinute());
    }
}
