package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.repository.entity.CiesControlHisRecordsEntity;
import com.bcels.cies.repository.entity.CiesDemandDistributeRecordEntity;
import com.bcels.cies.repository.mapper.CiesDemandDistributeRecordMapper;
import com.bcels.cies.request.CiesDemandRequest;
import com.bcels.cies.response.CiesControlHisRecordsListResponse;
import com.bcels.cies.response.CiesDemandResponse;
import com.bcels.cies.service.ICiesDemandDistributeRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.bean.PageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class CiesDemandDistributeRecordServiceImpl extends ServiceImpl<CiesDemandDistributeRecordMapper, CiesDemandDistributeRecordEntity> implements ICiesDemandDistributeRecordService {


    @Autowired
    private CiesDemandDistributeRecordMapper ciesDemandDistributeRecordMapper;
    @Override
    public CiesDemandResponse findLastDemand(String connPointId) {
        QueryWrapper<CiesDemandDistributeRecordEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("connection_point_id", connPointId);
        queryWrapper.orderByDesc("create_time");
        queryWrapper.last("LIMIT 1");
        List<CiesDemandDistributeRecordEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyProperties(list.get(0),CiesDemandResponse::new);
    }

    @Override
    public PageResponse<CiesDemandResponse> findDemandRecordsForPage(CiesDemandRequest request) {
        Page<CiesDemandResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesDemandResponse> pageResult = ciesDemandDistributeRecordMapper.findDemandRecordsForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }
}
