package com.bcels.cies.controller;

import com.bcels.cies.domain.CiesOperationLogService;
import com.bcels.cies.repository.entity.CiesOperationLogEntity;
import com.bcels.cies.request.CiesOperationLogRequest;
import com.bcels.cies.response.CiesOperationLogResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ciesOperationLog/v1")
public class CiesOperationLogController {

    @Autowired
    private CiesOperationLogService operationLogService;

    @PostMapping("findOperationLog")
    public ResultData<PageResponse<CiesOperationLogResponse>> findOperationLog(@RequestBody CiesOperationLogRequest request) {
        return ResultData.success(operationLogService.findOperationLog(request));
    }

    @PostMapping("saveOpeationLog")
    public ResultData<Void> saveOpeationLog(@RequestBody CiesOperationLogEntity ciesOperationLog) {
        operationLogService.saveOpeationLog(ciesOperationLog);
        return ResultData.success();
    }
}
