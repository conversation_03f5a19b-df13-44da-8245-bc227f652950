package com.bcels.cies.controller;

import com.alibaba.fastjson.JSONObject;
import com.bcels.cies.api.CiesMarketInfoApi;
import com.bcels.cies.domain.CiesMarketDomainService;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesMarketElecPriRecordRequest;
import com.bcels.cies.request.CiesMarketStageRecordRequest;
import com.bcels.cies.response.CiesMarketElecPriListResponse;
import com.bcels.cies.response.CiesMarketListResponse;
import com.bcels.cies.response.CiesMarketStageListResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;

@RestController
@RequestMapping("/marketConfig/v1")
public class CiesMarketConfigController implements CiesMarketInfoApi {


    @Autowired
    private CiesMarketDomainService ciesMarketDomainService;
    @Override
    @PostMapping("page")
    public ResultData<PageResponse<CiesMarketListResponse>> findForPage(@RequestBody CiesMarketConfigRequest request) {
        return ResultData.success(ciesMarketDomainService.findForPage(request));
    }

    @Override
    @PostMapping("export")
    public ResultData<Void> exportPage(@RequestBody CiesMarketConfigRequest request, HttpServletResponse response) throws IOException {
        ciesMarketDomainService.exportPage(request,response);
        return ResultData.success();
    }

    @Override
    @PostMapping("showGraphics")
    public ResultData<Map<Double, BigDecimal>> showGraphics(@RequestBody CiesMarketConfigRequest request) {
        Map<Double, BigDecimal> result = ciesMarketDomainService.showGraphics(request);
        return ResultData.success(result);
    }

    @Override
    @PostMapping("showPrice")
    public ResultData<CiesMarketElecPriListResponse> findElecPriByYear(@RequestBody CiesMarketConfigRequest request) {
        CiesMarketElecPriListResponse ciesMarketElecPriListResponse = ciesMarketDomainService.findElecPriByYear(request);
        return ResultData.success(ciesMarketElecPriListResponse);
    }

    @Override
    @PostMapping("addElecPrice")
    public ResultData<Void> addElecPriByYear(@RequestBody CiesMarketElecPriRecordRequest request) {
        ciesMarketDomainService.addElecPriByYear(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("showStage")
    public ResultData<CiesMarketStageListResponse> findStageByYear(@RequestBody CiesMarketConfigRequest request) {
        CiesMarketStageListResponse stageListResponse = ciesMarketDomainService.findStageByYear(request);
        return ResultData.success(stageListResponse);
    }

    @Override
    @PostMapping("addStage")
    public ResultData<Void> addStageByYear(@RequestBody CiesMarketStageRecordRequest request) {
        ciesMarketDomainService.addStageByYear(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("exportStage")
    public ResultData<Void> exportStageList(@RequestBody CiesMarketConfigRequest request, HttpServletResponse response) throws IOException {
        ciesMarketDomainService.exportStageList(request,response);
        return ResultData.success();
    }

    @Override
    @PostMapping("upload")
    public ResultData<String> upload(@RequestPart("file") MultipartFile file){
        return ResultData.success(ciesMarketDomainService.upload(file));
    }

    @Override
    @PostMapping("parseExcel")
    public ResultData<Void> parseExcel(@RequestBody CiesMarketConfigRequest request){
       ciesMarketDomainService.batchParseExcel(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("queryMarketInfo")
    public ResultData<JSONObject> queryMarketInfo(@RequestBody CiesMarketConfigRequest request){
        return ResultData.success(ciesMarketDomainService.findMarketData(request));
    }

    @Override
    @PostMapping("useLastPrice")
    public ResultData<Void> useLastPrice(CiesMarketConfigRequest request) {
        ciesMarketDomainService.useLastPrice(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("usePeriod")
    public ResultData<Void> marketPeriodCorrelation(@RequestBody CiesMarketConfigRequest request){
        ciesMarketDomainService.marketPeriodCorrelation(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("downloadTemplate")
    public ResultData<Void> downloadTemplate(HttpServletResponse response){
        ciesMarketDomainService.downloadTemplate(response);
        return ResultData.success();
    }
}
