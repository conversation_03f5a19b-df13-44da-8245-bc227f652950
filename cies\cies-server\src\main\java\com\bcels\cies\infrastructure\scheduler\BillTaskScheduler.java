package com.bcels.cies.infrastructure.scheduler;

import com.bcels.cies.emuns.SettlementBillStatusEnum;
import com.bcels.cies.request.CiesEssMonthlyBillIncomeRequest;
import com.bcels.cies.response.CiesEssMonthlyBillResponse;
import com.bcels.cies.service.ICiesEssMonthlyBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Service
public class BillTaskScheduler {

    @Autowired
    private ICiesEssMonthlyBillService iCiesEssMonthlyBillService;
    private final ScheduledExecutorService executor = Executors.newScheduledThreadPool(4);

    @PostConstruct
    public void init() {
        // 服务启动时，先将时间已经满足条件的数据状态修改
        List<CiesEssMonthlyBillResponse> billByStatus = iCiesEssMonthlyBillService.getBillByStatus(SettlementBillStatusEnum.APPEALED.getCode());
        LocalDateTime now = LocalDateTime.now();

        // 拆分逻辑
        List<String> expiredBills = billByStatus.stream()
                .filter(bill -> bill.getDisputeCountdown()  != null)
                .filter(bill -> !now.isBefore(bill.getDisputeCountdown()))
                .map(CiesEssMonthlyBillResponse::getEssBillId)
                .toList();
        // 如果服务中断期间，倒计时结束，需要修改状态
        iCiesEssMonthlyBillService.batchUpdateMonthlyBill(expiredBills,SettlementBillStatusEnum.CONFIRMED.getCode());

        List<CiesEssMonthlyBillResponse> activeBills = billByStatus.stream()
                .filter(bill -> bill.getDisputeCountdown()  != null)
                .filter(bill -> now.isBefore(bill.getDisputeCountdown()))
                .toList();
        // 加载未完成任务
        activeBills.forEach(this::scheduleTask);
    }

    /**
     * 添加任务
     * @param task
     */
    public void addTask(CiesEssMonthlyBillResponse task) {
        scheduleTask(task);
    }

    private void scheduleTask(CiesEssMonthlyBillResponse task) {
        long delay = Duration.between(LocalDateTime.now(),  task.getDisputeCountdown()).toSeconds();
        if (delay >= 0) {
            executor.schedule(() -> {
                CiesEssMonthlyBillIncomeRequest request = new CiesEssMonthlyBillIncomeRequest();
                request.setEssBillId(task.getEssBillId());
                request.setUpdateBy("服务器");
                request.setSettlementStatus(SettlementBillStatusEnum.CONFIRMED.getCode());
                // 如果满足时间，自动清空时间并修改结算单状态
                request.setDisputeCountdown(null);
                iCiesEssMonthlyBillService.updateMonthlyBill(request);
            }, delay, TimeUnit.SECONDS);
        }
    }
}
