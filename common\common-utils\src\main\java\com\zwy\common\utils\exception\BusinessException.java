package com.zwy.common.utils.exception;

import com.zwy.common.utils.bean.ErrorCode;
import lombok.Getter;

/**
 * @Author: J.T.
 * @Date: 2021/10/12 11:18
 * @Version 1.0
 * 业务异常
 */

public class BusinessException extends RuntimeException{

    @Getter
    private ErrorCode errorCode;

    public BusinessException() {
        super();
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(Throwable cause) {
        super(cause);
    }

    public BusinessException(ErrorCode errorCode) {
        super();
        this.errorCode = errorCode;
    }

    public BusinessException(String message,ErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    public BusinessException(String message,Integer errorCode) {
        ErrorCode errorCodeObject=new ErrorCode() {
            @Override
            public int getCode() {
                return errorCode;
            }
            @Override
            public String getMessage() {
                return message;
            }
        };
        this.errorCode = errorCodeObject;
    }

    public BusinessException(String message,ErrorCode errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public BusinessException(Throwable cause,ErrorCode errorCode) {
        super(cause);
        this.errorCode = errorCode;
    }


}
