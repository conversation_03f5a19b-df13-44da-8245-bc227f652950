package com.bcels.cies.repository.mapper;

import com.bcels.cies.repository.entity.CiesConnectionPointEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.response.CiesConnectionPointResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 并网点信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Mapper
public interface CiesConnectionPointMapper extends BaseMapper<CiesConnectionPointEntity> {

    List<CiesConnectionPointResponse> findConnByProjectIds(@Param("projectIds") List<String> projectIds);

}
