package com.bcels.cies;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableFeignClients(basePackages = {"com.bcels.cies"})
@EnableScheduling
@EnableDiscoveryClient
@EnableAsync
@SpringBootApplication(exclude = SecurityAutoConfiguration.class)
public class CiesApplication {

	public static void main(String[] args) {
		SpringApplication.run(CiesApplication.class, args);
	}

}
