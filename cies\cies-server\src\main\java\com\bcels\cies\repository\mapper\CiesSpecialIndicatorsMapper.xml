<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesSpecialIndicatorsMapper">

    <select id="findSpecialIndicatorsForPage"
            resultType="com.bcels.cies.response.CiesSpecialIndicatorsResponse">
        select ind.*,enter.enterprise_name,pro.pro_name
        from cies_special_indicators ind
        left join cies_project_info pro on pro.project_id=ind.project_id and ind.dr=0
        inner join cies_enterprise_info enter on enter.enterprise_id=pro.enterprise_id
        <where>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND pro.project_id  = #{request.projectId}
            </if>
            <if test="request.enterpriseName  != null and request.enterpriseName  != ''">
                AND enter.enterprise_name  = #{request.enterpriseName}
            </if>

            <if test="request.indicatorName  != null and request.indicatorName  != ''">
                AND ind.indicator_name LIKE CONCAT('%', #{request.indicatorName},  '%')
            </if>
            <if test="request.relateDataType  != null and request.relateDataType  != ''">
                AND ind.relate_data_type LIKE CONCAT('%', #{request.relateDataType},  '%')
            </if>
            <if test="request.dataName  != null and request.dataName  != ''">
            AND ind.data_name LIKE CONCAT('%', #{request.dataName},  '%')
            </if>
        </where>
        order by pro.pro_name,ind.create_time desc,ind.indicator_name,ind.relate_data_type
    </select>
    <select id="findSpecialIndicatorsById" resultType="com.bcels.cies.response.CiesSpecialIndicatorsResponse">
        select ind.*,enter.enterprise_name,pro.pro_name
        from cies_special_indicators ind
                 join cies_equip_info equip on equip.equip_id = ind.equip_id
                 join cies_project_info pro on pro.project_id=ind.project_id
                 join cies_enterprise_info enter on enter.enterprise_id=pro.enterprise_id
        where ind.special_indicators_id = #{specialIndicatorsId}
    </select>
</mapper>
