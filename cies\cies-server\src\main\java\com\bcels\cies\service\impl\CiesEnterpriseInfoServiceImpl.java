package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.repository.entity.CiesEnterpriseInfoEntity;
import com.bcels.cies.repository.mapper.CiesEnterpriseInfoMapper;
import com.bcels.cies.request.CiesEnterpriseInfoRequest;
import com.bcels.cies.response.CiesEnterpriseInfoResponse;
import com.bcels.cies.service.ICiesEnterpriseInfoService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.IdGenUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import com.zwy.common.utils.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


@Service
public class CiesEnterpriseInfoServiceImpl extends ServiceImpl<CiesEnterpriseInfoMapper, CiesEnterpriseInfoEntity> implements ICiesEnterpriseInfoService {


    @Autowired
    private CiesEnterpriseInfoMapper mapper;

    @Override
    public PageResponse<CiesEnterpriseInfoResponse> findEnterpriseForPage(CiesEnterpriseInfoRequest request) {
        QueryWrapper<CiesEnterpriseInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(request.getEnterpriseName()),"enterprise_name",request.getEnterpriseName());
        wrapper.between(StringUtils.isNotBlank(request.getStartTime()) && StringUtils.isNotBlank(request.getEndTime()), "create_time", request.getStartTime(), request.getEndTime());
        wrapper.orderByDesc("create_time");
        IPage<CiesEnterpriseInfoEntity> pageResult = page(new Page<>(request.getPage(), request.getPageSize()), wrapper);
        List<CiesEnterpriseInfoResponse> projectInfoList = BeanCopyUtil.copyListProperties(pageResult.getRecords(), CiesEnterpriseInfoResponse::new);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), projectInfoList);
    }

    @Override
    public void updateEnterpriseInfo(CiesEnterpriseInfoRequest request) {
        if (request.getFlag() == null){
            if(verifyName(request.getEnterpriseName(),request.getEnterpriseId())){
                throw new BusinessException("企业名称已存在！");
            }
        }
        LambdaUpdateWrapper<CiesEnterpriseInfoEntity> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CiesEnterpriseInfoEntity::getEnterpriseId,request.getEnterpriseId())
                .set(StringUtils.isNotEmpty(request.getEnterpriseName()),CiesEnterpriseInfoEntity::getEnterpriseName,request.getEnterpriseName())
                .set(StringUtils.isNotEmpty(request.getEnterpriseDes()),CiesEnterpriseInfoEntity::getEnterpriseDes,request.getEnterpriseDes())
                .set(request.getProjectNum() != null,CiesEnterpriseInfoEntity::getProjectNum,request.getProjectNum())
                .set(CiesEnterpriseInfoEntity::getUpdateTime,LocalDateTime.now())
                .set(CiesEnterpriseInfoEntity::getUpdateBy, CiesUserContext.getCurrentUser());
        mapper.update(null,wrapper);
    }

    @Override
    public void addEnterpriseInfo(CiesEnterpriseInfoRequest request) {
        if(verifyName(request.getEnterpriseName(),null)){
            throw new BusinessException("企业名称已存在！");
        }
        CiesEnterpriseInfoEntity ciesEnterpriseInfoEntity = BeanCopyUtil.copyProperties(request, CiesEnterpriseInfoEntity::new);
        ciesEnterpriseInfoEntity.setEnterpriseId(IdGenUtil.genUniqueId());
        ciesEnterpriseInfoEntity.setCreateBy(CiesUserContext.getCurrentUser());
        ciesEnterpriseInfoEntity.setCreateTime(LocalDateTime.now());
        ciesEnterpriseInfoEntity.setDr(YesOrNo.NO.getCode());
        ciesEnterpriseInfoEntity.setProjectNum(0);
        this.save(ciesEnterpriseInfoEntity);
    }

    @Override
    public List<CiesEnterpriseInfoResponse> findEnterprise(String enterpriseName) {
        QueryWrapper<CiesEnterpriseInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(enterpriseName),"enterprise_name",enterpriseName)
                .last("LIMIT 10");
        List<CiesEnterpriseInfoEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyListProperties(list,CiesEnterpriseInfoResponse::new);
    }

    @Override
    public List<CiesEnterpriseInfoResponse> findEnergyEnterprise(String projectType, String enterpriseName) {
        return mapper.findEnergyEnterprise(enterpriseName, projectType);
    }

    private boolean verifyName(String enterpriseName, String enterpriseId) {
        QueryWrapper<CiesEnterpriseInfoEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("enterprise_name", enterpriseName);
        wrapper.notIn(StringUtils.isNotEmpty(enterpriseId), "enterprise_id", enterpriseId);
        return getOne(wrapper) != null;
    }
}
