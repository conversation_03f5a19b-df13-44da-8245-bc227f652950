package com.bcels.cies.domain;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import com.bcels.cies.client.CiesInternalInterfaceClient;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.repository.entity.CiesDemandDistributeRecordEntity;
import com.bcels.cies.request.CiesDemandListRequest;
import com.bcels.cies.request.CiesDemandRequest;
import com.bcels.cies.response.*;
import com.bcels.cies.service.ICiesConnectionPointService;
import com.bcels.cies.service.ICiesDemandDistributeRecordService;
import com.bcels.cies.service.ICiesEsPlanRuleService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.IdGenUtil;
import com.zwy.common.utils.bean.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CiesDemandRecordDomainService {

    @Autowired
    private ICiesEsPlanRuleService iCiesEsPlanRuleService;

    @Autowired
    private ICiesConnectionPointService iCiesConnectionPointService;

    @Autowired
    private ICiesDemandDistributeRecordService iCiesDemandDistributeRecordService;

    @Autowired
    private CiesInternalInterfaceClient client;

    /**
     * 查询下发列表
     * @param request
     * @return
     */
    public CiesDemandDistributeListResponse findDemandList(CiesDemandRequest request) {
        CiesDemandDistributeListResponse response = new CiesDemandDistributeListResponse();
        if (StringUtils.isEmpty(request.getProjectId())) {
            return response;
        }
        List<CiesDemandResponse> list = new ArrayList<>();
        // 查询下发方式
        CiesEsPlanRuleResponse ciesEsPlanRuleResponse = iCiesEsPlanRuleService.findByProjectId(request.getProjectId());
        response.setCommandType(ciesEsPlanRuleResponse.getCommandType());

        // 获取列表
        List<String> list1 = new ArrayList<>();
        list1.add(request.getProjectId());
        List<CiesConnectionPointResponse> connPointList = iCiesConnectionPointService.findConnByProjectIds(list1);
        connPointList.sort(Comparator.comparing(CiesConnectionPointResponse::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));
        connPointList.forEach(conn -> {
            CiesDemandResponse item = new CiesDemandResponse();
            CiesDemandResponse lastDemand = iCiesDemandDistributeRecordService.findLastDemand(conn.getConnectionPointId());
            if (!Objects.isNull(lastDemand)){
                item.setDistributeValue(lastDemand.getDistributeValue());
                item.setLastDistributionTime(lastDemand.getCreateTime());
                item.setLastDistributionDemand(lastDemand.getDistributeValue());
            }
            item.setConnectionPointName(conn.getConnectionPointName());
            item.setConnectionPointId(conn.getConnectionPointId());
            item.setProjectId(request.getProjectId());
            list.add(item);
        });
        response.setList(list);
        return response;
    }

    /**
     * 下发并保存需量
     * @param request
     */
    public void saveDemandList(CiesDemandListRequest request) {
        List<CiesDemandRequest> demandList = request.getList();
        CiesEsPlanRuleResponse byProjectId = iCiesEsPlanRuleService.findByProjectId(request.getProjectId());
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        // 获取并网点以及对应的并网点信息
        List<String> connList = demandList.stream().map(CiesDemandRequest::getConnectionPointId).toList();
        List<CiesConnectionPointResponse> connInfoByIds = iCiesConnectionPointService.findConnInfoByIds(connList);
        Map<String, CiesConnectionPointResponse> connMap = connInfoByIds.stream().collect(Collectors.toMap(
                CiesConnectionPointResponse::getConnectionPointId,
                obj -> obj,
                (oldValue, newValue) -> newValue
        ));
        List<CiesDemandDistributeRecordEntity> list = BeanCopyUtil.copyListProperties(demandList, CiesDemandDistributeRecordEntity::new);
        for (CiesDemandDistributeRecordEntity item : list) {
            // 保存推送记录
            CiesConnectionPointResponse response = connMap.get(item.getConnectionPointId());
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("channelId", response.getChannelId());
            jsonObject1.put("val_array", Stream.of(item.getDistributeValue()).toList());
            jsonObject1.put("start_timestamp",System.currentTimeMillis());
            jsonObject1.put("demandId",response.getDemandId());
            jsonArray.add(jsonObject1);
            // 保存需量下发记录
            item.setDemandDistributeId(IdGenUtil.genUniqueId());
            item.setCreateTime(LocalDateTime.now());
            item.setCommandType(byProjectId.getCommandType());
            item.setCreateBy(CiesUserContext.getCurrentUser());
        }
        iCiesDemandDistributeRecordService.saveBatch(list);
        jsonObject.put("demand", jsonArray);
        jsonObject.put("projectId", request.getProjectId());
        log.info("项目：{}下发需量数据为：{}", byProjectId, jsonObject.toJSONString());
        client.distributeDemand(jsonObject);
    }

    /**
     * 查询下发记录（分页）
     * @param request
     * @return
     */
    public PageResponse<CiesDemandResponse> findDemandRecordsForPage(CiesDemandRequest request){
        return iCiesDemandDistributeRecordService.findDemandRecordsForPage(request);
    }
}
