<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesEnterpriseInfoMapper">

    <select id="findEnergyEnterprise" resultType="com.bcels.cies.response.CiesEnterpriseInfoResponse">

        select distinct enter.* FROM cies_enterprise_info enter
        JOIN cies_project_info pro on pro.enterprise_id = enter.enterprise_id
        <where>
            <if test="enterpriseName  != null and enterpriseName  != ''">
                AND enter.enterprise_name = #{enterpriseName}
            </if>
            <if test="projectType  != null and projectType  != ''">
                AND pro.project_type = #{projectType}
            </if>
        </where>
        limit 10
    </select>
</mapper>
