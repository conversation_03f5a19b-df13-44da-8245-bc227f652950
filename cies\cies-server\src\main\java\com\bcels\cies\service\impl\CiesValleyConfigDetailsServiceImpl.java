package com.bcels.cies.service.impl;

import com.bcels.cies.repository.entity.CiesValleyConfigDetailsEntity;
import com.bcels.cies.repository.mapper.CiesValleyConfigDetailsMapper;
import com.bcels.cies.response.CiesDeepResponse;
import com.bcels.cies.service.ICiesValleyConfigDetailsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 深谷电量配置明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class CiesValleyConfigDetailsServiceImpl extends ServiceImpl<CiesValleyConfigDetailsMapper, CiesValleyConfigDetailsEntity> implements ICiesValleyConfigDetailsService {


    @Autowired
    private CiesValleyConfigDetailsMapper ciesValleyConfigDetailsMapper;
    @Override
    public CiesDeepResponse queryDeepInfo(String essBillId) {
        return ciesValleyConfigDetailsMapper.queryDeepInfo(essBillId);
    }

    @Override
    public CiesDeepResponse queryDeepInfoByDataType(String essBillId, String dataType) {
        return ciesValleyConfigDetailsMapper.queryDeepInfoByDataType(essBillId,dataType);

    }
}
