package com.bcels.cies.emuns;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 分时时段枚举
 */
public enum PeriodTypeEnum {
    PEAK("topTime","尖峰"),
    OFF_PEAK("lowTime","低谷"),
    DEEP_VALLEY("deepTime","深谷"),
    HIGH("highTime","高峰"),
    NORMAL("flatTime","平");

    private String  code;

    private final String name;

    PeriodTypeEnum(String code,String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }



    private static final Map<String, PeriodTypeEnum> DESC_TO_ENUM = new HashMap<>();


    static {
        for (PeriodTypeEnum period : values()) {
            DESC_TO_ENUM.put(period.name,  period);
        }
    }


    public static String getCodeByDesc(String name) {
        return Optional.ofNullable(DESC_TO_ENUM.get(name))
                .map(PeriodTypeEnum::getCode)
                .orElseThrow(() -> new IllegalArgumentException("无效时段描述: " + name));
    }

    public static PeriodTypeEnum fromCode(String code) {
        for (PeriodTypeEnum period : values()) {
            if (period.code.equals(code))  {
                return period;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        for (PeriodTypeEnum period : values()) {
            if (period.code.equals(code))  {
                return period.name;
            }
        }
        return null;
    }
}
