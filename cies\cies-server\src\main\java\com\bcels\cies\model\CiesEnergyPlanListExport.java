package com.bcels.cies.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 储能计划下发审核表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@ToString
public class CiesEnergyPlanListExport {


    @ApiModelProperty("企业")
    private String enterpriseName;

    @ApiModelProperty("项目名称")
    private String proName;

    @ApiModelProperty("计划日期")
    private String planDate;

    @ApiModelProperty("计划充电时间段")
    private String plannedChargingTime;

    @ApiModelProperty("计划放电时间段")
    private String plannedDischargingTime;

    @ApiModelProperty("计划状态")
    private String auditStatus;

    @ApiModelProperty("下发状态")
    private String dispatchStatus;

    @ApiModelProperty("本日充电电量(kwh)")
    private BigDecimal dailyChargeEnergy;

    @ApiModelProperty("本日放电电量(kwh)")
    private BigDecimal dailyDischargeEnergy;

    @ApiModelProperty("本日充电成本（元）")
    private BigDecimal dailyChargeCost;

    @ApiModelProperty("本日放电收入（元）")
    private BigDecimal dailyDischargeIncome;

    @ApiModelProperty("本日充放电利润（元）")
    private BigDecimal dailyProfit;

    @ApiModelProperty("计划最后修改时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("计划最后修改人")
    private String updateBy;

}
