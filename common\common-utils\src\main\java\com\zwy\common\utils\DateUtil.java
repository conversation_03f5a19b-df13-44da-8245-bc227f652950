package com.zwy.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Slf4j
public class DateUtil extends DateUtils {

    /**
     * 日期格式:yyyy-mm-dd<br>
     * 例如:2005-11-02
     */
    public final static String DATE_PATTERN_LINE = "yyyy-MM-dd";


    public final static String DATE_PATTERN_LINE_V2 = "yy年MM月dd日";
    /**
     * 日期格式:yyyy/mm/dd<br>
     * 例如:2005/11/02
     */
    public final static String DATE_PATTERN_BIAS = "yyyy/MM/dd";
    /**
     * 日期时间格式(24小时制):yyyy-mm-dd HH:mm:ss<br>
     * 例如:2005-11-02 23:01:01
     */
    public final static String DATETIME24_PATTERN_LINE = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期时间格式(24小时制):yyyymmddHHmmss<br>
     * 例如:20051102230101
     */
    public final static String DATETIME_PATTERN_LINE = "yyyyMMddHHmmss";

    /**
     * 日期时间格式(24小时制):yyyy-MM-dd'T'HH:mm:ss.SSSXXX <br>
     * 例如:2005-11-02 23:01:01
     */
    public final static String DATETIME_Milli_PATTERN_LINE = "yyyy-MM-dd HH:mm:ss.SSSSSSSXXX";
    /**
     * 日期时间格式(12小时制):yyyy-mm-dd hh:mm:ss<br>
     * 例如:2005-11-02 11:01:01
     */
    public final static String DATETIME12_PATTERN_LINE = "yyyy-MM-dd hh:mm:ss";
    /**
     * 日期时间格式(24小时制):yyyy/mm/dd HH:mm:ss<br>
     * 例如:2005/11/02 23:01:01
     */
    public final static String DATETIME24_PATTERN_BIAS = "yyyy/MM/dd HH:mm:ss";
    /**
     * 日期时间格式(12小时制):yyyy/mm/dd hh:mm:ss<br>
     * 例如:2005/11/02 11:01:01
     */
    public final static String DATETIME12_PATTERN_BIAS = "yyyy/MM/dd hh:mm:ss";
    /**
     * 日期时间格式(24小时制):yyyy-mm-dd HH:mm<br>
     * 例如:2005-11-02 23:01:01
     */
    public final static String DATETIME24_NOS_PATTERN_LINE = "yyyy-MM-dd HH:mm";
    /**
     * 日期时间格式(24小时制):yyyyMMdd<br>
     * 例如:20151217
     */
    public static final String DATE_PATTERN = "yyyyMMdd";
    public static final String DATE_CHINESE_PATTERN = "yyyy年MM月dd日";
    public static final String DATE_PATTERN_YEAR = "yyyy";
    /**
     * 日期格式:yyyy-mm<br>
     * 例如:2005-11
     */
    public final static String MONTH_PATTERN_LINE = "yyyy-MM";


    public final static String MONTH_PATTERN_LINE_UNDER_LINE = "yyyyMM";


    public final static String HOUR_MINUTE = "HH:mm";

    /**
     * 日期格式化对象,日期型（yyyy-MM-dd）
     */
    private static ThreadLocal<SimpleDateFormat> DATE_FORMAT = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected synchronized SimpleDateFormat initialValue() {
            return new SimpleDateFormat(DATE_PATTERN_LINE);
        }
    };
    /**
     * 日期时间格式化对象,日期时间型（yyyy-MM-dd HH:mm:ss）
     */
    private static ThreadLocal<SimpleDateFormat> DATE_TIME_FORMAT = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected synchronized SimpleDateFormat initialValue() {
            return new SimpleDateFormat(DATETIME24_PATTERN_LINE);
        }
    };
    private int weeks = 0;
    // 用来全局控制 上一周，本周，下一周的周数变化
    private int MaxDate;// 一月最大天数
    private int MaxYear;// 一年最大天数

    /**
     * 获取指定时间戳的偏移天数 (传-1 获取昨天的时间戳)
     *
     * @param day
     * @param offset
     * @return
     */
    public static Timestamp getTimestampByOtherDay(Timestamp day, int offset) {
        Calendar c = Calendar.getInstance();
        c.setTime(day);
        c.add(Calendar.DAY_OF_MONTH, offset);
        return new Timestamp(c.getTimeInMillis());
    }

    /**
     * 获取指定时间戳的偏移小时 (传-10 获取10小时的时间戳)
     *
     * @param day
     * @param offset
     * @return
     */
    public static Timestamp getTimestampByOtherHour(Timestamp day, int offset) {
        Calendar c = Calendar.getInstance();
        c.setTime(day);
        c.add(Calendar.HOUR_OF_DAY, offset);
        return new Timestamp(c.getTimeInMillis());
    }

    /**
     * 得到二个日期间的间隔天数
     */
    public static String getTwoDay(String sj1, String sj2) {
        SimpleDateFormat myFormatter = new SimpleDateFormat(DATE_PATTERN_LINE);
        long day = 0;
        try {
            Date date = myFormatter.parse(sj1);
            Date mydate = myFormatter.parse(sj2);
            day = (date.getTime() - mydate.getTime()) / (24 * 60 * 60 * 1000);
        } catch (Exception e) {
            return "";
        }
        return day + "";
    }

    /**
     * 根据一个日期，返回是星期几的字符串
     *
     * @param sdate
     * @return
     */
    public static String getWeek(String sdate) {
        // 再转换为时间
        Date date = DateUtil.strToDate(sdate);
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        //int hour=c.get(Calendar.DAY_OF_WEEK);
        // hour中存的就是星期几了，其范围 1~7,1=星期日 7=星期六，其他类推
        return new SimpleDateFormat("EEEE").format(c.getTime());
    }

    public static String getWeekStr(String sdate) {
        String str = "";
        Date date = DateUtil.strToDate(sdate);
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int hour = c.get(Calendar.DAY_OF_WEEK);
        if (1 == hour) {
            str = "星期日";
        } else if (2 == hour) {
            str = "星期一";
        } else if (3 == hour) {
            str = "星期二";
        } else if (4 == hour) {
            str = "星期三";
        } else if (5 == hour) {
            str = "星期四";
        } else if (6 == hour) {
            str = "星期五";
        } else if (7 == hour) {
            str = "星期六";
        }
        return str;
    }

    /**
     * 将短时间格式字符串转换为时间 yyyy-MM-dd
     *
     * @param strDate
     * @return
     */
    public static Date strToDate(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat(DATE_PATTERN_LINE);
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }

    /**
     * 两个时间之间的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static long getDays(String date1, String date2) {
        if (date1 == null || date1.equals("")) {
            return 0;
        }
        if (date2 == null || date2.equals("")) {
            return 0;
        }
        // 转换为标准时间
        SimpleDateFormat myFormatter = new SimpleDateFormat(DATE_PATTERN_LINE);
        Date date = null;
        Date mydate = null;
        try {
            date = myFormatter.parse(date1);
            mydate = myFormatter.parse(date2);
        } catch (Exception e) {
        }
        long day = (date.getTime() - mydate.getTime()) / (24 * 60 * 60 * 1000);
        return day;
    }

    // 获取当天时间
    public static String getNowTime(String dateformat) {
        Date now = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat(dateformat);// 可以方便地修改日期格式
        String hehe = dateFormat.format(now);
        return hehe;
    }

    /**
     * 取得指定日期所在周的第一天
     */
    public static Date getFirstDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek()); // Monday
        return c.getTime();
    }

    /**
     * 取得指定日期所在周的最后一天
     */
    public static Date getLastDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek() + 6); // Sunday
        return c.getTime();
    }

    public static List<String> dateToWeek(Date mdate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(mdate);
        int b = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (b == 0) {
            b = 7;
        }
        Date fdate;
        List<String> list = new ArrayList<String>();
        Long fTime = mdate.getTime() - b * 24 * 3600000;
        String datetemp = "2099-12-12";
        for (int a = 1; a < 8; a++) {
            fdate = new Date();
            fdate.setTime(fTime + (a * 24 * 3600000));
            SimpleDateFormat sf = new SimpleDateFormat(DATE_PATTERN_LINE);
            datetemp = sf.format(fdate);
            list.add(datetemp);
        }
        return list;
    }

    public static String getbz(Date date) {
        SimpleDateFormat sf = new SimpleDateFormat(DATE_PATTERN_LINE);
        String datetemp = sf.format(date);
        return datetemp;
    }

    public static String getbz(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        SimpleDateFormat sf = new SimpleDateFormat("MM月dd日");
        String datetemp = "";
        try {
            datetemp = sf.format(sdf.parse(date));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return datetemp;
    }

    public static String getDateString(Date date) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        String datetemp = sf.format(date);
        return datetemp;
    }

    /**
     * 获取本周一日期
     *
     * @param current 当前时间
     * @return
     * @throws Exception
     */
    public static Date getThisWeekMonday(Date current) throws Exception {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(current);
            c.setFirstDayOfWeek(Calendar.MONDAY);
            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);// // 本周1
            return c.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    public static List<String> getWeekToDate(Date mdate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(mdate);
        int b = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (b == 0) {
            b = 7;
        }
        Date fdate;
        List<String> list = new ArrayList<String>();
        Long fTime = mdate.getTime() - b * 24 * 3600000;
        String datetemp = "2099-12-12";
        for (int a = 1; a < 8; a++) {
            fdate = new Date();
            fdate.setTime(fTime + (a * 24 * 3600000));
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
            datetemp = sf.format(fdate);
            list.add(datetemp);
        }
        return list;
    }

    /**
     * 获得本周日时间
     *
     * @param current
     * @return
     * @throws Exception
     */
    public static Date getThisWeekSunday(Date current) throws Exception {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(current);
            c.setFirstDayOfWeek(Calendar.MONDAY);
            c.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
            return c.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获得上周日时间
     *
     * @param current
     * @return
     * @throws Exception
     */
    public static Date getLastWeekSunday(Date current) throws Exception {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(getThisWeekMonday(current));
            c.setFirstDayOfWeek(Calendar.MONDAY);
            c.add(Calendar.DATE, -1);
            return c.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获得上周一时间
     *
     * @param current
     * @return
     * @throws Exception
     */
    public static Date getLastWeekMonday(Date current) throws Exception {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(getThisWeekMonday(current));
            c.setFirstDayOfWeek(Calendar.MONDAY);
            c.add(Calendar.DATE, -7);
            return c.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * @param date
     * @return
     */
    public static Timestamp getTimestamp(Date date) {
        SimpleDateFormat dateFormate = new SimpleDateFormat(DateUtil.DATETIME24_PATTERN_LINE);
        return Timestamp.valueOf(dateFormate.format(date));
    }

    public static Timestamp getTimestamp() {
        return getTimestamp(new Date(System.currentTimeMillis()));
    }

    public static Timestamp getTimestamp(String time) {
        if (time == null || time.toLowerCase().equals("null") || time.toLowerCase().length() == 0) {
            return new Timestamp(System.currentTimeMillis());
        } else {
            return Timestamp.valueOf(time);
        }
    }

    /**
     * 获取当前日期所在月的第一天的日期
     *
     * @param current
     * @return
     */
    @SuppressWarnings("deprecation")
    public static Date getFirstDate(Date current) throws Exception {
        try {
            return new Date(current.getYear(), current.getMonth(), 1);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获取当前日期所在月的最后一天的日期
     *
     * @param current
     * @return
     * @throws Exception
     */
    @SuppressWarnings("deprecation")
    public static Date getLastDate(Date current) throws Exception {
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(new Date(current.getYear(), current.getMonth(), 1));
            c.roll(Calendar.MONTH, true);
            if (current.getMonth() == 11) {
                c.roll(Calendar.YEAR, true);
            }
            c.add(Calendar.DATE, -1);
            return c.getTime();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 得到当前系统日期
     *
     * @return 得到系统当前日期
     */
    public static Date getCurrentDate() {
        return new Date(System.currentTimeMillis());
    }

    /**
     * 将字符串转化为日期型数据<br>
     * 字符串必须是yyyy-MM-dd格式
     *
     * @param src 日期数据字符串
     * @return java.util.Date型日期类型数据
     */
    public static Date parseDate(String src) {
        if (src == null || src.trim().length() <= 0) {
            return null;
        }
        try {
            return parse(src, 0);
        } catch (ParseException pe) {
            throw new RuntimeException(pe);
        }
    }

    /**
     * 根据日期、小时、分钟、秒组合成日期时间
     *
     * @param date 日期
     * @param hour 小时
     * @param
     * @param
     * @return 组合后的日期时间
     * @throws
     */
    public static Date parseDate(String date, int hour, int minute, int second) throws Exception {
        Calendar cal = Calendar.getInstance();
        Date dateObj = parseDate(date);
        cal.set(getYear(dateObj), getMonth(dateObj), getDay(dateObj), hour, minute, second);
        return cal.getTime();
    }

    /**
     * 根据年、月、日生成日志对象
     *
     * @param year  年
     * @param month 月(1~12之间)
     * @param day   日
     * @return 日期对象
     * @throws
     */
    public static Date parseDate(int year, int month, int day) throws Exception {
        Calendar cal = Calendar.getInstance();
        cal.set(year, month - 1, day);
        return cal.getTime();
    }

    /**
     * 得到日期中的年份
     *
     * @param date 日期
     * @return 年份
     */
    public static int getYear(Date date) {
        if (date == null) {
            return 0;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.YEAR);
    }

    /**
     * 得到日期中的月份
     *
     * @param date 日期
     * @return 月份
     */
    public static int getMonth(Date date) {
        if (date == null) {
            return 0;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.MONTH);
    }

    /**
     * 得到日期中的天
     *
     * @param date 日期
     * @return 天
     */
    public static int getDay(Date date) {
        if (date == null) {
            return 0;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 得到日期中的小时
     *
     * @param date 日期
     * @return 小时
     */
    public static int getHour(Date date) {
        if (date == null) {
            return 0;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 得到日期中的分钟数
     *
     * @param date 日期
     * @return 分钟
     */
    public static int getMinute(Date date) {
        if (date == null) {
            return 0;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.MINUTE);
    }

    /**
     * 根据指定的格式化模式,格式化日历数据<br>
     * 默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param now 给定日期
     * @return 被格式化后的字符串
     */
    public static String formatDate(Calendar now) {
        return formatDate(now, DateUtil.DATETIME24_PATTERN_LINE);
    }

    /**
     * 根据指定的格式化模式,格式化日历数据<br>
     * 如果格式化模式为null或者为空,则默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param now            给定日期
     * @param formatePattern 格式化模式
     * @return 被格式化后的字符串<br>
     */
    public static String formatDate(Calendar now, String formatePattern) {
        if (now == null) {
            return null;
        }
        if (formatePattern == null || formatePattern.trim().length() <= 0) {
            formatePattern = DateUtil.DATETIME24_PATTERN_LINE;
        }
        Date tempDate = now.getTime();
        SimpleDateFormat dateFormate = new SimpleDateFormat(formatePattern);
        return dateFormate.format(tempDate);
    }

    /**
     * 将java.util.Date数据转换为指定格式的字符串<br>
     * 如果格式化模式为null或者为空,则默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param date           java.util.Date类型数据
     * @param formatePattern 指定的日期格式化模式
     * @return 格式化后的日期的字符串形式<br>
     */
    public static String formatDate(Date date, String formatePattern) {
        if (formatePattern == null || formatePattern.trim().length() <= 0) {
            formatePattern = DateUtil.DATETIME24_PATTERN_LINE;
        }
        if (date == null) {
            return "";
        } else {
            SimpleDateFormat dateFormate = new SimpleDateFormat(formatePattern);
            return dateFormate.format(date);
        }
    }

    /**
     * 将java.sql.Timestamp数据转换为指定格式的字符串<br>
     * 如果格式化模式为null或者为空,则默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param date           Timestamp数据
     * @param formatePattern 日期格式化模式
     * @return 格式化后的日期的字符串形式
     */
    public static String formatDate(Timestamp date, String formatePattern) {
        if (formatePattern == null || formatePattern.trim().length() <= 0) {
            formatePattern = DateUtil.DATETIME24_PATTERN_LINE;
        }
        if (date == null) {
            return "";
        } else {
            SimpleDateFormat dateFormate = new SimpleDateFormat(formatePattern);
            return dateFormate.format(date);
        }
    }

    /**
     * 将java.util.Date数据转换为指定格式的字符串<br>
     * 如果格式化模式为null或者为空,则默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param date java.util.Date类型数据
     * @return 格式化后的日期的字符串形式<br>
     */
    public static String formatDate(Date date) {
        return formatDate(date, DateUtil.DATETIME24_PATTERN_LINE);
    }

    /**
     * 将java.util.Date数据转换为指定格式的字符串<br>
     * 如果格式化模式为null或者为空,则默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param date java.util.Date类型数据
     * @return 格式化后的日期的字符串形式<br>
     */
    public static String formatDateNoraml(Date date) {
        return formatDate(date, DateUtil.DATE_PATTERN_LINE);
    }

    /**
     * 将代表日期的长整形数值转换为yyyy-MM-dd HH:mm:ss格式的字符串<br>
     *
     * @param datetime 需要转换的日期的长整形数值
     * @return 格式化后的日期字符串
     */
    public static String formatDate(long datetime) {
        return formatDate(datetime, DateUtil.DATETIME24_PATTERN_LINE);
    }

    /**
     * 将代表日期的字符串转换yyyy-MM-dd HH:mm:ss格式的字符串
     *
     * @param datetime 需要转换的日期
     * @return 格式化后的日期字符串
     */
    public static String formate(String datetime) {
        return formatDate(datetime, DateUtil.DATETIME24_PATTERN_LINE);
    }

    /**
     * 将代表日期的字符串转换未指定格式的字符串<br>
     * 如果格式化模式为null或者为空,则默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param datetime       需要转换的日期的字符串
     * @param formatePattern 指定的日期格式
     * @return 格式化后的日期字符串
     */
    public static String formatDate(String datetime, String formatePattern) {
        if (datetime == null || datetime.trim().length() <= 0) {
            return "";
        }
        Date date = null;
        try {
            if (formatePattern != null && (formatePattern.equals(DateUtil.DATE_PATTERN_BIAS) || formatePattern.equals(DateUtil.DATE_PATTERN_LINE))) {
                date = parseDate(datetime);
            } else {
                date = parseDateTime(datetime);
            }


        } catch (Exception ex) {
            log.error("日期转换失败:", ex);
        }
        return formatDate(date, formatePattern);
    }

    public static Date formatDateNormal(String datetime, String formatePattern) {
        if (datetime == null || datetime.trim().length() <= 0 || StringUtils.isBlank(formatePattern)) {
            return null;
        }
        SimpleDateFormat myFormatter = new SimpleDateFormat(formatePattern);

        try {
            return myFormatter.parse(datetime);
        } catch (ParseException e) {
            log.error("日期转换失败:", e);
            return null;
        }
    }

    /**
     * 将代表日期的长整形数值转换为y指定格式的字符串<br>
     * 如果格式化模式为null或者为空,则默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param datetime       需要转换的日期的长整形数值
     * @param formatePattern 指定的日期格式
     * @return 格式化后的日期字符串
     */
    public static String formatDate(long datetime, String formatePattern) {
        if (datetime <= 0) {
            return "";
        }
        Date date = new Date(datetime);
        return formatDate(date, formatePattern);
    }

    /**
     * 将java.sql.Date数据转换为指定格式的字符串<br>
     * 默认使用yyyy-MM-dd HH:mm:ss
     *
     * @param date java.sql.Date类型数据
     * @return 格式化后的日期的字符串形式<br>
     */
    public static String formatDate(java.sql.Date date) {
        return formatDate(toUtilDate(date));
    }

    /**
     * 将字符串转化为日期型数据<br>
     * 字符串必须是yyyy-MM-dd HH:mm:ss格式
     *
     * @param src 日期数据字符串
     * @return java.util.Date型日期时间型数据
     */
    public static Date parseDateTime(String src) {
        if (src == null || src.equals("")) {
            return null;
        }

        try {
            return parse(src, 1);
        } catch (ParseException pe) {
            throw new RuntimeException(pe);
        }
    }

    /**
     * 将java.sql.Date转换为java.util.Date数据类型
     *
     * @param date 需要转换的java.sql.Date数据
     * @return 转换后的java.util.Date数据
     */
    public static Date toUtilDate(java.sql.Date date) {
        if (date == null) {
            return null;
        } else {
            return new Date(date.getTime());
        }
    }

    /**
     * 某日期上添加x时间段
     *
     * @param date
     * @param iType  如Calendar.DAY_OF_MONTH
     * @param iValue 增加的时间
     * @return
     * <AUTHOR>
     */
    public static Date add(Date date, int iType, int iValue) {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(iType, iValue);
        return calendar.getTime();
    }

    /**
     * 某日期上添加x时间段
     *
     * @param timestamp
     * @param iType     如Calendar.DAY_OF_MONTH
     * @param iValue    增加的时间
     * @return
     * <AUTHOR>
     */
    public static Timestamp add(Timestamp timestamp, int iType, int iValue) {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(timestamp);
        calendar.add(iType, iValue);
        return new Timestamp(calendar.getTimeInMillis());
    }


    public static Date strToDate(String dateStr, String format) throws ParseException {
        SimpleDateFormat df = null;
        Date date = null;
        df = new SimpleDateFormat(format);
        try {
            date = df.parse(dateStr);
        } catch (ParseException pe) {
            throw new ParseException(pe.getMessage(), pe.getErrorOffset());
        }

        return date;
    }

    public static Long getLongByStr(String str, String format) {
        if (str == null) {
            return null;
        }
        SimpleDateFormat f = new SimpleDateFormat(format);
        Date date = null;
        try {
            date = f.parse(str);
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        Long time = Long.valueOf(date.getTime());
        return time;
    }

    /**
     * 获取两个时间差值
     *
     * @param timest 开始时间
     * @param timeed 结束时间
     * @param type   1 天，2 时， 3 分， 4 秒， 5 毫秒（ms）
     * @return 成功返回时间差值，不成功返回null
     */
    public static Long getTimeDiff(Date timest, Date timeed, int type) {
        try {
            if (timest == null) {
                log.info("getTimeDiff timest 为空！");
                return null;
            }
            if (timeed == null) {
                log.info("getTimeDiff timeed 为空！");
                return null;
            }

            Long result = null;
            switch (type) {
                case 1:
                    result = getDiffDays(timest, timeed);
                    break;
                case 2:
                    result = getDiffHours(timest, timeed);
                    break;
                case 3:
                    result = getDiffMinutes(timest, timeed);
                    break;
                case 4:
                    result = getDiffSeconds(timest, timeed);
                    break;
                case 5:
                    result = getDiffStamp(timest, timeed);
                    break;
                default:
                    log.info("getTimeDiff type未定义！");
                    break;
            }
            return result;

        } catch (Exception e) {
            log.error("getTimeDiff 异常：" + e.getMessage());
        }
        return null;
    }

    /**
     * 获取两个时间相差的天数
     *
     * @param timest 开始时间
     * @param timeed 结束时间
     * @return 成功返回相差天数，不成功返回null
     */
    private static Long getDiffDays(Date timest, Date timeed) {
        try {
            Calendar calst = Calendar.getInstance();
            Calendar caled = Calendar.getInstance();
            calst.setTime(timest);
            caled.setTime(timeed);
            log.info("LongDiffDays 开始时间：" + formatDate(timest, DATE_PATTERN_LINE));
            log.info("LongDiffDays 结束时间：" + formatDate(timeed, DATE_PATTERN_LINE));

            //设置时间为0时
            calst.set(Calendar.HOUR_OF_DAY, 0);
            calst.set(Calendar.MINUTE, 0);
            calst.set(Calendar.SECOND, 0);

            caled.set(Calendar.HOUR_OF_DAY, 0);
            caled.set(Calendar.MINUTE, 0);
            caled.set(Calendar.SECOND, 0);
            //得到两个日期相差的天数
            Long days = ((Long) (caled.getTime().getTime() / 1000) - (Long) (calst.getTime().getTime() / 1000)) / 3600 / 24;

            return days;
        } catch (Exception e) {
            log.error("LongDiffDays 异常：" + e.getMessage());
        }
        return null;
    }

    /**
     * 获取两个时间相差的分钟数
     *
     * @param timest 开始时间
     * @param timeed 结束时间
     * @return 成功返回相差分钟数，不成功返回null
     */
    private static Long getDiffHours(Date timest, Date timeed) {
        try {
            Calendar calst = Calendar.getInstance();
            Calendar caled = Calendar.getInstance();
            calst.setTime(timest);
            caled.setTime(timeed);
            log.info("LongDiffDays 开始时间：" + formatDate(timest, "yyyy-MM-dd HH:mm:ss"));
            log.info("LongDiffDays 结束时间：" + formatDate(timeed, "yyyy-MM-dd HH:mm:ss"));

            //设置时间为0时
//			calst.set(java.util.Calendar.HOUR_OF_DAY, 0);
            calst.set(Calendar.MINUTE, 0);
            calst.set(Calendar.SECOND, 0);

//			caled.set(java.util.Calendar.HOUR_OF_DAY, 0);
            caled.set(Calendar.MINUTE, 0);
            caled.set(Calendar.SECOND, 0);
            //得到两个日期相差的天数
            Long hoerss = ((Long) (caled.getTime().getTime() / 1000) - (Long) (calst.getTime().getTime() / 1000)) / 3600;

            return hoerss;
        } catch (Exception e) {
            log.error("getDiffHours 异常：" + e.getMessage());
        }
        return null;
    }

    /**
     * 获取两个时间相差的分钟数
     *
     * @param timest 开始时间
     * @param timeed 结束时间
     * @return 成功返回相差分钟数，不成功返回null
     */
    private static Long getDiffMinutes(Date timest, Date timeed) {
        try {
            Calendar calst = Calendar.getInstance();
            Calendar caled = Calendar.getInstance();
            calst.setTime(timest);
            caled.setTime(timeed);
            log.info("LongDiffDays 开始时间：" + formatDate(timest, "yyyy-MM-dd HH:mm:ss"));
            log.info("LongDiffDays 结束时间：" + formatDate(timeed, "yyyy-MM-dd HH:mm:ss"));

            //设置时间为0时
//			calst.set(java.util.Calendar.HOUR_OF_DAY, 0);
//			calst.set(java.util.Calendar.MINUTE, 0);
            calst.set(Calendar.SECOND, 0);

//			caled.set(java.util.Calendar.HOUR_OF_DAY, 0);
//			caled.set(java.util.Calendar.MINUTE, 0);
            caled.set(Calendar.SECOND, 0);
            //得到两个日期相差的天数
            Long minutess = ((Long) (caled.getTime().getTime() / 1000) - (Long) (calst.getTime().getTime() / 1000)) / 60;

            return minutess;
        } catch (Exception e) {
            log.info("getDiffMinutes 异常：" + e.getMessage());
        }
        return null;
    }

    /**
     * 获取两个时间相差的数据刻度
     *
     * @param timest 开始时间
     * @param timeed 结束时间
     * @return 成功返回时间刻度，不成功返回null
     */
    private static Long getDiffSeconds(Date timest, Date timeed) {
        try {
            Calendar calst = Calendar.getInstance();
            Calendar caled = Calendar.getInstance();
            calst.setTime(timest);
            caled.setTime(timeed);
            log.info("LongDiffDays 开始时间：" + formatDate(timest, "yyyy-MM-dd HH:mm:ss"));
            log.info("LongDiffDays 结束时间：" + formatDate(timeed, "yyyy-MM-dd HH:mm:ss"));

            //设置时间为0时
//			calst.set(java.util.Calendar.HOUR_OF_DAY, 0);
//			calst.set(java.util.Calendar.MINUTE, 0);
//			calst.set(java.util.Calendar.SECOND, 0);

//			caled.set(java.util.Calendar.HOUR_OF_DAY, 0);
//			caled.set(java.util.Calendar.MINUTE, 0);
//			caled.set(java.util.Calendar.SECOND, 0);
            //得到两个日期相差的天数
            Long minutess = ((Long) (caled.getTime().getTime() / 1000) - (Long) (calst.getTime().getTime() / 1000));

            return minutess;
        } catch (Exception e) {
            log.error("getDiffSeconds 异常：" + e.getMessage());
        }
        return null;
    }

    /**
     * 获取两个时间的相差时间刻度
     *
     * @param timest 开始时间
     * @param timeed 结束时间
     * @return 成功返回时间刻度，不成功返回null
     */
    private static Long getDiffStamp(Date timest, Date timeed) {
        try {
            Calendar calst = Calendar.getInstance();
            Calendar caled = Calendar.getInstance();
            calst.setTime(timest);
            caled.setTime(timeed);
            log.info("LongDiffDays 开始时间：" + formatDate(timest, "yyyy-MM-dd HH:mm:ss"));
            log.info("LongDiffDays 结束时间：" + formatDate(timeed, "yyyy-MM-dd HH:mm:ss"));

            //设置时间为0时
//			calst.set(java.util.Calendar.HOUR_OF_DAY, 0);
//			calst.set(java.util.Calendar.MINUTE, 0);
//			calst.set(java.util.Calendar.SECOND, 0);

//			caled.set(java.util.Calendar.HOUR_OF_DAY, 0);
//			caled.set(java.util.Calendar.MINUTE, 0);
//			caled.set(java.util.Calendar.SECOND, 0);
            //得到两个日期相差的天数
            Long stamps = ((Long) (caled.getTime().getTime()) - (Long) (calst.getTime().getTime()));

            return stamps;
        } catch (Exception e) {
            log.error("getDiffStamp 异常：" + e.getMessage());
        }
        return null;
    }

    public static Integer compareDate(Date date1, Date date2, int stype) {
        if (null == date1) {
            return null;
        }
        int n = 0;
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        try {
            c1.setTime(date1);
            c2.setTime(date2);
        } catch (Exception e) {
            e.printStackTrace();
        }
        while (!c1.after(c2)) { // 循环对比，直到相等，n 就是所要的结果
            n++;
            if (stype == 1) {
                c1.add(Calendar.MONTH, 1); // 比较月份，月份+1
            } else {
                c1.add(Calendar.DATE, 1); // 比较天数，日期+1
            }
        }
        n = n - 1;
        if (stype == 2) {
            n = (int) n / 365;
        }
        return n;
    }

    public static Date monthChange(Date date, Integer month) {
        //return DateUtil.addMonths(date,month);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.add(Calendar.MONTH, month);

        Date lastdate = DateUtil.getLastDayOfMonth(date, 0);

        Date dates = DateUtil.addMonths(date, month);
        if (DateUtil.formatDate(lastdate).equals(DateUtil.formatDate(date))) {
            if (Calendar.MONTH != 2) {
                dates = DateUtil.getLastDayOfMonth(dates, 0);
            }
        }
        return dates;
    }

    public static Date getLastDayOfMonth(Date fiducialDate, int offset) {
        Calendar cal = Calendar.getInstance();
        if (fiducialDate == null) {
            fiducialDate = new Date();
        }
        cal.setTime(fiducialDate);
        cal.add(2, offset + 1);
        cal.set(5, 1);
        cal.add(5, -1);
        return cal.getTime();
    }

    public static SimpleDateFormat getDateFormat(int sl) {
        switch (sl) {
            case 0:
                return DATE_FORMAT.get();
            case 1:
                return DATE_TIME_FORMAT.get();
        }
        return null;
    }

    //通用时间转换方法
    public static Date parse(String textDate, int sl) throws ParseException {
        switch (sl) {
            case 0://日期转换
                return getDateFormat(0).parse(textDate);
            case 1://日期+时间转换
                return getDateFormat(1).parse(textDate);
        }
        return null;
    }

    // 获取72小时，也就是day天前的时间，精确到00:00:00
    public static Date getDateBefore(Date d, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) - day);
        return now.getTime();
    }

    /**
     * @param date
     * @param n    加减n 个月份后的时间
     * @return Date
     * @description 根据传入的时间，返回 n 相聚 n 个月后的时间
     * @user MindHacks
     * @date 2016年12月5日下午9:06:01H
     */
    public static Date currentDateChangeMonth(Date date, Integer n) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, n);
        c.add(Calendar.DAY_OF_MONTH, -1);
        return c.getTime();
    }

    /**
     * @param t1
     * @param t2
     * @return Integer
     * @description 返回两个date类型时间，相差的天数(注意：只适合整点2016-11-11 00:00:00)
     * @user MindHacks
     * @date 2016年12月6日下午4:20:49
     */
    public static Integer dateSubtract(Date t1, Date t2) {
        long to = t1.getTime();
        long from = t2.getTime();
        return (int) ((to - from) / (1000 * 60 * 60 * 24));
    }

    /**
     * @param n
     * @return java.util.Date
     * @description : 根据传入的相对天数，返回当前时间的加减值
     * @user MindHacks
     * @date 2017/1/10 14:53
     */
    public static Date relativeDay(Date current, Integer n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(current);
        Date startDate = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, n);//参数-1代表在原来时间的基础上减少一天，换为1则为加一天;ps:加减月数 小时同加减天数
        return calendar.getTime();
    }

    /**
     * @param current
     * @param n
     * @return java.util.Date
     * @description :根据传入的相对相对年数，返回当前时间的加减值年数
     * @user MindHacks
     * @date 2017/1/10 15:00
     */
    public static Date relativeYear(Date current, Integer n) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(current);
        Date startDate = calendar.getTime();
        calendar.add(Calendar.YEAR, n);
        return calendar.getTime();
    }

    public static int getMonthDiff(Date s, Date e) {
        if (s.after(e)) {
            Date t = s;
            s = e;
            e = t;
        }
        Calendar start = Calendar.getInstance();
        start.setTime(s);
        Calendar end = Calendar.getInstance();
        end.setTime(e);
        Calendar temp = Calendar.getInstance();
        temp.setTime(e);
        // temp.add(Calendar.DATE, 1);

        int y = end.get(Calendar.YEAR) - start.get(Calendar.YEAR);
        int endM = end.get(Calendar.MONTH), startM = start.get(Calendar.MONTH);
        int m = endM - startM + y * 12;

        if (start.get(Calendar.DAY_OF_MONTH) > end.get(Calendar.DAY_OF_MONTH) + 1) {
            m -= 1;
        }

        return m;
    }

    /**
     * 当前日期加上天数后的日期
     *
     * @param num 为增加的天数
     * @return
     */
    public static String plusDay(int num) {
        Date d = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currdate = format.format(d);
        //System.out.println("现在的日期是：" + currdate);

        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, num);// num为增加的天数，可以改变的
        d = ca.getTime();
        String enddate = format.format(d);
        //System.out.println("增加天数以后的日期：" + enddate);
        return enddate;
    }

    /**
     * 返回指定格式的字符串日期
     *
     * @param date   日期 允许NULL,为NULL时返回空字符
     * @param format 返回的字符串日期格式
     * @return
     */
    public static String DateToStr(Date date, String format) {
        String dateStr = null;
        if (date != null) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            dateStr = simpleDateFormat.format(date);
        }
        return dateStr;
    }

    /**
     * 天的变动
     *
     * @param day
     * @return
     */
    public static Date dayChange(Date date, Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        return calendar.getTime();
    }

    /**
     * 比较两个日期，计算中间的天数
     *
     * @param start
     * @param end
     * @return
     */
    public static int getIntervalDaysReal(Date start, Date end) {
        long diff = end.getTime() - start.getTime();
        int days = (int) (diff / (1000 * 60 * 60 * 24));

        return days;
    }

    /**
     * 描述：设置时间
     *
     * @param
     * @return 作者 ：lfq
     * 日期 ：
     */
    public static Date setDate(Date date, int hours, int minute, int second) {
        date = setHours(date, hours);
        date = setMinutes(date, minute);
        date = setSeconds(date, second);
        date = setMilliseconds(date, 0);
        return date;
    }

    public static LocalDateTime getStartTime(LocalDate localDate) {
        return localDate.atTime(0, 0, 0);
    }

    public static LocalDateTime getEndTime(LocalDate localDate) {
        return localDate.atTime(23, 59, 59);
    }

    public static LocalTime parseLocalTime(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return null;
        }
        return LocalTime.parse(timeStr, DateTimeFormatter.ofPattern(HOUR_MINUTE));
    }


    // 计算当月最后一天,返回字符串
    public String getDefaultDay() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        Calendar lastDate = Calendar.getInstance();
        lastDate.set(Calendar.DATE, 1);// 设为当前月的1号
        lastDate.add(Calendar.MONTH, 1);// 加一个月，变为下月的1号
        lastDate.add(Calendar.DATE, -1);// 减去一天，变为当月最后一天
        str = sdf.format(lastDate.getTime());
        return str;
    }

    // 上月第一天
    public String getPreviousMonthFirst() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        Calendar lastDate = Calendar.getInstance();
        lastDate.set(Calendar.DATE, 1);// 设为当前月的1号
        lastDate.add(Calendar.MONTH, -1);// 减一个月，变为下月的1号
        // lastDate.add(Calendar.DATE,-1);//减去一天，变为当月最后一天
        str = sdf.format(lastDate.getTime());
        return str;
    }

    // 获取当月第一天
    public String getFirstDayOfMonth() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        Calendar lastDate = Calendar.getInstance();
        lastDate.set(Calendar.DATE, 1);// 设为当前月的1号
        str = sdf.format(lastDate.getTime());
        return str;
    }

    // 获得本周星期日的日期
    public String getCurrentWeekday() {
        weeks = 0;
        int mondayPlus = this.getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus + 6);
        Date monday = currentDate.getTime();
        DateFormat df = DateFormat.getDateInstance();
        String preMonday = df.format(monday);
        return preMonday;
    }

    // 获得当前日期与本周日相差的天数
    private int getMondayPlus() {
        Calendar cd = Calendar.getInstance();
        // 获得今天是一周的第几天，星期日是第一天，星期二是第二天......
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK) - 1; // 因为按中国礼拜一作为第一天所以这里减1
        if (dayOfWeek == 1) {
            return 0;
        } else {
            return 1 - dayOfWeek;
        }
    }

    // 获得当前日期与本周日相差的天数
    private int getAmraMondayPlus() {
        Calendar cd = Calendar.getInstance();
        // 获得今天是一周的第几天，星期日是第一天，星期二是第二天......
        int dayOfWeek = cd.get(Calendar.DAY_OF_WEEK); // 因为按中国礼拜一作为第一天所以这里减1
        if (dayOfWeek == 1) {
            return 0;
        } else {
            return 1 - dayOfWeek;
        }
    }

    // 获得本周一的日期
    public String getMondayOFWeek() {
        weeks = 0;
        int mondayPlus = this.getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus);
        Date monday = currentDate.getTime();
        DateFormat df = DateFormat.getDateInstance();
        String preMonday = df.format(monday);
        return preMonday;
    }

    // 获得相应周的周六的日期
    public String getSaturday() {
        int mondayPlus = this.getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus + 7 * weeks + 6);
        Date monday = currentDate.getTime();
        DateFormat df = DateFormat.getDateInstance();
        String preMonday = df.format(monday);
        return preMonday;
    }

    // 获得美国上周星期日-1的日期
    public String getPreviousWeekSunday() {
        weeks = 0;
        weeks--;
        int mondayPlus = this.getAmraMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus + weeks);
        Date monday = currentDate.getTime();
        SimpleDateFormat sf = new SimpleDateFormat("MM月dd日");
        String preMonday = sf.format(monday);
        return preMonday;
    }

    // 获得美国上周星期一-1的日期
    public String getPreviousWeekday() {
        weeks--;
        int mondayPlus = this.getAmraMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus + 7 * weeks);
        Date monday = currentDate.getTime();
        SimpleDateFormat sf = new SimpleDateFormat("MM月dd日");
        String preMonday = sf.format(monday);
        return preMonday;
    }

    // 获得下周星期一的日期
    public String getNextMonday() {
        weeks++;
        int mondayPlus = this.getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus + 7);
        Date monday = currentDate.getTime();
        DateFormat df = DateFormat.getDateInstance();
        String preMonday = df.format(monday);
        return preMonday;
    }

    // 获得下周星期日的日期
    public String getNextSunday() {
        int mondayPlus = this.getMondayPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, mondayPlus + 7 + 6);
        Date monday = currentDate.getTime();
        DateFormat df = DateFormat.getDateInstance();
        String preMonday = df.format(monday);
        return preMonday;
    }

    @SuppressWarnings("unused")
    private int getMonthPlus() {
        Calendar cd = Calendar.getInstance();
        int monthOfNumber = cd.get(Calendar.DAY_OF_MONTH);
        cd.set(Calendar.DATE, 1);// 把日期设置为当月第一天
        cd.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
        MaxDate = cd.get(Calendar.DATE);
        if (monthOfNumber == 1) {
            return -MaxDate;
        } else {
            return 1 - monthOfNumber;
        }
    }

    // 获得上月最后一天的日期
    public String getPreviousMonthEnd() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        Calendar lastDate = Calendar.getInstance();
        lastDate.add(Calendar.MONTH, -1);// 减一个月
        lastDate.set(Calendar.DATE, 1);// 把日期设置为当月第一天
        lastDate.roll(Calendar.DATE, -1);// 日期回滚一天，也就是本月最后一天
        str = sdf.format(lastDate.getTime());
        return str;
    }

    // 获得下个月第一天的日期
    public String getNextMonthFirst() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        Calendar lastDate = Calendar.getInstance();
        lastDate.add(Calendar.MONTH, 1);// 减一个月
        lastDate.set(Calendar.DATE, 1);// 把日期设置为当月第一天
        str = sdf.format(lastDate.getTime());
        return str;
    }

    // 获得下个月最后一天的日期
    public String getNextMonthEnd() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        Calendar lastDate = Calendar.getInstance();
        lastDate.add(Calendar.MONTH, 1);// 加一个月
        lastDate.set(Calendar.DATE, 1);// 把日期设置为当月第一天
        lastDate.roll(Calendar.DATE, -1);// 日期回滚一天，也就是本月最后一天
        str = sdf.format(lastDate.getTime());
        return str;
    }

    // 获得明年最后一天的日期
    public String getNextYearEnd() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        Calendar lastDate = Calendar.getInstance();
        lastDate.add(Calendar.YEAR, 1);// 加一个年
        lastDate.set(Calendar.DAY_OF_YEAR, 1);
        lastDate.roll(Calendar.DAY_OF_YEAR, -1);
        str = sdf.format(lastDate.getTime());
        return str;
    }

    // 获得明年第一天的日期
    public String getNextYearFirst() {
        String str = "";
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_PATTERN_LINE);
        Calendar lastDate = Calendar.getInstance();
        lastDate.add(Calendar.YEAR, 1);// 加一个年
        lastDate.set(Calendar.DAY_OF_YEAR, 1);
        str = sdf.format(lastDate.getTime());
        return str;
    }

    // 获得本年有多少天
    @SuppressWarnings("unused")
    private int getMaxYear() {
        Calendar cd = Calendar.getInstance();
        cd.set(Calendar.DAY_OF_YEAR, 1);// 把日期设为当年第一天
        cd.roll(Calendar.DAY_OF_YEAR, -1);// 把日期回滚一天。
        int MaxYear = cd.get(Calendar.DAY_OF_YEAR);
        return MaxYear;
    }

    private int getYearPlus() {
        Calendar cd = Calendar.getInstance();
        int yearOfNumber = cd.get(Calendar.DAY_OF_YEAR);// 获得当天是一年中的第几天
        cd.set(Calendar.DAY_OF_YEAR, 1);// 把日期设为当年第一天
        cd.roll(Calendar.DAY_OF_YEAR, -1);// 把日期回滚一天。
        int MaxYear = cd.get(Calendar.DAY_OF_YEAR);
        if (yearOfNumber == 1) {
            return -MaxYear;
        } else {
            return 1 - yearOfNumber;
        }
    }

    // 获得本年第一天的日期
    public String getCurrentYearFirst() {
        int yearPlus = this.getYearPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, yearPlus);
        Date yearDay = currentDate.getTime();
        DateFormat df = DateFormat.getDateInstance();
        String preYearDay = df.format(yearDay);
        return preYearDay;
    }

    // 获得本年最后一天的日期 *
    public String getCurrentYearEnd() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");// 可以方便地修改日期格式
        String years = dateFormat.format(date);
        return years + "-12-31";
    }

    // 获得上年第一天的日期 *
    public String getPreviousYearFirst() {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");// 可以方便地修改日期格式
        String years = dateFormat.format(date);
        int years_value = Integer.parseInt(years);
        years_value--;
        return years_value + "-1-1";
    }

    // 获得上年最后一天的日期
    public String getPreviousYearEnd() {
        weeks--;
        int yearPlus = this.getYearPlus();
        GregorianCalendar currentDate = new GregorianCalendar();
        currentDate.add(GregorianCalendar.DATE, yearPlus + MaxYear * weeks + (MaxYear - 1));
        Date yearDay = currentDate.getTime();
        DateFormat df = DateFormat.getDateInstance();
        String preYearDay = df.format(yearDay);
        getThisSeasonTime(11);
        return preYearDay;
    }

    // 获得本季度
    public String getThisSeasonTime(int month) {
        int array[][] = {{1, 2, 3}, {4, 5, 6}, {7, 8, 9}, {10, 11, 12}};
        int season = 1;
        if (month >= 1 && month <= 3) {
            season = 1;
        }
        if (month >= 4 && month <= 6) {
            season = 2;
        }
        if (month >= 7 && month <= 9) {
            season = 3;
        }
        if (month >= 10 && month <= 12) {
            season = 4;
        }
        int start_month = array[season - 1][0];
        int end_month = array[season - 1][2];
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy");// 可以方便地修改日期格式
        String years = dateFormat.format(date);
        int years_value = Integer.parseInt(years);
        int start_days = 1;// years+"-"+String.valueOf(start_month)+"-1";//getLastDayOfMonth(years_value,start_month);
        int end_days = getLastDayOfMonth(years_value, end_month);
        String seasonDate = years_value + "-" + start_month + "-" + start_days + ";" + years_value + "-" + end_month + "-" + end_days;
        return seasonDate;
    }

    /**
     * 获取某年某月的最后一天
     *
     * @param year  年
     * @param month 月
     * @return 最后一天
     */
    private int getLastDayOfMonth(int year, int month) {
        if (month == 1 || month == 3 || month == 5 || month == 7 || month == 8 || month == 10 || month == 12) {
            return 31;
        }
        if (month == 4 || month == 6 || month == 9 || month == 11) {
            return 30;
        }
        if (month == 2) {
            if (isLeapYear(year)) {
                return 29;
            } else {
                return 28;
            }
        }
        return 0;
    }

    public static Date getMongoDBDate() {
        Calendar calle = Calendar.getInstance();
        calle.setTime(new Date());
        calle.add(Calendar.HOUR_OF_DAY, +8);
        return calle.getTime();
    }
    /*
    * Document doc = collection.find().first();
Date date = doc.getDate("date");
SimpleDateFormat formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
formattedDate.setTimeZone(TimeZone.getTimeZone("UTC"));
System.out.println(formattedDate.format(date));

    *
    * */

    /**
     * 是否闰年
     *
     * @param year 年
     * @return
     */
    public boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    public static Date getStartTimeOfDate(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static LocalDate toLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return toLocalDateTime(date).toLocalDate();
    }


    public static LocalDate toLocalDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        return LocalDate.parse(date, DateTimeFormatter.ofPattern(DATE_PATTERN_LINE));
    }


    public static LocalTime toLocalTime(String time) {
        if (time == null) {
            return null;
        }
        return LocalTime.parse(time, DateTimeFormatter.ofPattern(HOUR_MINUTE));
    }


    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }


    public static LocalDateTime toLocalDateTime(String date) {
        if (date == null) {
            return null;
        }

        return LocalDateTime.parse(date, DateTimeFormatter.ofPattern(DATETIME24_PATTERN_LINE));
    }

    /**
     * 将年月字符串，转换成每月第一天 yyyyMM
     * @param month
     * @return
     */
    public static LocalDate toLocalDateByMonth(String month) {
        if (StringUtils.isBlank(month)) {
            return null;
        }
        return LocalDate.parse(month + "01", DateTimeFormatter.ofPattern(DATE_PATTERN));
    }

    public static Date toDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zoneId).toInstant();
        return Date.from(instant);
    }


    public static Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }


    public static Long toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        Instant instant = zdt.toInstant();
        return instant.toEpochMilli();
    }

    public static LocalDateTime toLocalDateTime(Long milliTimestamp) {
        if (milliTimestamp == null) {
            return null;
        }
        Instant instant = Instant.ofEpochMilli(milliTimestamp);
        // Convert Instant to LocalDateTime
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static String toDateString(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(DATE_PATTERN_LINE));
    }

    public static String toMonthString(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern(MONTH_PATTERN_LINE));
    }

    public static String toMonthString(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(MONTH_PATTERN_LINE));
    }

    public static String toDateString(LocalDate localDate, String format) {
        if (localDate == null) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(format));
    }

    public static String toDateTimeString(LocalDateTime localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(DATETIME24_PATTERN_LINE));
    }


    public static String formatDate(LocalDate localDate, String monthPatternLine) {
        if (localDate == null) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(monthPatternLine));
    }

    public static LocalDateTime getStartDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.atTime(0, 0, 0);
    }

    public static LocalDateTime getEndDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.atTime(23, 59, 59);
    }

    public static LocalDateTime secondTimeStamp2LocalDateTime(String timestamp) {
        return LocalDateTime.ofEpochSecond(Long.parseLong(timestamp), 0, ZoneOffset.ofHours(+8));
    }

    public static LocalDateTime milliToLocalDateTime(String date) {
        if (date == null) {
            return null;
        }

        return LocalDateTime.parse(date, DateTimeFormatter.ofPattern(DATETIME_Milli_PATTERN_LINE));
    }

    public static int toMinuitOfDay(LocalDateTime dateTime) {
        if (dateTime == null) {
            return 0;
        }
        return dateTime.getHour() * 60 + dateTime.getMinute();
    }

    public static String toTimeString(LocalTime startTime) {
        if (startTime == null) {
            return null;
        }
        return startTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    public static String currentSecondTimestampString() {
        return String.valueOf(currentSecondTimestamp());
    }

    public static long currentSecondTimestamp() {
        return LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
    }

    /**
     * 获取上个月月初第一天
     *
     * @return
     */
    public static LocalDate getBeforeMonthFirstDay() {
        // 创建 Calendar 对象
        Calendar calendar = Calendar.getInstance();

        // 将日期设置为上个月的第一天
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        LocalDate localDate = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        return localDate;
    }

    /**
     * 获取指定天数后的日期
     */
    public static LocalDate getDaysLaterDay(int num) {
        // 创建 Calendar 对象
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTimeInMillis(System.currentTimeMillis());
        // 将日期加上30天
        calendar.add(Calendar.DAY_OF_MONTH, num);
        // 输出结果
        return calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static String toTimeHHMMSSString(LocalTime startTime) {
        return startTime == null ? null : startTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
    }

    public static LocalTime toLocalTimeHHMMSS(String time) {
        return time == null ? null : LocalTime.parse(time, DateTimeFormatter.ofPattern("HH:mm:ss"));
    }


    public static LocalDateTime toLocalDateTimeByLocalTimeStr(String localTimeStr) {
        return LocalDateTime.of(LocalDate.now(), toLocalTime(localTimeStr));
    }

    public static String toYearMonthString(LocalDate date) {
        return date == null ? null : date.format(DateTimeFormatter.ofPattern(MONTH_PATTERN_LINE_UNDER_LINE));
    }
}
