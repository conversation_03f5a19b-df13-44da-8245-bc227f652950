package com.bcels.cies.task;

import com.bcels.cies.domain.CiesTaskDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时执行任务
 */
@Component
@Slf4j
public class Task {
    @Autowired
    private CiesTaskDomainService ciesTaskDomainService;

    /**
     * 查询第二天是否存在储能计划 不存在则拷贝
     */
    @Scheduled(cron = "0 55 23 * * ?")
    public void copyPlan() {
        long startTime = System.currentTimeMillis();
        ciesTaskDomainService.copyPlan();
        long endTime = System.currentTimeMillis();
        log.info("执行拷贝储能计划任务结束,耗费时间：{}秒", (endTime - startTime) / 1000);
    }

    /**
     * 下发储能计划
     */
    @Scheduled(cron = "0 58 23 * * ?")
    public void distributePlan() {
        long startTime = System.currentTimeMillis();
        ciesTaskDomainService.distributePlan();
        long endTime = System.currentTimeMillis();
        log.info("执行下发计划任务任务结束,耗费时间：{}秒", (endTime - startTime) / 1000);
    }

    /**
     * 第二天零点计算昨天的成本、利润、收入等数据
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void calculateProfit() {
        long startTime = System.currentTimeMillis();
        ciesTaskDomainService.calculateProfit();
        long endTime = System.currentTimeMillis();
        log.info("执行计算利润，成本任务结束,耗费时间：{}秒", (endTime - startTime) / 1000);
    }

    /**
     * 同步最新指标和测点数据
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void queryLatestIndicatorData() {
        long startTime = System.currentTimeMillis();
        ciesTaskDomainService.queryLatestIndicatorData();
        long endTime = System.currentTimeMillis();
        log.info("执行同步指标实时数据任务结束,耗费时间：{}秒", (endTime - startTime) / 1000);
    }

    /**
     * 批量更新计划审核状态
     */
    @Scheduled(cron = "0 56 23 * * ?")
    public void updatePlanStatus() {
        long startTime = System.currentTimeMillis();
        ciesTaskDomainService.updatePlanStatus();
        long endTime = System.currentTimeMillis();
        log.info("执行修改计划审核状态为已过期任务结束,耗费时间：{}秒", (endTime - startTime) / 1000);
    }

    @Scheduled(cron = "0 0 6 1 * ?")
    public void generateSettlementBill() {
        long startTime = System.currentTimeMillis();
        ciesTaskDomainService.generateSettlementBill();
        long endTime = System.currentTimeMillis();
        log.info("执行生成任务结束,耗费时间：{}秒", (endTime - startTime) / 1000);
    }
}
