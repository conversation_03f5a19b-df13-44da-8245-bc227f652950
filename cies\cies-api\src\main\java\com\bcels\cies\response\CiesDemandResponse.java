package com.bcels.cies.response;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "需量下发列表")
public class CiesDemandResponse {

    @Schema(description = "并网点名称")
    private String connectionPointName;

    @Schema(description = "上次下发需量（KW）")
    private BigDecimal lastDistributionDemand;

    @Schema(description = "上次下发时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastDistributionTime;

    @Schema(description = "本次下发需量（KW）")
    private BigDecimal distributeValue;

    @Schema(description = "指令下发类型")
    private String commandType;
    
    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "并网点id")
    private String connectionPointId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
