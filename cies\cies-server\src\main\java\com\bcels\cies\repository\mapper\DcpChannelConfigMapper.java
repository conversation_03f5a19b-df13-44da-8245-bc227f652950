package com.bcels.cies.repository.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.bcels.cies.repository.entity.DcpChannelConfigEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesPointAndIndicatorRequest;
import com.bcels.cies.response.CiesPointAndIndicatorResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 通道配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Mapper
@DS("dcp")
public interface DcpChannelConfigMapper extends BaseMapper<DcpChannelConfigEntity> {



    List<CiesPointAndIndicatorResponse> findChannelForPoint(String projectId);

    List<CiesPointAndIndicatorResponse> findEquipForPoint(@Param("request") CiesPointAndIndicatorRequest request);

    List<CiesPointAndIndicatorResponse> findTestPoint(@Param("request") CiesPointAndIndicatorRequest request);

    CiesPointAndIndicatorResponse findTestPointById(String testPointId);

    List<CiesPointAndIndicatorResponse> findEquip(String projectId);

    List<CiesPointAndIndicatorResponse> findIndicatorForOne(@Param("request") CiesPointAndIndicatorRequest request);

    List<CiesPointAndIndicatorResponse> findIndicatorForTwo(@Param("request") CiesPointAndIndicatorRequest request);

    String findEquipById(String equipId);

    String findChannelById(String cId);
}
