package com.bcels.cies.domain;


import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.bcels.cies.service.ICiesEquipInfoService;
import com.zwy.common.utils.bean.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CiesEquipInfoDomainService {

    @Autowired
    private ICiesEquipInfoService iCiesEquipInfoService;

    public PageResponse<CiesEquipInfoResponse> findEquipForPage(CiesEquipInfoRequest request) {
        return iCiesEquipInfoService.findEquipInfoForPage(request);
    }

    public void updateEquipInfo(CiesEquipInfoRequest request){
        iCiesEquipInfoService.updateEquipInfo(request);
    }

    public CiesEquipInfoResponse findEquipInfoById(String equipId){
        return iCiesEquipInfoService.findEquipInfoById(equipId);
    }

    public List<CiesEquipInfoResponse> findParentEquip(CiesEquipInfoRequest request){
        return iCiesEquipInfoService.findParentEquip(request);
    }
}
