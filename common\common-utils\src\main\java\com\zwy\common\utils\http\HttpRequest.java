package com.zwy.common.utils.http;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.util.HashMap;
import java.util.Map;


@Getter
public class HttpRequest {

    private String url;
    private HttpMethod method;        // 请求方式
    private Map<String, String> cookies;       // 发送的cookie列表
    private Map<String, String> headers;       // 发送的请求头
    private String mime;
    private String reqCharset;    // 请求的字符编码
    private String resCharset;    // 预计相应的字符编码
    private Map<String, String> params;        // 请求参数,如果是get请求会拼接到url里;如果是post请求会作为表单
    private String postString;    // post文本
    private byte[] postBody;      // post二进制块数据
    private boolean pooled;        // 是否使用http连接池
    private int connTimeout;   // 连接超时
    private int readTimeout;   // 读取超时
    private boolean followRedirect;// 是否自动处理30X跳转
    private int retryMax;      // 最大重试次数,0表示不重试
    private long retryInterval; // 重试时间间隔,毫秒为单位
    private String proxyHost;     // http代理host
    private int proxyPort;     // http代理端口
    private String proxyUser;     // http代理用户名
    private String proxyPwd;      // http代理密码

    @Setter
    private KeyStore keyStore;

    private HttpRequest() {
    }

    public static HttpRequestBuilder builder(String url, HttpMethod method) {
        return new HttpRequestBuilder(url, method);
    }

    public static HttpRequestBuilder builder(String url) {
        return new HttpRequestBuilder(url);
    }

    public boolean needProxy() {
        return StringUtils.isNotBlank(proxyHost);
    }

    public boolean needProxyAuth() {
        return StringUtils.isNotBlank(proxyUser) && StringUtils.isNotBlank(proxyPwd);
    }

    @Getter
    public static class HttpRequestBuilder {
        private String url;
        private HttpMethod method = HttpMethod.GET;
        private Map<String, String> cookies;
        private Map<String, String> headers;
        private String mime = "text/plain";
        private String reqCharset = StandardCharsets.UTF_8.name();
        private String resCharset = StandardCharsets.UTF_8.name();
        private Map<String, String> params;
        private String postString;
        private byte[] postBody;
        private boolean pooled = false;
        private int connTimeout = 5000;                         // 默认连接超时5秒
        private int readTimeout = 600000;                       // 默认读取超时5分钟
        private boolean followRedirect = true;
        private int retryMax = 0;
        private long retryInterval = 1000;
        private String proxyHost;
        private int proxyPort;
        private String proxyUser;
        private String proxyPwd;

        private HttpRequestBuilder(String url, HttpMethod method) {
            this.url = url;
            this.method = method;
            this.cookies = new HashMap<>(0);
            this.headers = new HashMap<>(0);
            this.params = new HashMap<>(0);
        }

        private HttpRequestBuilder(String url) {
            this(url, HttpMethod.GET);
        }

        public HttpRequestBuilder withRetry(int retryMax, long retryInterval) {
            this.retryMax = retryMax;
            this.retryInterval = retryInterval;
            return this;
        }

        public HttpRequestBuilder withFollowRedirect(boolean followRedirect) {
            this.followRedirect = followRedirect;
            return this;
        }

        public HttpRequestBuilder addCookie(String name, String value) {
            this.cookies.put(name, value);
            return this;
        }

        public HttpRequestBuilder addHeader(String name, String value) {
            this.headers.put(name, value);
            return this;
        }

        public HttpRequestBuilder addParam(String name, String value) {
            this.params.put(name, value);
            return this;
        }

        public HttpRequestBuilder withHttpProxy(String proxyHost, int proxyPort, String proxyUser, String proxyPwd) {
            this.proxyHost = proxyHost;
            this.proxyPort = proxyPort;
            this.proxyUser = proxyUser;
            this.proxyPwd = proxyPwd;
            return this;
        }

        public HttpRequestBuilder withMime(String mime) {
            this.mime = mime;
            return this;
        }

        public HttpRequestBuilder withRequstCharset(String charset) {
            this.reqCharset = charset;
            return this;
        }

        public HttpRequestBuilder withResponseCharset(String charset) {
            this.resCharset = charset;
            return this;
        }

        public HttpRequestBuilder withPostString(String string) {
            this.postString = string;
            return this;
        }

        public HttpRequestBuilder withPostBody(byte[] body) {
            this.postBody = body;
            return this;
        }

        public HttpRequestBuilder withPooled(boolean pooled) {
            this.pooled = pooled;
            return this;
        }

        public HttpRequestBuilder withConnTimeout(int connTimeout) {
            this.connTimeout = connTimeout;
            return this;
        }

        public HttpRequestBuilder withReadTimeout(int readTimeout) {
            this.readTimeout = readTimeout;
            return this;
        }

        public HttpRequest build() {
            HttpRequest request = new HttpRequest();
            request.url = this.url;
            request.method = this.method;
            request.cookies = this.cookies;
            request.headers = this.headers;
            request.params = this.params;
            request.mime = this.mime;
            request.reqCharset = this.reqCharset;
            request.resCharset = this.resCharset;
            request.postString = this.postString;
            request.postBody = this.postBody;
            request.pooled = this.pooled;
            request.connTimeout = this.connTimeout;
            request.readTimeout = this.readTimeout;
            request.proxyHost = proxyHost;
            request.proxyPort = proxyPort;
            request.proxyUser = proxyUser;
            request.proxyPwd = proxyPwd;
            request.retryMax = this.retryMax;
            return request;
        }
    }

}
