package com.bcels.cies.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zwy.common.utils.bean.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@Schema(description = "储能月度结算-收益")
public class CiesEssMonthlyBillIncomeRequest implements Serializable {

    @Schema(description = "储能结算主键")
    private String essBillId;

    @Schema(description = "企业")
    private String enterpriseName;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "申诉到期时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime disputeCountdown;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "结算状态")
    private String settlementStatus;

    @Schema(description = "开具方式")
    private String issueMethod;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "结算月份")
    private String settlementMonth;

    @Schema(description = "峰谷价差总收益（元）")
    private BigDecimal peakValleyRevenue;

    @Schema(description = "甲方收益（元）")
    private BigDecimal partyAIncome;

    @Schema(description = "乙方收益（元）")
    private BigDecimal partyBIncome;

    @Schema(description = "是否单独计算深谷")
    private Integer isCalDeep;

    @Schema(description = "甲方分成比例(%)")
    private BigDecimal partyARatio;

    @Schema(description = "乙方分成比例(%)")
    private BigDecimal partyBRatio;

    @Schema(description = "甲方分成金额(元)")
    private BigDecimal partyAAmount;

    @Schema(description = "乙方分成金额(元)")
    private BigDecimal partyBAmount;

    @Schema(description = "税差计算比例")
    private BigDecimal taxAdjustmentRate;

    @Schema(description = "税差金额(元)")
    private BigDecimal taxAdjustmentAmount;

    @Schema(description = "用户结算附件地址")
    private String userAttachmentUrl;

    @Schema(description = "登记结算单PDF附件地址")
    private String pdfAttachmentUrl;

    @Schema(description = "登记结算单Excel/Word附件地址")
    private String officeAttachmentUrl;

    @Schema(description = "审核人")
    private String reviewer;

    @Schema(description = "审核时间")
    private LocalDateTime reviewTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "峰谷价差列表")
    private List<CiesPeakValleyPriceRequest> billList;
}
