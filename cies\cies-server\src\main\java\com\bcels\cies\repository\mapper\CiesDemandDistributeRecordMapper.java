package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcels.cies.repository.entity.CiesDemandDistributeRecordEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesControlHisRequest;
import com.bcels.cies.request.CiesDemandRequest;
import com.bcels.cies.response.CiesControlHisRecordsListResponse;
import com.bcels.cies.response.CiesDemandResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 需量下发记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Mapper
public interface CiesDemandDistributeRecordMapper extends BaseMapper<CiesDemandDistributeRecordEntity> {

    IPage<CiesDemandResponse> findDemandRecordsForPage(@Param("page") IPage<CiesDemandResponse> page, @Param("request") CiesDemandRequest request);
}
