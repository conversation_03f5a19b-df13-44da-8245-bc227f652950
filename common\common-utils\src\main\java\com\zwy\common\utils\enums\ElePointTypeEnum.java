package com.zwy.common.utils.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum ElePointTypeEnum {
    E96(96, 15),
    E1440(1440, 1),
    E288(288, 5);

    private int point;

    private int minuteInterval;

    public static ElePointTypeEnum findMinuteInterval(int minuteInterval) {
        return Stream.of(ElePointTypeEnum.values()).filter(f -> f.getMinuteInterval() == minuteInterval).findFirst().orElse(null);
    }
}
