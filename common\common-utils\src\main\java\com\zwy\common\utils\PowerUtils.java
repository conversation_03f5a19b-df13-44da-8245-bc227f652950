package com.zwy.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.zwy.common.utils.BigDecimalUtils.*;


public class PowerUtils {


    public static BigDecimal toMW(BigDecimal power) {
        if (power == null) {
            return BigDecimal.ZERO;
        }
        return divide4(power, BigDecimalUtils.THOUSAND);
    }

    public static BigDecimal toMW5(BigDecimal power) {
        if (power == null) {
            return BigDecimal.ZERO;
        }
        return divide5(power, BigDecimalUtils.THOUSAND);
    }

    public static BigDecimal toKW(BigDecimal power) {
        if (power == null) {
            return BigDecimal.ZERO;
        }
        return power.multiply(BigDecimalUtils.THOUSAND);
    }

}
