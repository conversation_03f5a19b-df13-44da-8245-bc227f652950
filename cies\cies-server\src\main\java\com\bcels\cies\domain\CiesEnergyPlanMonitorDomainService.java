package com.bcels.cies.domain;

import com.alibaba.fastjson.JSONObject;
import com.bcels.cies.client.CiesInternalInterfaceClient;
import com.bcels.cies.infrastructure.utils.ExcelExportUtil;
import com.bcels.cies.model.CiesPlanMonitorListExport;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.request.CiesEnergyPlanMonitorRequest;
import com.bcels.cies.request.CiesQueryIndicatorsRequest;
import com.bcels.cies.response.*;
import com.bcels.cies.service.ICiesEnergyStoragePlanService;
import com.bcels.cies.service.ICiesSpecialIndicatorsService;
import com.zwy.common.utils.BeanCopyUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
@Service
public class CiesEnergyPlanMonitorDomainService {

    @Autowired
    private ICiesEnergyStoragePlanService iCiesEnergyStoragePlanService;

    @Autowired
    private ICiesSpecialIndicatorsService iCiesSpecialIndicatorsService;

    @Autowired
    private CiesInternalInterfaceClient client;

    private static final List<String> ALL_DAY_MINUTES;

    static {
        // 初始化静态列表（仅执行一次）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        ALL_DAY_MINUTES = Collections.unmodifiableList(
                IntStream.range(0,  24 * 60) // 0 ~ 1439 分钟
                        .mapToObj(minute ->
                                LocalTime.of(minute  / 60, minute % 60).format(formatter)
                        )
                        .collect(Collectors.toList())
        );
    }

    /**
     *  获取项目类型为“电化学储能的项目
     * @param request
     */
    public CiesPlanMonitorViewResponse findPowerBrokenLine(CiesEnergyPlanMonitorRequest request) {
        CiesPlanMonitorViewResponse result = new CiesPlanMonitorViewResponse();
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        String projectId = request.getProjectId();
        // 查询计划功率
        LocalDate startDate = LocalDate.parse(request.getStartTime(), formatter1);
        List<String> startTimeListResult = new ArrayList<>();
        List<BigDecimal> plannedPowerListResult1 = new ArrayList<>();
        List<BigDecimal> plannedPowerListResult2 = new ArrayList<>();
        List<BigDecimal> plannedPowerListResult3 = new ArrayList<>();
        List<BigDecimal> plannedPowerListResult4 = new ArrayList<>();
        List<BigDecimal> plannedPowerListResult5 = new ArrayList<>();
        LocalDate endDate = LocalDate.parse(request.getEndTime(), formatter1);
        while (!startDate.isAfter(endDate)) {
            CiesEnergyPlanListRequest planRequest = new CiesEnergyPlanListRequest();
            planRequest.setProjectId(projectId);
            planRequest.setPlanDate(startDate.toString().replace("-", "/"));
            List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(planRequest);
            // 计划功率 需要拆分到一分钟级别
            List<CiesEnergyStoragePlanResponse> powerPlan = splitToMinuteIntervals1(stationEnergyPlan);

            List<String> startTimeList = new ArrayList<>();
            List<BigDecimal> plannedPowerList1 = new ArrayList<>();
            List<BigDecimal> plannedPowerList2 = new ArrayList<>();
            List<BigDecimal> plannedPowerList3 = new ArrayList<>();
            List<BigDecimal> plannedPowerList4 = new ArrayList<>();
            List<BigDecimal> plannedPowerList5 = new ArrayList<>();

            // 批量查询特殊指标对应指标id
            List<String> indicatorList = Stream.of("储能站有功功率", "工厂用电有功功率", "工厂总有功功率", "储能站SOC").toList();
            // 查询数据中台其他功率-储能站有功功率，工厂用电有功功率，工厂总有功功率，储能站SOC
            List<CiesSpecialIndicatorsResponse> specialIndicatorList = iCiesSpecialIndicatorsService.findSpecialIndicatorList(indicatorList, projectId);
            if (specialIndicatorList != null) {
                // 遍历获取指标名称和指标id
                Map<String, CiesSpecialIndicatorsResponse> indicatorMap = specialIndicatorList.stream()
                        .collect(Collectors.toMap(
                                CiesSpecialIndicatorsResponse::getIndicatorName,
                                item -> item
                        ));
                // 根据日期查询储能站有功功率
                CiesSpecialIndicatorsResponse response = indicatorMap.get("储能站有功功率");
                List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses = batchQueryIndicator(response, startDate);
                Map<String, BigDecimal> stringBigDecimalMap = convertToMap(ciesHisIndicatorsDataResponses);
                // 根据日期查询储能站有功功率
                CiesSpecialIndicatorsResponse response1 = indicatorMap.get("工厂用电有功功率");
                List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses1 = batchQueryIndicator(response1, startDate);
                Map<String, BigDecimal> stringBigDecimalMap1 = convertToMap(ciesHisIndicatorsDataResponses1);


                // 根据日期查询储能站有功功率
                CiesSpecialIndicatorsResponse response2 = indicatorMap.get("工厂总有功功率");
                List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses2 = batchQueryIndicator(response2, startDate);
                Map<String, BigDecimal> stringBigDecimalMap2 = convertToMap(ciesHisIndicatorsDataResponses2);


                // 根据日期查询储能站有功功率
                CiesSpecialIndicatorsResponse response3 = indicatorMap.get("储能站SOC");
                List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses3 = batchQueryIndicator(response3, startDate);
                Map<String, BigDecimal> stringBigDecimalMap3 = convertToMap(ciesHisIndicatorsDataResponses3);

                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime baseTime = LocalDateTime.parse(startDate  + " 00:00:00", formatter);

                for (int i = 0; i < ALL_DAY_MINUTES.size(); i++) {
                    String current = baseTime.format(formatter);
                    startTimeList.add(current.substring(0,current.lastIndexOf(":")));
                    if (powerPlan != null && i < powerPlan.size()) {
                        CiesEnergyStoragePlanResponse tempResponse = powerPlan.get(i);
                        plannedPowerList1.add(tempResponse.getPlannedPower());
                    }

                    if (stringBigDecimalMap != null) {
                        if (stringBigDecimalMap.containsKey(current)) {
                            plannedPowerList2.add(stringBigDecimalMap.get(current));
                        } else {
                            plannedPowerList2.add(null);
                        }
                    }

                    if (stringBigDecimalMap1 != null) {
                        if (stringBigDecimalMap1.containsKey(current)) {
                            plannedPowerList3.add(stringBigDecimalMap1.get(current));
                        } else {
                            plannedPowerList3.add(null);
                        }
                    }

                    if (stringBigDecimalMap2 != null) {
                        if (stringBigDecimalMap2.containsKey(current)) {
                            plannedPowerList4.add(stringBigDecimalMap2.get(current));
                        } else {
                            plannedPowerList4.add(null);
                        }
                    }
                    if (stringBigDecimalMap3 != null) {
                        if (stringBigDecimalMap3.containsKey(current)) {
                            plannedPowerList5.add(stringBigDecimalMap3.get(current));
                        } else {
                            plannedPowerList5.add(null);
                        }
                    }
                    baseTime = baseTime.plusMinutes(1);
                }
                startTimeListResult.addAll(startTimeList);
                plannedPowerListResult1.addAll(plannedPowerList1);
                plannedPowerListResult2.addAll(plannedPowerList2);
                plannedPowerListResult3.addAll(plannedPowerList3);
                plannedPowerListResult4.addAll(plannedPowerList4);
                plannedPowerListResult5.addAll(plannedPowerList5);

            }
            result.setTime(startTimeListResult);
            result.setPlanPower(plannedPowerListResult1);
            result.setStorageStation(plannedPowerListResult2);
            result.setFactoryElecActivePower(plannedPowerListResult3);
            result.setFactoryElecTotalPower(plannedPowerListResult4);
            result.setStorageStationSOC(plannedPowerListResult5);
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    /**
     * 导出
     * @param request
     * @param response
     * @throws IOException
     */
    public void export(CiesEnergyPlanMonitorRequest request, HttpServletResponse response) throws IOException {
        List<CiesPlanMonitorListResponse> powerBrokenLine = exportData(request);
        List<CiesPlanMonitorListExport> ciesPlanMonitorListExports = BeanCopyUtil.copyListProperties(powerBrokenLine, CiesPlanMonitorListExport::new);
        String customFileName = "储能计划监控" + System.currentTimeMillis() + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        response.setHeader("Content-Disposition", "attachment; filename=" + customFileName);

        byte[] excelBytes = ExcelExportUtil.exportToExcel(ciesPlanMonitorListExports, "储能计划监控",CiesPlanMonitorListExport.class);
        response.getOutputStream().write(excelBytes);
    }


    /**
     * 拆分计划为一分钟
     *
     * @param originalPlans
     * @return
     */
    private List<CiesEnergyStoragePlanResponse> splitToMinuteIntervals1(List<CiesEnergyStoragePlanResponse> originalPlans) {
        // 一分钟维度并网点计划
        List<CiesEnergyStoragePlanResponse> newPlanList = new ArrayList<>();

        // 拆分该并网点下所有计划为一分钟级别，便于对不同并网点时刻并集
        originalPlans.forEach(plan -> {
            List<CiesEnergyStoragePlanResponse> temp = splitPlan(plan, 1);
            newPlanList.addAll(temp);
        });
        newPlanList.forEach(record  -> {
            BigDecimal power = Optional.ofNullable(record.getPlannedPower()).orElse(BigDecimal.ZERO);
            BigDecimal adjustedPower = switch (record.getChargeDischargeType())  {
                case "DISCHARGE" -> power;       // 放电保持正值
                case "CHARGE" -> power.negate();  // 充电转为负值
                default -> power;                // 其他情况不变
            };
            record.setPlannedPower(adjustedPower);  // 直接修改原对象属性
        });
        return newPlanList;
    }
    public  List<CiesEnergyStoragePlanResponse> splitPlan(CiesEnergyStoragePlanResponse originalPlan, int intervalMinutes) {

        List<CiesEnergyStoragePlanResponse> result = new ArrayList<>();
        LocalTime start = LocalTime.parse(originalPlan.getStartTime());
        LocalTime end = LocalTime.parse(originalPlan.getEndTime());

        // 一分钟时间相同不拆分
        if (intervalMinutes == 1 && start.equals(end)) {
            result.add(originalPlan);
            return result;
        }
        return splitByMinute(originalPlan, start, end);
    }

    private static List<CiesEnergyStoragePlanResponse> splitByMinute(CiesEnergyStoragePlanResponse original, LocalTime start, LocalTime end) {
        List<CiesEnergyStoragePlanResponse> plans = new ArrayList<>();
        LocalTime current = start;
        // 跨天标识
        boolean flag  = false;
        if ("00:00".equals(end.toString())){
            end = LocalTime.of(23,59);
            flag = true;
        }
        while (current.isBefore(end))  {
            CiesEnergyStoragePlanResponse plan = BeanCopyUtil.copyProperties(original, CiesEnergyStoragePlanResponse::new);
            plan.setStartTime(current.toString());
            plan.setEndTime(current.plusMinutes(1).toString());
            plans.add(plan);
            current = current.plusMinutes(1);
        }
        // 24点数据特殊处理
        if (flag){
            CiesEnergyStoragePlanResponse plan = BeanCopyUtil.copyProperties(original, CiesEnergyStoragePlanResponse::new);
            plan.setStartTime(current.toString());
            plan.setEndTime("00:00");
            plans.add(plan);
        }
        return plans;
    }

    /**
     * 获取指定日期全天历史指标数据（数据中台）
     * @param response
     * @param startDate
     * @return
     */
    private List<CiesHisIndicatorsDataResponse> batchQueryIndicator(CiesSpecialIndicatorsResponse response, LocalDate startDate) {
        if (response.getRelateIndicatorId() == null){
            return null;
        }
        List<String> specialIds = new ArrayList<>();
        specialIds.add(response.getRelateIndicatorId());
        CiesQueryIndicatorsRequest request1 = new CiesQueryIndicatorsRequest();
        request1.setIndicatorIds(specialIds);
        request1.setStartTime(LocalDateTime.of(startDate, LocalTime.of(0, 0, 0)));
        request1.setEndTime(LocalDateTime.of(startDate, LocalTime.of(23, 59, 59)));
        request1.setDataType(response.getRelateDataType());
        Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap = client.batchQueryHisData(request1);
        log.info("储能计划监控，获取指标数据 请求参数为：{}，返回响应结果：{}", JSONObject.toJSONString(request1), stringListMap);
        if (stringListMap == null) {
            return null;
        }
        List<CiesHisIndicatorsDataResponse> responseList = stringListMap.get(response.getRelateIndicatorId());
        if (CollectionUtils.isEmpty(responseList)){
            return null;
        }

        responseList.sort(Comparator.comparing(CiesHisIndicatorsDataResponse::getUpdateTime));
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        responseList.forEach(obj -> {
            // 截断毫秒（保留到秒）
            LocalDateTime truncatedTime = obj.getUpdateTime().truncatedTo(ChronoUnit.SECONDS);

            // 格式化为字符串
            String formattedTime = truncatedTime.format(outputFormatter);
            obj.setUpdateTimeStr(formattedTime);
        });
        return responseList;
    }

    /**
     *  获取项目类型为“电化学储能的项目”
     * @param request
     */
    public List<CiesPlanMonitorListResponse> exportData(CiesEnergyPlanMonitorRequest request) {
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        List<CiesPlanMonitorListResponse> result = new ArrayList<>();
        String projectId = request.getProjectId();
        // 查询计划功率
        LocalDate startDate = LocalDate.parse(request.getStartTime(), formatter1);
        LocalDate endDate = LocalDate.parse(request.getEndTime(), formatter1);
        while (!startDate.isAfter(endDate)) {
            CiesEnergyPlanListRequest planRequest = new CiesEnergyPlanListRequest();
            planRequest.setProjectId(projectId);
            planRequest.setPlanDate(startDate.toString().replace("-", "/"));
            List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(planRequest);
            // 计划功率 需要拆分到一分钟级别
            List<CiesEnergyStoragePlanResponse> powerPlan = splitToMinuteIntervals1(stationEnergyPlan);

            // 批量查询特殊指标对应指标id
            List<String> indicatorList = Stream.of("储能站有功功率", "工厂用电有功功率", "工厂总有功功率", "储能站SOC").toList();
            // 查询数据中台其他功率-储能站有功功率，工厂用电有功功率，工厂总有功功率，储能站SOC
            List<CiesSpecialIndicatorsResponse> specialIndicatorList = iCiesSpecialIndicatorsService.findSpecialIndicatorList(indicatorList, projectId);
            if (specialIndicatorList != null) {
                // 遍历获取指标名称和指标id
                Map<String, CiesSpecialIndicatorsResponse> indicatorMap = specialIndicatorList.stream()
                        .collect(Collectors.toMap(
                                CiesSpecialIndicatorsResponse::getIndicatorName,
                                item -> item
                        ));
                // 根据日期查询储能站有功功率
                CiesSpecialIndicatorsResponse response = indicatorMap.get("储能站有功功率");
                List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses = batchQueryIndicator(response, startDate);
                Map<String, BigDecimal> stringBigDecimalMap = convertToMap(ciesHisIndicatorsDataResponses);
                // 根据日期查询储能站有功功率
                CiesSpecialIndicatorsResponse response1 = indicatorMap.get("工厂用电有功功率");
                List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses1 = batchQueryIndicator(response1, startDate);
                Map<String, BigDecimal> stringBigDecimalMap1 = convertToMap(ciesHisIndicatorsDataResponses1);


                // 根据日期查询储能站有功功率
                CiesSpecialIndicatorsResponse response2 = indicatorMap.get("工厂总有功功率");
                List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses2 = batchQueryIndicator(response2, startDate);
                Map<String, BigDecimal> stringBigDecimalMap2 = convertToMap(ciesHisIndicatorsDataResponses2);


                // 根据日期查询储能站有功功率
                CiesSpecialIndicatorsResponse response3 = indicatorMap.get("储能站SOC");
                List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses3 = batchQueryIndicator(response3, startDate);
                Map<String, BigDecimal> stringBigDecimalMap3 = convertToMap(ciesHisIndicatorsDataResponses3);


                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime baseTime = LocalDateTime.parse(startDate + " 00:00:00", formatter);

                for (int i = 0; i < ALL_DAY_MINUTES.size(); i++) {
                    String current = baseTime.format(formatter);
                    CiesPlanMonitorListResponse temp = new CiesPlanMonitorListResponse();
                    temp.setTime(current.substring(0, current.lastIndexOf(":")));
                    if (powerPlan != null && i < powerPlan.size()) {
                        CiesEnergyStoragePlanResponse tempResponse = powerPlan.get(i);
                        temp.setPlanPower(tempResponse.getPlannedPower());
                    }

                    if (stringBigDecimalMap != null) {
                        if (stringBigDecimalMap.containsKey(current)) {
                            temp.setStorageStation(stringBigDecimalMap.get(current));

                        } else {
                            temp.setStorageStation((null));
                        }
                    }

                    if (stringBigDecimalMap1 != null) {
                        if (stringBigDecimalMap1.containsKey(current)) {
                            temp.setFactoryElecActivePower(stringBigDecimalMap1.get(current));
                        } else {
                            temp.setFactoryElecActivePower(null);
                        }
                    }

                    if (stringBigDecimalMap2 != null) {
                        if (stringBigDecimalMap2.containsKey(current)) {
                            temp.setFactoryElecTotalPower(stringBigDecimalMap2.get(current));
                        } else {
                            temp.setFactoryElecTotalPower(null);
                        }
                    }
                    if (stringBigDecimalMap3 != null) {
                        if (stringBigDecimalMap3.containsKey(current)) {
                            temp.setStorageStationSOC(stringBigDecimalMap3.get(current));
                        } else {
                            temp.setStorageStationSOC(null);
                        }
                    }
                    result.add(temp);
                    baseTime = baseTime.plusMinutes(1);

                }
            }
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, BigDecimal> convertToMap(List<CiesHisIndicatorsDataResponse> ciesHisIndicatorsDataResponses) {
        if (CollectionUtils.isEmpty(ciesHisIndicatorsDataResponses)){
            return null;
        }
        return ciesHisIndicatorsDataResponses.stream()
                .collect(Collectors.toMap(
                        CiesHisIndicatorsDataResponse::getUpdateTimeStr, // Key: updateTimeStr
                        CiesHisIndicatorsDataResponse::getIndicatorValue, // Value: indicatorValue
                        (existingValue, newValue) -> existingValue // 处理重复键的策略（保留旧值）
                ));
    }
}
