package com.zwy.common.utils.bean;

import lombok.*;

import java.io.Serializable;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PageRequest implements Serializable {

    private long page = 1;

    private long pageSize = 10;


    public long findLimit() {
        return pageSize;
    }

    public long findOffset() {
        return page >= 1 ? (page - 1) * pageSize : 0;
    }
}
