package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@Schema(description = "小程序-收益页面")
public class CiesMobileIncomeResponse implements Serializable {

    private String essBillId;

    @Schema(description = "结算单项目名称")
    private String projectName;

    @Schema(description = "月份")
    private List<String> month;

    @Schema(description = "甲方收益（用户收益）")
    private List<BigDecimal> partyAIncome;

    @Schema(description = "乙方收益（投资收益）")
    private List<BigDecimal> partyBIncome;

    @Schema(description = "峰谷价差收益")
    private List<BigDecimal> peakValleyRevenue;

    @Schema(description = "累计甲方收益（用户收益）")
    private BigDecimal totalPartyAIncome;

    @Schema(description = "累计乙方收益（投资收益）")
    private BigDecimal totalPartyBIncome;

    @Schema(description = "累计峰谷价差收益")
    private BigDecimal totalPeakValleyRevenue;
}

