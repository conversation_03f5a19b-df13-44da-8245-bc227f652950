package com.bcels.cies.domain;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bcels.cies.emuns.SpecialIndicatorEnum;
import com.bcels.cies.infrastructure.utils.RedisUtils;
import com.bcels.cies.infrastructure.utils.RestTemplateUtil;
import com.bcels.cies.repository.entity.*;
import com.bcels.cies.request.CiesSynDeleteRequest;
import com.bcels.cies.request.CiesSynEquipRequest;
import com.bcels.cies.request.CiesSynIndicatorRequest;
import com.bcels.cies.request.CiesSynProjectRequest;
import com.bcels.cies.response.CiesEsPlanRuleResponse;
import com.bcels.cies.service.*;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.IdGenUtil;
import com.zwy.common.utils.enums.YesOrNo;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.asm.Advice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CiesDataMiddlePlatformService {

    @Value("${app.auth.project-url}")
    private String synProjectUrl;

    private static final String FAIL_CODE = "500";

    @Autowired
    private ICiesProjectInfoService ciesProjectInfoService;

    @Autowired
    private ICiesEsPlanRuleService iCiesEsPlanRuleService;

    @Autowired
    private ICiesEquipInfoService iCiesEquipInfoService;

    @Autowired
    private ICiesIndicatorsInfoService iCiesIndicatorsInfoService;

    @Autowired
    private ICiesSpecialIndicatorsService iCiesSpecialIndicatorsService;

    @Autowired
    private IDcpChannelConfigService iDcpChannelConfigService;

    @Autowired
    private RestTemplateUtil restTemplateUtil;

    /**
     * 同步项目信息
     *
     * @param request
     */
    @Transactional
    public void synProjectInfo(CiesSynProjectRequest request) {
        log.info("数据中台同步-项目信息为：{}",JSONObject.toJSONString(request));
        CiesProjectInfoEntity entity = BeanCopyUtil.copyProperties(request, CiesProjectInfoEntity::new);
        CiesEsPlanRuleEntity esPlanRuleEntity;
        CiesProjectInfoEntity byId = ciesProjectInfoService.getById(request.getProjectId());
        if (!ObjectUtils.isEmpty(byId) && byId.getDr() == 0) {
            entity.setUpdateBy("服务器");
            entity.setCreateBy(byId.getCreateBy());
            entity.setCreateTime(byId.getCreateTime());
            ciesProjectInfoService.updateBaseProject(entity);
            CiesEsPlanRuleResponse byProjectId = iCiesEsPlanRuleService.findByProjectId(request.getProjectId());
            esPlanRuleEntity = new CiesEsPlanRuleEntity();
            esPlanRuleEntity.setRuleId(byProjectId.getRuleId());
            esPlanRuleEntity.setTimeDimension(request.getTimeDimension());
            esPlanRuleEntity.setUpdateBy("服务器");
            iCiesEsPlanRuleService.updateRule(esPlanRuleEntity);
            pushProjectInfoAsync(request.getProjectId(), request.getProName(), request.getTenantCode(), "update");
        } else {
            entity.setCreateTime(LocalDateTime.now());
            entity.setCreateBy("服务器");
            ciesProjectInfoService.save(entity);
            esPlanRuleEntity = new CiesEsPlanRuleEntity();
            esPlanRuleEntity.setRuleId(IdGenUtil.genUniqueId());
            esPlanRuleEntity.setTimeDimension(request.getTimeDimension());
            esPlanRuleEntity.setCreateTime(LocalDateTime.now());
            esPlanRuleEntity.setCreateBy("服务器");
            esPlanRuleEntity.setDr(YesOrNo.NO.getCode());
            esPlanRuleEntity.setProjectId(request.getProjectId());
            iCiesEsPlanRuleService.save(esPlanRuleEntity);

            // 默认生成特殊业务指标
            List<CiesSpecialIndicatorsEntity> list = new ArrayList<>();
            for (SpecialIndicatorEnum metric : SpecialIndicatorEnum.values())  {
                CiesSpecialIndicatorsEntity indicatorsEntity = new CiesSpecialIndicatorsEntity();
                indicatorsEntity.setSpecialIndicatorsId(IdGenUtil.genUniqueId());
                indicatorsEntity.setProjectId(request.getProjectId());
                indicatorsEntity.setIndicatorName(metric.getName());
                indicatorsEntity.setUnit(metric.getUnit());
                indicatorsEntity.setCreateBy("服务器");
                indicatorsEntity.setCreateTime(LocalDateTime.now());
                indicatorsEntity.setDr(YesOrNo.NO.getCode());
                list.add(indicatorsEntity);
            }
            iCiesSpecialIndicatorsService.saveBatch(list);
            pushProjectInfoAsync(request.getProjectId(), request.getProName(), request.getTenantCode(), "add");
        }
    }

    /**
     * 同步设备
     *
     * @param request
     */
    public void synEquipInfo(CiesSynEquipRequest request) {
        log.info("数据中台同步-设备信息为：{}",JSONObject.toJSONString(request));
        CiesEquipInfoEntity ciesEquipInfoEntity = BeanCopyUtil.copyProperties(request, CiesEquipInfoEntity::new);
        CiesEquipInfoEntity byId = iCiesEquipInfoService.getById(request.getEquipId());
        if (!ObjectUtils.isEmpty(byId) ) {
            ciesEquipInfoEntity.setUpdateBy("服务器");
            iCiesEquipInfoService.updateEquip(ciesEquipInfoEntity);
        } else {
            ciesEquipInfoEntity.setCreateTime(LocalDateTime.now());
            ciesEquipInfoEntity.setCreateBy("服务器");
            ciesEquipInfoEntity.setDr(YesOrNo.NO.getCode());
            ciesEquipInfoEntity.setProjectId(request.getProjectId());
            // 默认设备级别为1
            ciesEquipInfoEntity.setLevel(1);
            iCiesEquipInfoService.save(ciesEquipInfoEntity);
        }
    }


    /**
     * 同步指标与测点
     *
     * @param request
     */
    public void synIndicatorInfo(CiesSynIndicatorRequest request) {
        log.info("数据中台同步-指标与测点信息为：{}",JSONObject.toJSONString(request));
        String channelName = iDcpChannelConfigService.findChannelById(request.getCId());
        String equip = iDcpChannelConfigService.findEquipById(request.getEquipId());
        String equipName = equip != null ? equip : request.getEquipName();
        request.setChannelName(channelName);
        request.setEquipName(equipName);
        CiesIndicatorsInfoEntity ciesIndicatorsInfoEntity = BeanCopyUtil.copyProperties(request, CiesIndicatorsInfoEntity::new);
        CiesIndicatorsInfoEntity byId = iCiesIndicatorsInfoService.getById(request.getIndicatorId());
        if (!ObjectUtils.isEmpty(byId) && byId.getDr() == 0) {
            iCiesIndicatorsInfoService.updateSynIndicator(request);
        } else {
            // 数据名称处理初始化
            if ("测点".equals(request.getDataType())){
                String pointName = String.format("%s-%s-%s", channelName, equipName, request.getDataName());
                ciesIndicatorsInfoEntity.setDataName(pointName);
            }else{
                ciesIndicatorsInfoEntity.setDataName(request.getDataName());
            }
            // 页面展示数据
            ciesIndicatorsInfoEntity.setPageDisplayName(request.getDataName());
            ciesIndicatorsInfoEntity.setProjectId(request.getProjectId());
            ciesIndicatorsInfoEntity.setCreateTime(LocalDateTime.now());
            ciesIndicatorsInfoEntity.setCreateBy("服务器");
            ciesIndicatorsInfoEntity.setDr(YesOrNo.NO.getCode());
            iCiesIndicatorsInfoService.save(ciesIndicatorsInfoEntity);
        }
    }

    /**
     * 删除项目，设备，指标
     *
     * @param request
     */
    public void synDeleteRecord(CiesSynDeleteRequest request) {
        log.info("数据中台同步-删除项目，设备，测点与指标：{}", JSONObject.toJSONString(request));
        List<String> list = Arrays.stream(request.getBusId().split(",")).toList();
        if ("项目".equals(request.getDeleteType())) {
            UpdateWrapper<CiesProjectInfoEntity> wrapper = new UpdateWrapper<>();
            wrapper.in("project_id", list)
                    .set("dr", YesOrNo.YES.getCode())
                    .set("update_time", LocalDateTime.now())
                    .set("update_by","服务器");

            ciesProjectInfoService.update(null, wrapper);
            pushProjectInfoAsync(request.getBusId(), null, null, "delete");
        } else if ("设备".equals(request.getDeleteType())) {
            UpdateWrapper<CiesEquipInfoEntity> wrapper = new UpdateWrapper<>();
            wrapper.in("equip_id", list)
                    .set("dr", YesOrNo.YES.getCode())
                    .set("update_time", LocalDateTime.now())
                    .set("update_by","服务器");
            iCiesEquipInfoService.update(null, wrapper);
        } else {
            if (!CollectionUtils.isEmpty(list)) {
                UpdateWrapper<CiesIndicatorsInfoEntity> wrapper = new UpdateWrapper<>();
                wrapper.in("indicator_id", list)
                        .set("dr", YesOrNo.YES.getCode())
                        .set("update_time", LocalDateTime.now())
                        .set("update_by","服务器");
                iCiesIndicatorsInfoService.update(null, wrapper);
            }
        }
    }

    @Async
    public void pushProjectInfoAsync(String projectId, String proName,String tenantCode, String sycType) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("proName", proName);
        params.put("sycType", sycType);
        params.put("tenantCode",tenantCode);
        log.info("同步兆瓦云项目，参数为：{}", params);
        try {
            JSONObject jsonObject = restTemplateUtil.postJson(synProjectUrl, null, params, JSONObject.class);
            if (FAIL_CODE.equals(jsonObject.getString("code"))) {
                log.error(" 同步项目：{}到兆瓦云失败，失败原因:{}", projectId,jsonObject.getString("message"));
            }else{
                log.info("同步项目到兆瓦云成功，响应报文：{}",jsonObject.toJSONString());
            }
        } catch (Exception e) {
            log.error(" 异步同步项目异常: {}", e.getMessage(), e);
        }
    }
}
