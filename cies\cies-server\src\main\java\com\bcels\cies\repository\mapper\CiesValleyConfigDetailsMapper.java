package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.repository.entity.CiesValleyConfigDetailsEntity;
import com.bcels.cies.response.CiesDeepResponse;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 深谷电量配置明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Mapper
public interface CiesValleyConfigDetailsMapper extends BaseMapper<CiesValleyConfigDetailsEntity> {

    CiesDeepResponse queryDeepInfo(String essBillId);
    CiesDeepResponse queryDeepInfoByDataType(String essBillId,String dataType);
}

