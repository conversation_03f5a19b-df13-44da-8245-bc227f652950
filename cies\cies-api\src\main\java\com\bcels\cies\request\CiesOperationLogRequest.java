package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "操作记录")
public class CiesOperationLogRequest extends PageRequest implements Serializable {

    @Schema(description = "操作记录主键")
    private String logId;

    @Schema(description = "储能结算主键")
    private String essBillId;
}
