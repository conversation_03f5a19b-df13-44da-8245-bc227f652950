package com.bcels.cies.request;

import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "测点与指标")
public class CiesIndicatorsInfoListRequest extends PageRequest implements Serializable {


    @Schema(description = "指标主键")
    private String indicatorId;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "项目ID")
    private String projectId;
    
    @Schema(description = "关联设备名称")
    private String relatedEquipName;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "数据名称")
    private String dataName;
    
    @Schema(description = "页面展示名称")
    private String pageDisplayName;
}
