package com.bcels.cies.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.repository.entity.CiesControlHisRecordsEntity;
import com.bcels.cies.request.CiesControlHisRequest;
import com.bcels.cies.response.CiesControlHisRecordsListResponse;
import com.zwy.common.utils.bean.PageResponse;

import java.time.LocalDateTime;
import java.util.List;

public interface ICiesControlHisRecordsService extends IService<CiesControlHisRecordsEntity> {

    PageResponse<CiesControlHisRecordsListResponse> findControlHisRecordsForPage(CiesControlHisRequest request);

    List<CiesControlHisRecordsListResponse> findControlHisRecord(String projectId, LocalDateTime current);
}
