package com.bcels.cies.infrastructure.utils;

import java.util.List;

/**
 * 批量
 */
public class BatchConverter {
    public static <T> void batchConvert(List<T> list) {
        list.parallelStream().forEach(item  -> {
            try {
                FieldConverter.convertFields(item);
            } catch (IllegalAccessException e) {
                throw new RuntimeException("字段转换失败", e);
            }
        });
    }
}
