package com.bcels.cies.domain;


import com.bcels.cies.request.CiesPointAndIndicatorRequest;
import com.bcels.cies.request.CiesSpecialIndicatorsListRequest;
import com.bcels.cies.request.CiesSpecialIndicatorsUpdateRequest;
import com.bcels.cies.response.CiesPointAndIndicatorResponse;
import com.bcels.cies.response.CiesSpecialIndicatorsResponse;
import com.bcels.cies.service.ICiesSpecialIndicatorsService;
import com.bcels.cies.service.IDcpChannelConfigService;
import com.zwy.common.utils.bean.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CiesSpecialIndicatorsDomainService {

    @Autowired
    private ICiesSpecialIndicatorsService iCiesSpecialIndicatorsService;

    @Autowired
    private IDcpChannelConfigService iDcpChannelConfigService;

    /**
     * 获取测点与指标（分页）
     *
     * @return
     */
    public PageResponse<CiesSpecialIndicatorsResponse> findIndicatorsForPage(CiesSpecialIndicatorsListRequest request) {
        return iCiesSpecialIndicatorsService.findSpecialIndicatorsForPage(request);
    }

    public void updateSpecialIndicator(CiesSpecialIndicatorsUpdateRequest request){
        iCiesSpecialIndicatorsService.updateSpecialIndicator(request);
    }
    public CiesSpecialIndicatorsResponse findSpecialIndicatorById(String specialIndicatorsId){
        return iCiesSpecialIndicatorsService.findSpecialIndicatorById(specialIndicatorsId);
    }

    public void deleteIndicatorsById(CiesSpecialIndicatorsUpdateRequest request){
        iCiesSpecialIndicatorsService.deleteIndicatorsById(request);
    }

    public List<CiesPointAndIndicatorResponse> findChannelForPoint(String projectId) {
        return iDcpChannelConfigService.findChannelForPoint(projectId);
    }


    public List<CiesPointAndIndicatorResponse> findEquipForPoint(CiesPointAndIndicatorRequest request) {
        return iDcpChannelConfigService.findEquipForPoint(request);
    }


    public List<CiesPointAndIndicatorResponse> findTestPoint(CiesPointAndIndicatorRequest request) {
        List<CiesPointAndIndicatorResponse> testPoint = iDcpChannelConfigService.findTestPoint(request);
        return new ArrayList<>(testPoint.stream()
                .collect(Collectors.toMap(
                        CiesPointAndIndicatorResponse::getRelateIndicatorId, // 以relateIndicatorId为Key
                        Function.identity(),                                  // 保留原对象
                        (existing, replacement) -> existing                 // 重复时保留已有值
                ))
                .values());
    }


    public List<CiesPointAndIndicatorResponse> findEquip(String projectId) {
        return iDcpChannelConfigService.findEquip(projectId);
    }


    public List<CiesPointAndIndicatorResponse> findIndicatorForOne(CiesPointAndIndicatorRequest request) {
        return iDcpChannelConfigService.findIndicatorForOne(request);
    }

    public List<CiesPointAndIndicatorResponse> findIndicatorForTwo(CiesPointAndIndicatorRequest request) {
        return iDcpChannelConfigService.findIndicatorForTwo(request);
    }
}
