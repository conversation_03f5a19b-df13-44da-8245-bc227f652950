package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "测点和指标（数据中台）")
public class CiesPointAndIndicatorRequest implements Serializable {

    @Schema(description = "通道名称 ")
    private String channelName;

    @Schema(description = "通道ID ")
    private String cId;

    @Schema(description = "设备名称")
    private String equipName;

    @Schema(description = "设备ID")
    private String equipId;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "测点名称")
    private String testPointName;

    @Schema(description = "测点ID")
    private String testPointId;

    @Schema(description = "一次计算指标名称")
    private String oneIndicatorName;

    @Schema(description = "一次计算指标ID")
    private String oneIndicatorId;

    @Schema(description = "二次计算指标名称")
    private String twoIndicatorName;

    @Schema(description = "二次计算指标ID")
    private String twoIndicatorId;
}
