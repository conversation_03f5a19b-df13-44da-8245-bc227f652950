package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "同步数据中台设备信息")
public class CiesSynEquipRequest implements Serializable {

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "设备名称")
    private String equipName;

    @Schema(description = "设备ID")
    private String equipId;
}
