package com.bcels.cies.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "场站监控-历史查询")
public class CiesIndicatorHisRequest implements Serializable {


    @Schema(description = "指标Id")
    private String indicatorId;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "指标名称")
    private String indicatorName;
}
