package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "储能计划表")
public class CiesEnergyStoragePlanResponse implements Serializable {

    @Schema(description = "储能计划主键")
    private String energyStoragePlanId;

    @Schema(description = "计划日期")
    private String planDate;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "充放电类型")
    private String chargeDischargeType;

    @Schema(description = "计划功率")
    private BigDecimal plannedPower;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "并网点主键")
    private String connectionPointId;

    @Schema(description = "历史记录ID")
    private String historyRecordId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "设点id")
    private Integer equipPointId;
}
