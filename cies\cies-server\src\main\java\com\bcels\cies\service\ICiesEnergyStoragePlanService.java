package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesEnergyStoragePlanEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.response.CiesEnergyStoragePlanResponse;

import java.util.List;

/**
 * <p>
 * 储能计划表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface ICiesEnergyStoragePlanService extends IService<CiesEnergyStoragePlanEntity> {


    List<CiesEnergyStoragePlanResponse> findStationEnergyPlan(CiesEnergyPlanListRequest request);

    List<CiesEnergyStoragePlanResponse> findAllConnPointPlan(CiesEnergyPlanListRequest request);

    List<CiesEnergyStoragePlanResponse> findConnPlanByConnIds(List<String> connIds, String planDate);

    List<CiesEnergyStoragePlanResponse> findConfirmedPlan(List<String> projectIdList, String date);

    void batchDelete(CiesEnergyPlanListRequest request);

}
