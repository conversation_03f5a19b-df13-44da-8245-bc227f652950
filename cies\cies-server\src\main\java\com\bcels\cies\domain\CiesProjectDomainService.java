package com.bcels.cies.domain;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bcels.cies.emuns.ProjectTypeEnum;
import com.bcels.cies.emuns.SpecialIndicatorEnum;
import com.bcels.cies.emuns.TimeDimensionEnum;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.infrastructure.utils.MarketApiUtil;
import com.bcels.cies.repository.entity.*;
import com.bcels.cies.request.*;
import com.bcels.cies.response.*;
import com.bcels.cies.service.*;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.IdGenUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import com.zwy.common.utils.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CiesProjectDomainService {

    private static final String AREA_REQUEST_URL = "/eesa-report/electricityPrice/electricityPricePublic/api/v4/getRegionElectricityType";
    private static final String ELE_TYPE_URL = "/eesa-report/electricityPrice/electricityPricePublic/api/v4/getElectricityTypeVoltageLevel";

    private static final int SUCCESS  = 0;

    /**
     * 并网点默认生成指标
     */
    private static final List<String> ENERGY_METRICS  = Stream.of("正向有功总电量", "正向有功尖电量", "正向有功峰电量", "正向有功平电量", "正向有功谷电量"
            , "反向有功总电量", "反向有功尖电量", "反向有功峰电量", "反向有功平电量", "反向有功谷电量").toList();

    @Autowired
    private ICiesProjectInfoService iCiesProjectInfoService;

    @Autowired
    private ICiesEsPlanRuleService iCiesEsPlanRuleService;

    @Autowired
    private ICiesConnectionPointService iCiesConnectionPointService;

    @Autowired
    private IDcpChannelConfigService iDcpChannelConfigService;

    @Autowired
    private ICiesEnterpriseInfoService iCiesEnterpriseInfoService;

    @Autowired
    private MarketApiUtil marketApiUtil;

    @Autowired
    private ICiesSpecialIndicatorsService iCiesSpecialIndicatorsService;

    /**
     * 获取项目信息（分页）
     *
     * @return
     */
    public PageResponse<CiesProjectInfoResponse> getProjectInfoForPage(CiesProjectInfoRequest request) {
        return iCiesProjectInfoService.findProjectInfoForPage(request);
    }

    public PageResponse<CiesProjectInfoResponse> findMarKetForPage(CiesProjectInfoRequest request) {
        return iCiesProjectInfoService.findMarKetForPage(request);
    }

    /**
     * 查看项目详情
     *
     * @param projectId
     * @return
     */
    public CiesProjectInfoResponse getProjectInfById(String projectId) {
        CiesProjectInfoResponse ciesProjectInfoResponse = iCiesProjectInfoService.findProjectInfoById(projectId);
        ciesProjectInfoResponse.setProjectType(ProjectTypeEnum.getDescByCode(ciesProjectInfoResponse.getProjectType()));
        if (StringUtils.isNotEmpty(ciesProjectInfoResponse.getEnterpriseId())){
            CiesEnterpriseInfoEntity byId = iCiesEnterpriseInfoService.getById(ciesProjectInfoResponse.getEnterpriseId());
            ciesProjectInfoResponse.setEnterpriseName(byId.getEnterpriseName());
        }
        return ciesProjectInfoResponse;
    }

    /**
     * 更新项目信息
     *
     * @param request
     */
    public void updateProjectInfo(CiesProjectInfoRequest request) {
        request.setProjectType(ProjectTypeEnum.getCodeByDesc(request.getProjectType()));
        CiesProjectInfoEntity byId1 = iCiesProjectInfoService.getById(request.getProjectId());
        request.setCreateBy(byId1.getCreateBy());
        request.setCreateTime(byId1.getCreateTime());
        request.setTenantCode(byId1.getTenantCode());
        iCiesProjectInfoService.updateProjectInfo(request);
        // 清理冗余条件
        request.setProjectType(null);
        List<CiesProjectInfoResponse> projectByEnterpriseId = iCiesProjectInfoService.findProjectByEnterpriseId(request);
        if (projectByEnterpriseId != null) {
            CiesEnterpriseInfoEntity byId = iCiesEnterpriseInfoService.getById(request.getEnterpriseId());
            CiesEnterpriseInfoRequest request1 = BeanCopyUtil.copyProperties(byId, CiesEnterpriseInfoRequest::new);
            request1.setProjectNum(projectByEnterpriseId.size());
            request1.setFlag(true);
            iCiesEnterpriseInfoService.updateEnterpriseInfo(request1);
        }
        // 如果是企业切换,原有企业归属项目需要同步减1
        if (!StringUtils.isEmpty(byId1.getEnterpriseId()) && !byId1.getEnterpriseId().equals(request.getEnterpriseId())){
            CiesEnterpriseInfoEntity byId2 = iCiesEnterpriseInfoService.getById(byId1.getEnterpriseId());
            CiesEnterpriseInfoRequest request2 = BeanCopyUtil.copyProperties(byId2, CiesEnterpriseInfoRequest::new);
            request2.setProjectNum(request2.getProjectNum() - 1);
            request2.setFlag(true);
            iCiesEnterpriseInfoService.updateEnterpriseInfo(request2);
        }
    }

    /**
     * 查询企业下项目
     * @param request
     * @return
     */
    public List<CiesProjectInfoResponse> findProjectByEnterpriseId(CiesProjectInfoRequest request){
        return iCiesProjectInfoService.findProjectByEnterpriseId(request);
    }

    /**
     * 查询企业下储能项目
     * @param request
     * @return
     */
    public List<CiesProjectInfoResponse> findEnergyProjectByEnterpriseId(CiesProjectInfoRequest request){
        request.setProjectType(ProjectTypeEnum.ENERGY_STORAGE.getCode());
        return iCiesProjectInfoService.findProjectByEnterpriseId(request);
    }

    /**
     * 查看下发规则
     *
     * @param projectId
     * @return
     */
    public CiesEsPlanRuleResponse findEsPlanRuleByProjectId(String projectId) {
        // 获取基础规则
        CiesEsPlanRuleResponse ciesEsPlanRuleResponse = iCiesEsPlanRuleService.findByProjectId(projectId);
        ciesEsPlanRuleResponse.setTimeDimension(TimeDimensionEnum.getDescByCode(ciesEsPlanRuleResponse.getTimeDimension()));
        // 获取并网点信息
        List<DcpChannelConfigResponse> channel = iDcpChannelConfigService.findByProjectId(projectId);
        if (channel != null) {
            List<String> channelIds = channel.stream().map(DcpChannelConfigResponse::getCId).toList();
            List<CiesConnectionPointResponse> pointResponses = iCiesConnectionPointService.findByChannelId(channelIds);
            Map<String, String> channelMap = channel.stream()
                    .map(item -> {
                        if (item.getDr()  == 1) {
                            // 创建新对象或拷贝原对象（避免修改原始数据）
                            DcpChannelConfigResponse modifiedItem = BeanCopyUtil.copyProperties(item,DcpChannelConfigResponse::new);
                            modifiedItem.setChannelName(modifiedItem.getChannelName()  + "（删除）");
                            return modifiedItem;
                        }
                        return item;
                    })
                    .collect(Collectors.toMap(
                            DcpChannelConfigResponse::getCId,
                            DcpChannelConfigResponse::getChannelName
                    ));
            if (CollectionUtils.isEmpty(pointResponses)) {
                pointResponses = new ArrayList<>();
                // 初始化并网点信息
                for (Map.Entry<String, String> entry : channelMap.entrySet()) {
                    CiesConnectionPointResponse item = new CiesConnectionPointResponse();
                    item.setChannelName(entry.getValue());
                    item.setConnectionPointName(entry.getValue());
                    item.setChannelId(entry.getKey());
                    pointResponses.add(item);
                }
            } else {
                List<CiesConnectionPointResponse> connectionPointResponses = new ArrayList<>();
                for (DcpChannelConfigResponse channel1 : channel) {

                    CiesConnectionPointResponse item = new CiesConnectionPointResponse();
                    for (CiesConnectionPointResponse conn : pointResponses) {
                        if (channel1.getCId().equals(conn.getChannelId())) {
                            item = BeanCopyUtil.copyProperties(conn, CiesConnectionPointResponse::new);
                            item.setConnectionPointName(conn.getConnectionPointName() == null ? channelMap.get(conn.getChannelId()) : conn.getConnectionPointName());
                            break;
                        }
                    }
                    item.setChannelId(channel1.getCId());
                    item.setChannelName(channelMap.get(channel1.getCId()));
                    // 如果为要默认是通道名称，不为空则并网点名称
                    item.setConnectionPointName(item.getConnectionPointName() == null ? channelMap.get(item.getChannelId()) : item.getConnectionPointName());
                    connectionPointResponses.add(item);
                }
                pointResponses = BeanCopyUtil.copyListProperties(connectionPointResponses, CiesConnectionPointResponse::new);
            }
            if (!CollectionUtils.isEmpty(pointResponses)) {
                // 优先按照sortOrder字段排序  兼容null 后续按照channelId排序
                pointResponses.sort(
                        Comparator.comparing(
                                CiesConnectionPointResponse::getSortOrder,
                                Comparator.nullsLast(Comparator.naturalOrder())
                        ).thenComparing(
                                CiesConnectionPointResponse::getChannelName,
                                Comparator.nullsLast(Comparator.naturalOrder())
                        )
                );
            }
            ciesEsPlanRuleResponse.setConnectionPointEntityList(pointResponses);
        }
        return ciesEsPlanRuleResponse;
    }

    public void updateConnPoint(CiesConnectionPointRequest request) {
        if (!ObjectUtils.isEmpty(request)) {
            CiesConnectionPointEntity ciesConnectionPointEntities = BeanCopyUtil.copyProperties(request, CiesConnectionPointEntity::new);
            ciesConnectionPointEntities.setUpdateTime(LocalDateTime.now());
            ciesConnectionPointEntities.setUpdateBy(CiesUserContext.getCurrentUser());
            ciesConnectionPointEntities.setDr(YesOrNo.NO.getCode());
            ciesConnectionPointEntities.setChannelId(request.getChannelId());
            if (ciesConnectionPointEntities.getChannelName().contains("删除")) {
                ciesConnectionPointEntities.setDr(YesOrNo.YES.getCode());
                ciesConnectionPointEntities.setConnectionPointName(ciesConnectionPointEntities.getConnectionPointName()+ "（删除）");
            }
            if (request.getIsShow() == 0) {
                ciesConnectionPointEntities.setDr(YesOrNo.YES.getCode());
            }
            if (request.getIsShow() == 1 && ciesConnectionPointEntities.getChannelName().contains("删除")) {
                throw new BusinessException("当前通道已删除，无法保存为并网点");
            }
            if (request.getIsShow() == 1 && !ciesConnectionPointEntities.getChannelName().contains("删除")){
                ciesConnectionPointEntities.setDr(YesOrNo.NO.getCode());
            }
            CiesConnectionPointEntity byId = iCiesConnectionPointService.getById(request.getConnectionPointId());
            if (ObjectUtils.isEmpty(byId)){
                ciesConnectionPointEntities.setConnectionPointId(IdGenUtil.genUniqueId());
                iCiesConnectionPointService.save(ciesConnectionPointEntities);
                // 保存并网点，同步生成并网点对应指标
                saveConnIndicator(ciesConnectionPointEntities.getConnectionPointId(), request.getProjectId(), request.getConnectionPointName(), ciesConnectionPointEntities.getDr());
            } else {
                if (YesOrNo.YES.getCode() == ciesConnectionPointEntities.getDr()){
                    // 查询是否为最后一个并网点
                    List<String> projectIdList = new ArrayList<>();
                    projectIdList.add(byId.getProjectId());
                    List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(projectIdList);
                    if (connInfo.size()  == 1 && request.getConnectionPointId().equals(connInfo.get(0).getConnectionPointId())){
                        throw new BusinessException("项目至少需要有一条并网点，不允许删除");
                    }
                }
                ciesConnectionPointEntities.setCreateTime(byId.getCreateTime());
                ciesConnectionPointEntities.setCreateBy(byId.getCreateBy());
                iCiesConnectionPointService.updateById(ciesConnectionPointEntities);
                // 更新并网点，并网点名称变化或者删除并网点 更新并网点指标
                if (!byId.getConnectionPointName().equals(request.getConnectionPointName()) || YesOrNo.YES.getCode() == ciesConnectionPointEntities.getDr()) {
                    updateConnIndicator(request.getConnectionPointId(), request.getConnectionPointName(), ciesConnectionPointEntities.getDr());
                }
            }
        }
    }

    @Transactional
    public void updateRule(CiesEsPlanRuleRequest request) {
        // 保存下发规则
        CiesEsPlanRuleResponse ciesEsPlanRuleResponse = iCiesEsPlanRuleService.findByProjectId(request.getProjectId());
        CiesEsPlanRuleEntity ciesEsPlanRuleEntity = BeanCopyUtil.copyProperties(ciesEsPlanRuleResponse, CiesEsPlanRuleEntity::new);
        BeanCopyUtil.copyProperties(request, ciesEsPlanRuleEntity);
        ciesEsPlanRuleEntity.setUpdateTime(LocalDateTime.now());
        ciesEsPlanRuleEntity.setUpdateBy(CiesUserContext.getCurrentUser());
        ciesEsPlanRuleEntity.setDr(YesOrNo.NO.getCode());
        ciesEsPlanRuleEntity.setTimeDimension(TimeDimensionEnum.getCodeByDesc(request.getTimeDimension()));
        iCiesEsPlanRuleService.updateById(ciesEsPlanRuleEntity);
    }

    /**
     * 查看并网点信息
     *
     * @param connectionPointId
     * @return
     */
    public CiesConnectionPointResponse findConnInfoById(String connectionPointId) {
        return iCiesEsPlanRuleService.findConnInfoById(connectionPointId);
    }


    /**
     * 查询市场省市
     * @return
     */
    public List<String> findArea() {
        List<String> list = new ArrayList<>();
        JSONObject jsonObject = marketApiUtil.doGet(AREA_REQUEST_URL, null, JSONObject.class);
        if (SUCCESS == jsonObject.getInteger("resp_code")) {
            JSONArray jsonArray = jsonObject.getJSONArray("datas");
            for (int i = 0; i < jsonArray.size(); i++) {
                String city = jsonArray.getString(i);
                list.add(city);
            }
        }
        return list;
    }

    public JSONArray getElectricityType(String regionName) {
        JSONArray jsonArray = new JSONArray();
        Map<String, Object> params = new HashMap<>();
        params.put("regionName",regionName);
        JSONObject jsonObject = marketApiUtil.doGet(ELE_TYPE_URL, params, JSONObject.class);
        if (0 == jsonObject.getInteger("resp_code")) {
            jsonArray = jsonObject.getJSONArray("datas");
        }
        return jsonArray;
    }

    public CiesStatisticsProjectResponse statisticsProject(){
        return iCiesProjectInfoService.statisticsProject();
    }

    public Map<String, Object> getProvinceProject(CiesProjectInfoRequest request){
        return iCiesProjectInfoService.getProvinceProject(request);
    }

     public CiesProjectInfoResponse getPowerCurve(CiesProjectInfoRequest request){
        return iCiesProjectInfoService.getPowerCurve(request);
    }

    public List<CiesProjectInfoResponse> getSettlementStatement(CiesProjectInfoRequest request){
        return iCiesProjectInfoService.getSettlementStatement(request);
    }

    public List<CiesProjectInfoResponse> getProjectInfo(){
        return iCiesProjectInfoService.getProjectInfo();
    }

    public void verificationStatement(CiesProjectInfoRequest request){
        iCiesProjectInfoService.verificationStatement(request);
    }

    /**
     * 并网点保存业务指标
     *
     * @param connPointId
     * @param projectId
     * @param connPointName
     * @param dr
     */
    private void saveConnIndicator(String connPointId, String projectId, String connPointName, Integer dr) {
        List<CiesSpecialIndicatorsEntity> list = ENERGY_METRICS.stream()
                .map(indicator -> {
                    CiesSpecialIndicatorsEntity entity = new CiesSpecialIndicatorsEntity();
                    entity.setSpecialIndicatorsId(IdGenUtil.genUniqueId());
                    entity.setProjectId(projectId);
                    entity.setConnectionPointId(connPointId);
                    entity.setIndicatorName(connPointName + "-" + indicator);
                    entity.setUnit("kWh");
                    entity.setCreateBy(" 服务器");
                    entity.setCreateTime(LocalDateTime.now());
                    entity.setDr(dr);
                    return entity;
                })
                .collect(Collectors.toList());
        iCiesSpecialIndicatorsService.saveBatch(list);
    }

    /**
     * 并网点名称修改同步指标名称
     *
     * @param connPointId
     * @param connPointName
     * @param dr
     */
    private void updateConnIndicator(String connPointId, String connPointName, Integer dr) {
        List<CiesSpecialIndicatorsResponse> connSpecialIndicators = iCiesSpecialIndicatorsService.findConnSpecialIndicators(connPointId);
        if (CollectionUtils.isEmpty(connSpecialIndicators)) {
            return;
        }
        for (CiesSpecialIndicatorsResponse indicator : connSpecialIndicators) {
            String[] split = indicator.getIndicatorName().split("-");
            String indicatorName = connPointName + "-" + split[1];
            CiesSpecialIndicatorsEntity entity = new CiesSpecialIndicatorsEntity();
            entity.setSpecialIndicatorsId(indicator.getSpecialIndicatorsId());
            entity.setIndicatorName(indicatorName);
            entity.setDataName(indicatorName);
            entity.setUpdateBy(CiesUserContext.getCurrentUser());
            entity.setUpdateTime(LocalDateTime.now());
            entity.setDr(dr);
            iCiesSpecialIndicatorsService.updateIndicatorName(entity);
        }
    }
}
