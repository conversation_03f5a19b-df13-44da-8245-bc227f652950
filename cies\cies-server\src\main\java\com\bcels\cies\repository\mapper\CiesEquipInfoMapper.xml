<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesEquipInfoMapper">

    <select id="findEquipInfoForPage" resultType="com.bcels.cies.response.CiesEquipInfoResponse">
        select equip.*,pro.pro_name,equip1.equip_name as parentEquipName
        from cies_equip_info equip
        left join cies_equip_info equip1 on equip.parent_equip_id = equip1.equip_id
        left join cies_project_info pro on pro.project_id=equip.project_id
        <where>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND pro.project_id = #{request.projectId}
            </if>
            <if test="request.equipName  != null and request.equipName  != ''">
                AND equip.equip_name LIKE CONCAT('%', #{request.equipName}, '%')
            </if>
            <if test="request.level  != null and request.level  != ''">
                AND equip.level = #{request.level}
            </if>
        and equip.dr = 0
        </where>
        order by pro.pro_name asc,equip.create_time desc,equip.equip_name,equip.level,equip.sort
    </select>
</mapper>
