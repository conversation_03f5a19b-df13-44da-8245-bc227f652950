package com.bcels.cies.request;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@Schema(description = "审核操作")
public class CiesAuditOperateRequest implements Serializable {

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "是否调整")
    private Integer isAdjusted;

    @Schema(description = "计划审核状态")
    private String auditStatus;

    @Schema(description = "计划下发状态")
    private String dispatchStatus;

    @Schema(description = "计划日期集合")
    private List<String> planDateList;

    @Schema(description = "储能计划下发审核主键")
    private String dispatchAuditId;

    @Schema(description = "审核主键集合(用于重新计算)")
    private List<String> dispatchAuditIds;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;

    @Schema(description = "审核时间")
    private LocalDateTime reviewTime;

    @Schema(description = "审核人")
    private String reviewBy;
}
