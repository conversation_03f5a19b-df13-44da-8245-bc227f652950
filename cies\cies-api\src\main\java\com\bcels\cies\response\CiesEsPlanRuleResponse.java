package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Schema(description = "项目下发规则信息")
public class CiesEsPlanRuleResponse implements Serializable {

    @Schema(description = "规则主键")
    private String ruleId;

    @Schema(description = "时间维度")
    private String timeDimension;

    @Schema(description = "指令下发类型")
    private String commandType;

    @Schema(description = "下发规则")
    private String dispatchRule;

    @Schema(description = "是否支持手动控制")
    private String manualControlEnabled;

    @Schema(description = "当日覆盖规则")
    private String dailyOverrideRule;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "并网点信息集合")
    List<CiesConnectionPointResponse> connectionPointEntityList;
}
