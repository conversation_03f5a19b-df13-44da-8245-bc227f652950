package com.zwy.common.utils;

import com.zwy.common.utils.exception.ParamException;

/**
 * <AUTHOR>
 * 2024/6/20 14:43
 */
public class PasswordCheckUtil {



    private static void checkAccountAndPassword(String account, String password) {
        if (account == null || account.length() < 6) {
            throw new ParamException("账号长度至少为6位");
        }

        if (password == null || password.length() < 8) {
            throw new ParamException("密码长度至少为8位");
        }

        if (!checkPasswordStrength(password)) {
        }
    }

    private static boolean checkPasswordStrength(String password) {
        if (password == null || password.length() < 8) {
            return false; // 密码太短
        }

        boolean hasUpper = false, hasLower = false, hasDigit = false, hasSpecialChar = false;

        for (char ch : password.toCharArray()) {
            if (Character.isUpperCase(ch)) {
                hasUpper = true;
            } else if (Character.isLowerCase(ch)) {
                hasLower = true;
            } else if (Character.isDigit(ch)) {
                hasDigit = true;
            } else if (!Character.isLetterOrDigit(ch)) { // 简单判断为特殊字符
                hasSpecialChar = true;
            }
            // 如果所有条件都满足，则提前结束循环
            if (hasUpper && hasLower && hasDigit && hasSpecialChar) {
                break;
            }
        }

        return hasUpper && hasLower && hasDigit && hasSpecialChar;
    }
}
