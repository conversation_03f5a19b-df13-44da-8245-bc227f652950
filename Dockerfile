# Copyright © 2024 富鸿资本（湖南）融资租赁有限公司 版权所有

# 使用基于 Alpine 的 OpenJDK 17 镜像
FROM eclipse-temurin:17-jre-alpine

# 定义时区参数
ENV TZ=Asia/Shanghai
# 安装时区数据包
RUN apk add tzdata
# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建工作目录
RUN mkdir -p /kangfu

# 设置工作目录
WORKDIR /kangfu

# 暴露端口
EXPOSE 21236

# 将打包好的 JAR 文件添加到工作目录
ADD cies/cies-server/target/cies-server.jar  /kangfu/

# 启动 Java 应用程序
CMD java $JAVA_OPT -Dio.netty.leakDetectionLevel=DISABLED  -Dloader.path=./BOOT-INF/lib  -Djava.security.egd=file:/dev/./urandom  -jar /kangfu/cies-server.jar