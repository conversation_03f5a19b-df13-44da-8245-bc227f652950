package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesDemandDistributeRecordEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.request.CiesDemandRequest;
import com.bcels.cies.response.CiesDemandResponse;
import com.zwy.common.utils.bean.PageResponse;

public interface ICiesDemandDistributeRecordService extends IService<CiesDemandDistributeRecordEntity> {


    CiesDemandResponse findLastDemand(String connPointId);

    PageResponse<CiesDemandResponse> findDemandRecordsForPage(CiesDemandRequest request);

}
