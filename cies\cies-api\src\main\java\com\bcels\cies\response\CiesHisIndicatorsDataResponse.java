package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "指标历史数据")
public class CiesHisIndicatorsDataResponse implements Serializable {

    @Schema(description = "指标数据")
    private BigDecimal indicatorValue;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "更新时间（字符串）")
    private String updateTimeStr;
}
