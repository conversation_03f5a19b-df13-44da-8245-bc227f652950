package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
@Getter
@Setter
@TableName("cies_es_plan_rule")
@ApiModel(value = "CiesEsPlanRuleEntity对象", description = "")
public class CiesEsPlanRuleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("规则主键")
    @TableId
    private String ruleId;

    @ApiModelProperty("时间维度")
    private String timeDimension;

    @ApiModelProperty("指令下发类型")
    private String commandType;

    @ApiModelProperty("下发规则")
    private String dispatchRule;

    @ApiModelProperty("是否支持手动控制")
    private String manualControlEnabled;

    @ApiModelProperty("当日覆盖规则")
    private String dailyOverrideRule;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
