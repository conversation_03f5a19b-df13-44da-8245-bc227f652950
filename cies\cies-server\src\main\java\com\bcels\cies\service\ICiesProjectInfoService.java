package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesProjectInfoEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.response.CiesProjectInfoResponse;
import com.bcels.cies.response.CiesStatisticsProjectResponse;
import com.zwy.common.utils.bean.PageResponse;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 项目信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface ICiesProjectInfoService extends IService<CiesProjectInfoEntity> {


    PageResponse<CiesProjectInfoResponse> findMarketProjectInfoForPage(CiesMarketConfigRequest request);

    List<CiesProjectInfoResponse> findProjectInfo(String projectId);

    CiesProjectInfoResponse findProjectInfoById(String projectId);

    PageResponse<CiesProjectInfoResponse> findProjectInfoForPage(CiesProjectInfoRequest request);

    PageResponse<CiesProjectInfoResponse> findMarKetForPage(CiesProjectInfoRequest request);

    void updateProjectInfo(CiesProjectInfoRequest request);

    CiesProjectInfoResponse findEnterpriseByProjectId(String projectId);

    List<CiesProjectInfoResponse> findProjectByEnterpriseId(CiesProjectInfoRequest request);

    void updateBaseProject(CiesProjectInfoEntity entity);


    CiesStatisticsProjectResponse statisticsProject();

    List<CiesProjectInfoResponse> findAllProject();

    Map<String, Object> getProvinceProject(CiesProjectInfoRequest request);

    CiesProjectInfoResponse getPowerCurve(CiesProjectInfoRequest request);

    List<CiesProjectInfoResponse> getSettlementStatement(CiesProjectInfoRequest request);

    List<CiesProjectInfoResponse> getProjectInfo();

    void verificationStatement(CiesProjectInfoRequest request);

}
