package com.bcels.cies.domain;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bcels.cies.client.CiesInternalInterfaceClient;
import com.bcels.cies.emuns.*;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.infrastructure.utils.ExcelExportUtil;
import com.bcels.cies.infrastructure.utils.TimeSlotConverter;
import com.bcels.cies.model.CiesEnergyPlanListExport;
import com.bcels.cies.model.TimeRange;
import com.bcels.cies.repository.entity.*;
import com.bcels.cies.request.*;
import com.bcels.cies.response.*;
import com.bcels.cies.service.*;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.IdGenUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import com.zwy.common.utils.exception.BusinessException;
import io.swagger.annotations.ApiModelProperty;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.bcels.cies.emuns.PeriodTypeEnum.*;

@Slf4j
@Service
public class CiesEnergyPlanDomainService {

    /**
     * 保存方式（编辑/保存）
     */
    private static final String SAVE_TYPE = "save";


    @Autowired
    private ICiesEssPlanAuditService iCiesEssPlanAuditService;

    @Autowired
    private ICiesEnergyStoragePlanService iCiesEnergyStoragePlanService;

    @Autowired
    private ICiesProjectInfoService iCiesProjectInfoService;

    @Autowired
    private CiesMarketDomainService ciesMarketDomainService;

    @Autowired
    private ICiesMarketElecPriService iCiesMarketElecPriService;

    @Autowired
    private ICiesMarketStageService iCiesMarketStageService;

    @Autowired
    private ICiesEsPlanRuleService iCiesEsPlanRuleService;

    @Autowired
    private ICiesConnectionPointService iCiesConnectionPointService;

    @Autowired
    private ICiesSpecialIndicatorsService iCiesSpecialIndicatorsService;

    @Autowired
    private ICiesControlHisRecordsService iCiesControlHisRecordsService;

    @Autowired
    private CiesInternalInterfaceClient client;

    /**
     * 储能计划列表查询
     *
     * @param request
     * @return
     */
    public PageResponse<CiesEnergyPlanListResponse> findEnergyPlanForPage(CiesEnergyPlanListRequest request) {
        PageResponse<CiesEnergyPlanListResponse> energyPlan = iCiesEssPlanAuditService.findEnergyPlan(request);
        List<CiesEnergyPlanListResponse> energyPlanPageData = energyPlan.getPageData();

        energyPlanPageData.forEach(item -> {
            request.setProjectId(item.getProjectId());
            request.setPlanDate(item.getPlanDate());
            List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
            if (!CollectionUtils.isEmpty(stationEnergyPlan)) {
                Map<String, String> stringListMap = mergePlans(stationEnergyPlan);
                item.setPlannedChargingTime(stringListMap.get(EnergyStateEnum.CHARGE.getCode()));
                item.setPlannedDischargingTime(stringListMap.get(EnergyStateEnum.DISCHARGE.getCode()));
                item.setUpdateBy(stationEnergyPlan.get(0).getCreateBy());
                item.setUpdateTime(stationEnergyPlan.get(0).getCreateTime());
            }
        });
        return new PageResponse<>(energyPlan.getTotal(), energyPlan.getPage(), energyPlan.getPageSize(), energyPlanPageData);
    }

    /**
     * 储能计划列表导出
     *
     * @param request
     * @return
     */
    public void  exportEnergyPlanList(CiesEnergyPlanListRequest request, HttpServletResponse response) throws IOException {
        PageResponse<CiesEnergyPlanListResponse> energyPlan = iCiesEssPlanAuditService.findEnergyPlan(request);
        List<CiesEnergyPlanListResponse> energyPlanPageData = energyPlan.getPageData();

        energyPlanPageData.forEach(item -> {
            request.setProjectId(item.getProjectId());
            request.setPlanDate(item.getPlanDate());
            List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
            Map<String, String> stringListMap = mergePlans(stationEnergyPlan);
            item.setPlannedChargingTime(stringListMap.get(EnergyStateEnum.CHARGE.getCode()));
            item.setPlannedDischargingTime(stringListMap.get(EnergyStateEnum.DISCHARGE.getCode()));
            item.setAuditStatus(AuditStatusEnum.getDescByCode(item.getAuditStatus()));
            item.setDispatchStatus(DispatchStatusEnum.getDescByCode(item.getDispatchStatus()));
        });

        List<CiesEnergyPlanListExport> ciesMarketListExports = BeanCopyUtil.copyListProperties(energyPlanPageData, CiesEnergyPlanListExport::new);

        String customFileName = "储能计划列表" + System.currentTimeMillis() + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        response.setHeader("Content-Disposition", "attachment; filename=" + customFileName);

        byte[] excelBytes = ExcelExportUtil.exportToExcel(ciesMarketListExports, "储能计划列表", CiesEnergyPlanListExport.class);
        response.getOutputStream().write(excelBytes);
    }

    /**
     * 储能计划初始化
     * @param request
     */
    public CiesEnergyPlanDetailResponse initEnergyPlan(CiesEnergyPlanListRequest request) {
        CiesEnergyPlanDetailResponse  response= new CiesEnergyPlanDetailResponse();
        // 获取项目信息和市场基础信息
        CiesProjectInfoResponse projectInfo = iCiesProjectInfoService.findProjectInfo(request.getProjectId()).get(0);
        response.setProject(projectInfo);

        // 获取市场信息列表
        // 获取计划第一天的所在月yyyy-MM
        String month = request.getPlanStartTime().substring(0, request.getPlanStartTime().lastIndexOf("/"));
        Map<PeriodTypeEnum, String> periodTypeMap = summaryPeriod(month, request.getProjectId());
        CiesMarketListResponse item = new CiesMarketListResponse();
        // 分组填充时段
        item.setPeakPeriod(periodTypeMap.get(PEAK));
        item.setOffPeakPeriod(periodTypeMap.get(OFF_PEAK));
        item.setHighPeriod(periodTypeMap.get(HIGH));
        item.setNormalPeriod(periodTypeMap.get(NORMAL));

        // 填充分时电价数据
        CiesMarketElecPriEntity elecPriInfo = iCiesMarketElecPriService.findElecPriInfo(month, request.getProjectId());
        if (!ObjectUtils.isEmpty(elecPriInfo)) {
            item.setPeakEnergyPrice(elecPriInfo.getPeakPeriodPrice());
            item.setOffPeakEnergyPrice(elecPriInfo.getValleyPeriodPrice());
            item.setHighEnergyPrice(elecPriInfo.getHighPeriodPrice());
            item.setNormalEnergyPrice(elecPriInfo.getFlatPeriodPrice());
        }
        response.setMarket(item);
        // 获取市场信息图形
        CiesMarketConfigRequest  configRequest=new  CiesMarketConfigRequest();
        configRequest.setMonth(month);
        configRequest.setProjectId(request.getProjectId());
        Map<Double, BigDecimal> map = ciesMarketDomainService.showGraphics(configRequest);
        response.setGraphics(map);

        // 充放电信息-全场站
        CiesEsPlanRuleResponse ciesEsPlanRuleResponse = iCiesEsPlanRuleService.findByProjectId(request.getProjectId());
        CiesFullStationResponse ciesAllStationResponse = BeanCopyUtil.copyProperties(ciesEsPlanRuleResponse, CiesFullStationResponse::new);
        response.setPlan(ciesAllStationResponse);
        return response;
    }

    /**
     * 储能计划查看
     * @param request
     * @return
     */
    public CiesEnergyPlanDetailResponse findEnergyPlanDetail(CiesEnergyPlanListRequest request) {
        CiesEnergyPlanDetailResponse  response= new CiesEnergyPlanDetailResponse();
        // 获取项目信息和市场基础信息
        CiesProjectInfoResponse projectInfo = iCiesProjectInfoService.findProjectInfo(request.getProjectId()).get(0);
        response.setProject(projectInfo);

        // 获取市场信息列表
        // 获取计划第一天的所在月yyyy/MM
        String month = request.getPlanDate().substring(0, request.getPlanDate().lastIndexOf("/"));
        Map<PeriodTypeEnum, String> periodTypeMap = summaryPeriod(month, request.getProjectId());
        CiesMarketListResponse item = new CiesMarketListResponse();
        // 分组填充时段
        item.setPeakPeriod(periodTypeMap.get(PEAK));
        item.setOffPeakPeriod(periodTypeMap.get(OFF_PEAK));
        item.setHighPeriod(periodTypeMap.get(HIGH));
        item.setNormalPeriod(periodTypeMap.get(NORMAL));

        // 填充分时电价数据
        CiesMarketElecPriEntity elecPriInfo = iCiesMarketElecPriService.findElecPriInfo(month, request.getProjectId());
        if (!ObjectUtils.isEmpty(elecPriInfo)) {
            item.setPeakEnergyPrice(elecPriInfo.getPeakPeriodPrice());
            item.setOffPeakEnergyPrice(elecPriInfo.getValleyPeriodPrice());
            item.setHighEnergyPrice(elecPriInfo.getHighPeriodPrice());
            item.setNormalEnergyPrice(elecPriInfo.getFlatPeriodPrice());
        }
        response.setMarket(item);
        // 获取市场信息图形
        CiesMarketConfigRequest  configRequest=new  CiesMarketConfigRequest();
        configRequest.setMonth(month);
        configRequest.setProjectId(request.getProjectId());
        Map<Double, BigDecimal> map = ciesMarketDomainService.showGraphics(configRequest);
        response.setGraphics(map);
        // 充放电信息-全场站规则
        CiesEsPlanRuleResponse ciesEsPlanRuleResponse = iCiesEsPlanRuleService.findByProjectId(request.getProjectId());
        CiesFullStationResponse ciesAllStationResponse = BeanCopyUtil.copyProperties(ciesEsPlanRuleResponse, CiesFullStationResponse::new);
         // 充放电信息-全场站计划
        List<CiesEnergyStoragePlanResponse> ciesEnergyStoragePlanResponses = mergePeriod(request);
        ciesAllStationResponse.setPlanResponseList(ciesEnergyStoragePlanResponses);
        response.setPlan(ciesAllStationResponse);

        CiesEnergyPlanAuditResponse planAuditInfo = iCiesEssPlanAuditService.findPlanAuditInfo(request.getProjectId(), request.getPlanDate());
        response.setViewInfo(planAuditInfo);
        return response;
    }

    /**
     * 全场站储能计划(保存/提交)
     *
     * @param request
     */
    @Transactional
    public void saveFullStationPlan(CiesStationPlanRequest request) {
        // 获取并网点信息
        List<String> list = new ArrayList<>();
        list.add(request.getProjectId());
        List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(list);
        if (CollectionUtils.isEmpty(connInfo)){
            throw new BusinessException("缺少并网点,无法保存/提交计划");
        }
        List<CiesEnergyPlanRequest> plan = request.getPlan();
        plan.forEach(obj  -> {
            if (EnergyStateEnum.STANDBY.getCode().equals(obj.getChargeDischargeType()))  {
                obj.setPlannedPower(BigDecimal.ZERO);
            }
        });
        // 根据充放电时间，取出待机状态时间点
        List<CiesEnergyPlanRequest> tempResult = fillTimeGaps(plan);
        List<String> dateRange = new ArrayList<>();
        // 如果是批量则取值日期时间段
        if (request.getPlanDate().contains("~")) {
            String[] split = request.getPlanDate().split("~");
            if (split[0].equals(split[1])) {
                dateRange.add(split[0]);
            } else {
                dateRange = getDateRange(split[0], split[1]);
            }
        } else {
            dateRange.add(request.getPlanDate());
        }

        List<CiesEnergyStoragePlanEntity> result = new ArrayList<>();
        List<CiesEnergyStoragePlanEntity> planEntityList = BeanCopyUtil.copyListProperties(tempResult, CiesEnergyStoragePlanEntity::new);

        dateRange.forEach(item -> {
            List<CiesEnergyStoragePlanEntity> tempPlan = BeanCopyUtil.copyListProperties(planEntityList, CiesEnergyStoragePlanEntity::new);
            tempPlan.forEach(plan1 -> {
                plan1.setPlanDate(item);
                plan1.setProjectId(request.getProjectId());
                plan1.setCreateTime(LocalDateTime.now());
                plan1.setCreateBy(CiesUserContext.getCurrentUser());
                plan1.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
            });
            result.addAll(tempPlan);
            CiesEnergyPlanListResponse auditInfo = iCiesEssPlanAuditService.findAuditByProject(request.getProjectId(), item);
            if (ObjectUtils.isEmpty(auditInfo)) {
                // 计划下发审核表初始化
                CiesEssPlanAuditEntity audit = new CiesEssPlanAuditEntity();
                audit.setPlanDate(item);
                audit.setProjectId(request.getProjectId());
                audit.setIsAdjusted(YesOrNo.NO.getCode());
                audit.setCreateTime(LocalDateTime.now());
                audit.setCreateBy(CiesUserContext.getCurrentUser());
                audit.setDispatchAuditId(IdGenUtil.genUniqueId());
                audit.setAuditStatus(AuditStatusEnum.PENDING_REVIEW.getCode());
                audit.setDispatchStatus(DispatchStatusEnum.DISTRIBUTED.getCode());
                audit.setDr(YesOrNo.NO.getCode());
                iCiesEssPlanAuditService.save(audit);
            } else {
                CiesAuditOperateRequest auditStatusRequest1 = new CiesAuditOperateRequest();
                auditStatusRequest1.setDispatchAuditId(auditInfo.getDispatchAuditId());
                auditStatusRequest1.setDr(YesOrNo.NO.getCode());
                auditStatusRequest1.setIsAdjusted(YesOrNo.NO.getCode());
                auditStatusRequest1.setAuditStatus(AuditStatusEnum.PENDING_REVIEW.getCode());
                auditStatusRequest1.setDispatchStatus(DispatchStatusEnum.DISTRIBUTED.getCode());
                iCiesEssPlanAuditService.updatePlanAuditStatus(auditStatusRequest1);
            }
        });
        CiesEnergyPlanListRequest requestStation = new CiesEnergyPlanListRequest();
        requestStation.setProjectId(request.getProjectId());
        requestStation.setPlanDateList(dateRange);
        // 如果存在 先删除
        iCiesEnergyStoragePlanService.batchDelete(requestStation);
        iCiesEnergyStoragePlanService.saveBatch(result);
        // 生成并网点计划
        generateConnPlan(result, dateRange, connInfo);
    }

    /**
     *全场站计划查询（重置）
     * @param request
     * @return
     */
    public CiesFullStationResponse findFullStationPlan(CiesQueryStationPlanRequest request){

        CiesEsPlanRuleResponse ciesEsPlanRuleResponse = iCiesEsPlanRuleService.findByProjectId(request.getProjectId());
        CiesFullStationResponse ciesAllStationResponse = BeanCopyUtil.copyProperties(ciesEsPlanRuleResponse, CiesFullStationResponse::new);

        CiesEnergyPlanListResponse auditByProject = iCiesEssPlanAuditService.findAuditByProject(request.getProjectId(), request.getPlanDate());

        CiesEnergyPlanListRequest planListRequest = BeanCopyUtil.copyProperties(request, CiesEnergyPlanListRequest::new);
        List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(planListRequest);
        if (CollectionUtils.isEmpty(stationEnergyPlan)){
            stationEnergyPlan.add(new CiesEnergyStoragePlanResponse());
        }
        ciesAllStationResponse.setPlanResponseList(stationEnergyPlan);
        ciesAllStationResponse.setIsAdjusted(auditByProject != null ? auditByProject.getIsAdjusted() : YesOrNo.NO.getCode());
        return ciesAllStationResponse;
    }

    /**
     * 并网点页面查询
     *
     * @param request
     */
    public CiesInitConnPlanResponse getConnPlan(CiesQueryStationPlanRequest request) {
        CiesInitConnPlanResponse result = new CiesInitConnPlanResponse();
        String connId= "";
        if (StringUtils.isNotEmpty(request.getProjectId())){
            CiesEsPlanRuleResponse ciesEsPlanRuleResponse = iCiesEsPlanRuleService.findByProjectId(request.getProjectId());
            result = BeanCopyUtil.copyProperties(ciesEsPlanRuleResponse, CiesInitConnPlanResponse::new);

            CiesEnergyPlanListResponse auditByProject = iCiesEssPlanAuditService.findAuditByProject(request.getProjectId(), request.getPlanDate());
            result.setIsAdjusted(auditByProject != null ? auditByProject.getIsAdjusted() : YesOrNo.NO.getCode());

            // 查询并网点
            List<String> list = new ArrayList<>();
            list.add(request.getProjectId());
            List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(list);
            if (CollectionUtils.isEmpty(connInfo)){
                return result;
            }
            connInfo.sort(Comparator.comparing(CiesConnectionPointResponse::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(
                    CiesConnectionPointResponse::getConnectionPointName,
                    Comparator.nullsLast(Comparator.naturalOrder())
            ));
            result.setConn(connInfo);
            connId = connInfo.get(0).getConnectionPointId();
        }

        // 查询第一个并网点对应计划
        CiesEnergyPlanListRequest planListRequest = BeanCopyUtil.copyProperties(request, CiesEnergyPlanListRequest::new);
        if (StringUtils.isNotEmpty(request.getConnectionPointId())){
            connId = request.getConnectionPointId();
        }
        planListRequest.setConnectionPointId(connId);
        planListRequest.setProjectId(null);
        List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(planListRequest);
        if (CollectionUtils.isEmpty(stationEnergyPlan)){
            stationEnergyPlan.add(new CiesEnergyStoragePlanResponse());
        }
        result.setConnPlan(stationEnergyPlan);
        return result;
    }

    /**
     * 并网点储能计划（编辑保存）
     * @param request
     */
    public void saveCollPointPlan(CiesConnPointPlanRequest request) {
        List<CiesEnergyPlanRequest> basePlan = request.getPlan();
        basePlan.forEach(obj  -> {
            obj.setConnectionPointId(request.getConnectionPointId());
            if (EnergyStateEnum.STANDBY.getCode().equals(obj.getChargeDischargeType()))  {
                obj.setPlannedPower(BigDecimal.ZERO);
            }
        });
        // 根据充放电时间，取出待机状态时间点
        List<CiesEnergyPlanRequest> tempResult = fillTimeGaps(basePlan);
        // 全场站计划
        List<CiesEnergyStoragePlanEntity> result = new ArrayList<>();
        // 当前并网点计划
        List<CiesEnergyStoragePlanEntity> connResult = new ArrayList<>();
        List<String> dateRange = new ArrayList<>();
        // 如果是批量则取值日期时间段
        if (request.getPlanDate().contains("~")) {
            String[] split = request.getPlanDate().split("~");
            if (split[0].equals(split[1])) {
                dateRange.add(split[0]);
            } else {
                dateRange = getDateRange(split[0], split[1]);
            }
        } else {
            dateRange.add(request.getPlanDate());
        }

        //保存当前并网点计划
        CiesEnergyPlanListRequest requestConn = new CiesEnergyPlanListRequest();
        requestConn.setConnectionPointId(request.getConnectionPointId());
        requestConn.setPlanDateList(dateRange);
        // 如果存在 先删除
        iCiesEnergyStoragePlanService.batchDelete(requestConn);
        // 保存当前并网点计划
        dateRange.forEach(item -> {
            List<CiesEnergyStoragePlanEntity> tempPlan = BeanCopyUtil.copyListProperties(tempResult, CiesEnergyStoragePlanEntity::new);
            tempPlan.forEach(plan1 -> {
                plan1.setPlanDate(item);
                plan1.setCreateTime(LocalDateTime.now());
                plan1.setCreateBy(CiesUserContext.getCurrentUser());
                plan1.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
            });
            connResult.addAll(tempPlan);

            CiesEnergyPlanListResponse auditInfo = iCiesEssPlanAuditService.findAuditByProject(request.getProjectId(), item);
            if (ObjectUtils.isEmpty(auditInfo)) {
                // 计划下发审核表初始化
                CiesEssPlanAuditEntity audit = new CiesEssPlanAuditEntity();
                audit.setPlanDate(item);
                audit.setProjectId(request.getProjectId());
                audit.setIsAdjusted(YesOrNo.YES.getCode());
                audit.setCreateTime(LocalDateTime.now());
                audit.setCreateBy(CiesUserContext.getCurrentUser());
                audit.setDispatchAuditId(IdGenUtil.genUniqueId());
                audit.setAuditStatus(AuditStatusEnum.PENDING_REVIEW.getCode());
                audit.setDispatchStatus(DispatchStatusEnum.DISTRIBUTED.getCode());
                audit.setDr(YesOrNo.NO.getCode());
                iCiesEssPlanAuditService.save(audit);
            } else {
                CiesAuditOperateRequest auditStatusRequest1 = new CiesAuditOperateRequest();
                auditStatusRequest1.setDispatchAuditId(auditInfo.getDispatchAuditId());
                auditStatusRequest1.setIsAdjusted(YesOrNo.YES.getCode());
                auditStatusRequest1.setDr(YesOrNo.NO.getCode());
                auditStatusRequest1.setAuditStatus(AuditStatusEnum.PENDING_REVIEW.getCode());
                auditStatusRequest1.setDispatchStatus(DispatchStatusEnum.DISTRIBUTED.getCode());
                iCiesEssPlanAuditService.updatePlanAuditStatus(auditStatusRequest1);
            }
        });
        iCiesEnergyStoragePlanService.saveBatch(connResult);

            // 保存全量并网点计划并同步到全场站计划
            CiesEnergyPlanListRequest listRequest = new CiesEnergyPlanListRequest();
            listRequest.setPlanDateList(dateRange);
            listRequest.setProjectId(request.getProjectId());
            List<CiesEnergyStoragePlanResponse> allConnPointPlan = iCiesEnergyStoragePlanService.findAllConnPointPlan(listRequest);
            if (CollectionUtils.isEmpty(allConnPointPlan)){
                allConnPointPlan = new ArrayList<>();
            }
            // 一分钟维度并网点计划
            List<CiesEnergyStoragePlanResponse> newPlanList = new ArrayList<>();

            // 拆分该并网点下所有计划为一分钟级别，便于对不同并网点时刻并集
            allConnPointPlan.forEach(plan -> {
                List<CiesEnergyStoragePlanResponse> temp = splitPlan(plan, 1);
                newPlanList.addAll(temp);
            });

            // 按照当前并网点计划遍历时间，时间相同的累加计划功率
            Map<String, Map<String, BigDecimal>> collect = newPlanList.stream()
                    .filter(Objects::nonNull) // 过滤掉 null 对象
                    .filter(record -> record.getPlanDate()  != null && record.getStartTime()  != null)
                    .collect(Collectors.groupingBy(
                            // 第一层分组：按日期
                            CiesEnergyStoragePlanResponse::getPlanDate,
                            Collectors.groupingBy(
                                    // 第二层分组：按startTime
                                    CiesEnergyStoragePlanResponse::getStartTime,
                                    // 聚合计算：根据充放电类型调整plannedPower值
                                    Collectors.mapping(
                                            record -> {
                                                BigDecimal power = record.getPlannedPower();
                                                switch (record.getChargeDischargeType()) {
                                                    case "DISCHARGE":
                                                        return power;
                                                    case "CHARGE":
                                                        return power.negate();
                                                    case "STANDBY":
                                                        return BigDecimal.ZERO;
                                                    default:
                                                        return power;
                                                }
                                            },
                                            // 累加处理
                                            Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                                    )
                            )
                    ));
            // 生成新的计划绑定到相同时间段和项目id的全场站计划中
            CiesEnergyPlanListRequest requestStation = new CiesEnergyPlanListRequest();
            requestStation.setProjectId(request.getProjectId());
            requestStation.setPlanDateList(dateRange);
            // 如果存在 先删除
            iCiesEnergyStoragePlanService.batchDelete(requestStation);

            // 遍历时间段
            dateRange.forEach(item -> {
                List<CiesEnergyStoragePlanEntity> tempResultPlan  = new ArrayList<>();
                Map<String, BigDecimal> plannedPowerMap = collect.get(item);
                // 初始化一个1440大小的集合用来填充全场站计划
                DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

                List<CiesEnergyStoragePlanEntity> tempPlan = IntStream.range(0,  1440)  // 0~1439 分钟
                        .mapToObj(minute -> {
                            CiesEnergyStoragePlanEntity plan = new CiesEnergyStoragePlanEntity();

                            // 计算开始时间（00:00 + minute）
                            LocalTime startTime = LocalTime.MIN.plusMinutes(minute);
                            // 计算结束时间（开始时间 +1 分钟）
                            LocalTime endTime = startTime.plusMinutes(1);

                            // 设置格式化后的时间字符串（如 "00:00", "00:01", ..., "23:59"）
                            plan.setStartTime(startTime.format(timeFormatter));
                            plan.setEndTime(endTime.format(timeFormatter));

                            return plan;
                        })
                        .toList();
                tempPlan.forEach(plan -> {
                    BigDecimal planPower = plannedPowerMap.get(plan.getStartTime());
                    plan.setPlanDate(item);
                    plan.setConnectionPointId(null);
                    plan.setProjectId(request.getProjectId());
                    plan.setCreateTime(LocalDateTime.now());
                    plan.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
                    plan.setPlannedPower(planPower.abs());
                    if (planPower.compareTo(BigDecimal.ZERO)  > 0) {
                        // 正数 - 放电
                        plan.setChargeDischargeType("DISCHARGE");
                    } else if (planPower.compareTo(BigDecimal.ZERO)  < 0) {
                        // 负数 - 充电
                        plan.setChargeDischargeType("CHARGE");
                    } else {
                        // 零 - 待机
                        plan.setChargeDischargeType("STANDBY");
                    }
                    plan.setCreateBy(CiesUserContext.getCurrentUser());
                });
                tempResultPlan.addAll(tempPlan);
                tempResultPlan.sort(Comparator.comparing(CiesEnergyStoragePlanEntity::getStartTime));
                // 合并一分钟全场站计划
                 result.addAll(mergeStationPlan(tempResultPlan));
            });
            // 合并一分钟维度生成新的全场站计划
            iCiesEnergyStoragePlanService.saveBatch(result);
    }

    /**
     * 审核储能计划
     *
     * @param request
     */
    @Transactional
    public void reviewPlan(CiesAuditOperateRequest request) {
        CiesEssPlanAuditEntity entity = iCiesEssPlanAuditService.getById(request.getDispatchAuditId());
        // 如果审核为“已确认” 且计划日期为当天，则立即下发
        request.setDr(YesOrNo.NO.getCode());
        request.setReviewBy(CiesUserContext.getCurrentUser());
        request.setReviewTime(LocalDateTime.now());
        iCiesEssPlanAuditService.updatePlanAuditStatus(request);
        String today = LocalDate.now().toString().replace("-","/");
        if (today.equals(entity.getPlanDate()) && AuditStatusEnum.CONFIRMED.getCode().equals(request.getAuditStatus())) {
            distributePlan(entity.getProjectId(),today);
        }
    }

    /**
     * 储能计划利润数据-重新计算
     */
    public void batchRecalculatePlan(CiesAuditOperateRequest request) {
        List<String> list = request.getDispatchAuditIds();
        list.forEach(item ->
        {
            CiesEssPlanAuditEntity entity = iCiesEssPlanAuditService.getById(item);
            recalculatePlan(item, entity.getProjectId(), entity.getPlanDate());
        });
    }

    /**
     * 生成并网点计划
     *
     * @param stationPlans
     */
    private void generateConnPlan(List<CiesEnergyStoragePlanEntity> stationPlans,List<String> dateRange
    ,List<CiesConnectionPointResponse> connInfo) {
        List<CiesEnergyStoragePlanEntity> result = new ArrayList<>();
        connInfo.sort(Comparator.comparing(CiesConnectionPointResponse::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));
        // 父节点存在储能计划
        for (CiesConnectionPointResponse conn : connInfo) {
            CiesEnergyPlanListRequest requestConn = new CiesEnergyPlanListRequest();
            requestConn.setConnectionPointId(conn.getConnectionPointId());
            requestConn.setPlanDateList(dateRange);

            // 如果存在 先删除
            iCiesEnergyStoragePlanService.batchDelete(requestConn);
            List<CiesEnergyStoragePlanEntity> tempPlan = stationPlans.stream()
                    .map(plan -> {
                        CiesEnergyStoragePlanEntity newPlan = BeanCopyUtil.copyProperties(plan, CiesEnergyStoragePlanEntity::new);
                        newPlan.setPlannedPower(plan.getPlannedPower()
                                .multiply(conn.getCapacityRatio())
                                .setScale(2, RoundingMode.HALF_UP));
                        newPlan.setConnectionPointId(conn.getConnectionPointId());
                        newPlan.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
                        newPlan.setCreateTime(LocalDateTime.now());
                        newPlan.setProjectId(null);
                        return newPlan;
                    })
                    .collect(Collectors.toList());
            result.addAll(tempPlan);
        }
        iCiesEnergyStoragePlanService.saveBatch(result);
    }
    
    /**
     * 合并充放电计划
     *
     * @param plans
     * @return
     */
    private Map<String, String> mergePlans(List<CiesEnergyStoragePlanResponse> plans) {
        return plans.stream()
                .collect(Collectors.groupingBy(
                        CiesEnergyStoragePlanResponse::getChargeDischargeType,
                        LinkedHashMap::new,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    // 按开始时间排序
                                    list.sort(Comparator.comparing(CiesEnergyStoragePlanResponse::getStartTime));

                                    List<String> merged = new ArrayList<>();
                                    if (list.isEmpty()) return ""; // 空列表返回空字符串

                                    // 初始化第一个区间
                                    String currentStart = list.get(0).getStartTime();
                                    String currentEnd = list.get(0).getEndTime();

                                    for (int i = 1; i < list.size(); i++) {
                                        String nextStart = list.get(i).getStartTime();
                                        String nextEnd = list.get(i).getEndTime();

                                        if (currentEnd.equals(nextStart)) {
                                            // 相邻则合并
                                            currentEnd = nextEnd;
                                        } else {
                                            // 不相邻则保存当前区间
                                            merged.add(currentStart + "-" + currentEnd);
                                            currentStart = nextStart;
                                            currentEnd = nextEnd;
                                        }
                                    }
                                    // 添加最后一个区间
                                    merged.add(currentStart + "-" + currentEnd);

                                    // 将列表用逗号连接成字符串
                                    return String.join(",  ", merged);
                                }
                        )
                ));
    }


    /**
     * 获取并合并时段
     *
     * @param month
     * @return
     */
    private Map<PeriodTypeEnum, String> summaryPeriod(String month, String projectId) {
        List<CiesMarketStageEntity> marketStageList = iCiesMarketStageService.findMarketStageList(month, projectId);
        if (marketStageList == null || marketStageList.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, String> stringMap = mapPeriodFields(marketStageList.get(0));

        // 按照阶段分组，并对时间段汇总成集合
        Map<PeriodTypeEnum, List<TimeRange>> grouped = stringMap.entrySet().stream()
                .filter(e -> e.getValue() != null)
                .collect(Collectors.groupingBy(
                        e -> fromCode(e.getValue()),
                        Collectors.mapping(
                                e -> parseTimeRange(e.getKey()),
                                Collectors.toList()
                        )
                ));
        // 合并连续时段
        Map<PeriodTypeEnum, String> result = new EnumMap<>(PeriodTypeEnum.class);
        grouped.forEach((type, ranges) -> {
            ranges.sort(Comparator.comparing(TimeRange::getStart));
            result.put(type, mergeContinuousRanges(ranges));
        });
        return result;
    }


    /**
     * 解析字符串为时间范围（如"0时-0.5时" → 00:00-00:30）
     *
     * @param periodStr
     * @return
     */
    private TimeRange parseTimeRange(String periodStr) {
        String[] parts = periodStr.split("-");
        return new TimeRange(
                parseHour(parts[0]),
                parseHour(parts[1])
        );
    }

    /**
     * 合并连续时间段
     *
     * @param ranges
     * @return
     */
    private String mergeContinuousRanges(List<TimeRange> ranges) {
        if (ranges.isEmpty()) return "";

        List<String> merged = new ArrayList<>();
        TimeRange current = ranges.get(0);

        for (int i = 1; i < ranges.size(); i++) {
            if (ranges.get(i).getStart().equals(current.getEnd())) {
                current = new TimeRange(current.getStart(), ranges.get(i).getEnd());
            } else {
                merged.add(current.toString());
                current = ranges.get(i);
            }
        }
        merged.add(current.toString());

        return String.join(",", merged);
    }


    /**
     * 解析时间字符串（支持"0.5时"/"1时"等格式）
     *
     * @param hourStr
     * @return
     */
    private LocalTime parseHour(String hourStr) {
        if (!"24时".equals(hourStr)){
            double hours = Double.parseDouble(hourStr.replace("时", ""));
            int totalMinutes = (int) (hours * 60);
            return LocalTime.of(totalMinutes / 60, totalMinutes % 60);
        }else{
            return LocalTime.of(23,59);
        }
    }
    /**
     * 获取时段对应阶段映射
     *
     * @param entity
     * @return
     */
    private Map<String, String> mapPeriodFields(CiesMarketStageEntity entity) {
        return Stream.of(entity.getClass().getDeclaredFields())
                .filter(field -> field.getName().startsWith("period"))  // 筛选period开头的字段
                .collect(LinkedHashMap::new, (map, field) -> {
                    try {
                        field.setAccessible(true);
                        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                        if (annotation != null) {
                            String key = annotation.value();  // 注解文本作为Key
                            String value = (String) field.get(entity);  // 字段值作为Value
                            map.put(key, value);
                        }
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException("Failed to access field: " + field.getName(), e);
                    }
                }, LinkedHashMap::putAll);
    }

    /**
     * 填充默认时段（待机）
     * @param originalList
     * @return
     */
    private List<CiesEnergyPlanRequest> fillTimeGaps(List<CiesEnergyPlanRequest> originalList) {
        List<CiesEnergyPlanRequest> result = new ArrayList<>();

        // 按开始时间排序
        originalList.sort(Comparator.comparing(CiesEnergyPlanRequest::getStartTime));

        //  处理第一个时间段之前的间隙（00:00 - 第一条记录的startTime）
        if (!originalList.isEmpty())  {
            String firstStart = originalList.get(0).getStartTime();
            if (!"00:00".equals(firstStart)) {
                result.add(createStandbyRecord("00:00",  firstStart, originalList.get(0)));
            }
        }

        // 处理记录之间的间隙
        for (int i = 0; i < originalList.size()  - 1; i++) {
            CiesEnergyPlanRequest current = originalList.get(i);
            CiesEnergyPlanRequest next = originalList.get(i  + 1);
            result.add(current);

            if (!current.getEndTime().equals(next.getStartTime()))  {
                result.add(createStandbyRecord(current.getEndTime(),  next.getStartTime(),  current));
            }
        }

        // 处理最后一条记录
        if (!originalList.isEmpty())  {
            CiesEnergyPlanRequest last = originalList.get(originalList.size()  - 1);
            result.add(last);


            if (!"00:00".equals(last.getEndTime()))  {
                result.add(createStandbyRecord(last.getEndTime(),  "00:00", last));
            }
        }

        // 特殊处理：当原始列表为空时
        if (originalList.isEmpty())  {
            CiesEnergyPlanRequest standby = new CiesEnergyPlanRequest();
            standby.setStartTime("00:00");
            standby.setEndTime("00:00");
            standby.setChargeDischargeType(EnergyStateEnum.STANDBY.getCode());
            standby.setPlannedPower(BigDecimal.ZERO);
            result.add(standby);
        }
        return result;
    }

    private CiesEnergyPlanRequest createStandbyRecord(String start, String end, CiesEnergyPlanRequest template) {
        CiesEnergyPlanRequest record = new CiesEnergyPlanRequest();
        record.setPlanDate(template.getPlanDate());
        record.setStartTime(start);
        record.setEndTime(end);
        record.setChargeDischargeType(EnergyStateEnum.STANDBY.getCode());
        record.setPlannedPower(BigDecimal.ZERO);
        record.setProjectId(template.getProjectId());
        record.setConnectionPointId(template.getConnectionPointId());
        return record;
    }

    /**
     * 获取时间段内的日期
     * @param startDate
     * @param endDate
     * @return
     */
    private static List<String> getDateRange(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

        LocalDate start = LocalDate.parse(startDate.replace("/","-"));
        LocalDate end = LocalDate.parse(endDate.replace("/","-"));

        while (!start.isAfter(end))  {
            dateList.add(start.format(formatter));
            start = start.plusDays(1);
        }
        return dateList;
    }

    /**
     * 根据时间间隔拆分能源计划
     * @param originalPlan 原始计划对象
     * @param intervalMinutes 时间间隔（1或15分钟）
     * @return 拆分后的计划列表
     */
    public  List<CiesEnergyStoragePlanResponse> splitPlan(CiesEnergyStoragePlanResponse originalPlan, int intervalMinutes) {

        List<CiesEnergyStoragePlanResponse> result = new ArrayList<>();
        LocalTime start = LocalTime.parse(originalPlan.getStartTime());
        LocalTime end = LocalTime.parse(originalPlan.getEndTime());

        // 一分钟时间相同不拆分
        if (intervalMinutes == 1 && start.equals(end)) {
             result.add(originalPlan);
             return result;
        }
            return splitByMinute(originalPlan, start, end);
    }

    /**
     * 一分钟拆分
     * @param original
     * @param start
     * @param end
     * @return
     */
    private static List<CiesEnergyStoragePlanResponse> splitByMinute(CiesEnergyStoragePlanResponse original, LocalTime start, LocalTime end) {
        List<CiesEnergyStoragePlanResponse> plans = new ArrayList<>();
        LocalTime current = start;
        // 跨天标识
        boolean flag  = false;
        if ("00:00".equals(end.toString())){
            end = LocalTime.of(23,59);
            flag = true;
        }
        while (current.isBefore(end))  {
            CiesEnergyStoragePlanResponse plan = BeanCopyUtil.copyProperties(original, CiesEnergyStoragePlanResponse::new);
            plan.setStartTime(current.toString());
            plan.setEndTime(current.plusMinutes(1).toString());
            plans.add(plan);
            current = current.plusMinutes(1);
        }
        // 24点数据特殊处理
        if (flag){
            CiesEnergyStoragePlanResponse plan = BeanCopyUtil.copyProperties(original, CiesEnergyStoragePlanResponse::new);
            plan.setStartTime(current.toString());
            plan.setEndTime("00:00");
            plans.add(plan);
        }
        return plans;
    }

    /**
     * 合并相同充放电类型时间段
     * @param request
     * @return
     */
    private List<CiesEnergyStoragePlanResponse> mergePeriod(CiesEnergyPlanListRequest request) {
        List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
        if (CollectionUtils.isEmpty(stationEnergyPlan)){
            return Collections.emptyList();
        }
        stationEnergyPlan.sort(Comparator.comparing(obj ->
                LocalTime.parse(obj.getStartTime())
        ));
        List<CiesEnergyStoragePlanResponse> result = new ArrayList<>();
        CiesEnergyStoragePlanResponse current = BeanCopyUtil.copyProperties(stationEnergyPlan.get(0),CiesEnergyStoragePlanResponse::new);
        result.add(current);

        for (int i = 1; i < stationEnergyPlan.size();  i++) {
            CiesEnergyStoragePlanResponse next = stationEnergyPlan.get(i);

            if (current.getChargeDischargeType().equals(next.getChargeDischargeType())
                    && current.getEndTime().equals(next.getStartTime())) {
                // 动态合并
                current.setEndTime(next.getEndTime());
                current.setPlannedPower(current.getPlannedPower()
                        .add(next.getPlannedPower())
                        .setScale(2, RoundingMode.HALF_UP));
            } else {
                // 不可合并
                current = BeanCopyUtil.copyProperties(next,CiesEnergyStoragePlanResponse::new);
                result.add(current);
            }
        }
        return result;
    }

    /**
     * 获取15分钟时段电价
     * @param projectId
     * @return
     */
    private Map<String, BigDecimal> getPeriodPrice(String projectId, String planDate) {
        LocalDate localDate= LocalDate.parse(planDate.replace("/","-"));
        String yearMonth = YearMonth.from(localDate).toString().replace("-","/");
        // 获取电价
        CiesMarketElecPriEntity elecPriInfo = iCiesMarketElecPriService.findElecPriInfo(yearMonth, projectId);
        // 获取时段
        List<CiesMarketStageEntity> marketStageList = iCiesMarketStageService.findMarketStageList(yearMonth, projectId);

        if (ObjectUtils.isEmpty(elecPriInfo)) {
            log.error("当前项目：{}，在日期为：{}未配置电价，无法计算", projectId, yearMonth);
            throw new BusinessException("当前项目:" + projectId + "在日期为" + yearMonth + "未配置电价，无法重新计算");
        }
        if (CollectionUtils.isEmpty(marketStageList)) {
            log.error("当前项目：{}，在日期为：{}未配置时段，无法计算", projectId, yearMonth);
            throw new BusinessException("当前项目:" + projectId + "在日期为" + yearMonth + "未配置时段，无法重新计算");
        }
        Map<String, String> stringBigDecimalMap = mapPeriodFields(marketStageList.get(0));

        LinkedHashMap<Double, BigDecimal> result = new LinkedHashMap<>();
        stringBigDecimalMap.forEach((k, v) ->
                result.put(extractNumericValue(k), getPriceByPeriodType(elecPriInfo, v))
        );
        Map<String, BigDecimal> resultMap = new LinkedHashMap<>();

        LocalDateTime localDateTime = localDate.atTime(0,0,0);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 半小时拆分为15分钟的电价
        for (Map.Entry<Double, BigDecimal> entry : result.entrySet()) {
            // 第一个15分钟（00:00-00:15）记录在00:00上面
            resultMap.put(formatter.format(localDateTime), entry.getValue());
            // 第二个15分钟（00:15-00:30） 记录在00:15上面
            localDateTime = localDateTime.plusMinutes(15);
            resultMap.put(formatter.format(localDateTime), entry.getValue());
            localDateTime = localDateTime.plusMinutes(15);
        }
        return resultMap;
    }

    /**
     * 从periodType中提取数值（如"0时-0.5时" → 0）
     */
    private static double extractNumericValue(String periodType) {
        // 截取第一个时之前的数字
        String substring = periodType.substring(0, periodType.indexOf("时"));
        return Double.parseDouble(substring);  // 取第一部分"0.5"
    }

    /**
     * 根据时段类型获取对应电价
     *
     * @param elecPriInfo   电价实体对象
     * @param periodTypeStr 时段类型字符串（如"高峰"）
     * @return 对应时段电价
     * @throws IllegalArgumentException 当输入无效或字段不存在时抛出
     */
    private BigDecimal getPriceByPeriodType(CiesMarketElecPriEntity elecPriInfo, String periodTypeStr) {
        PeriodTypeEnum periodTypeEnum = PeriodTypeEnum.fromCode(periodTypeStr);
        if (periodTypeEnum == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal price = switch (periodTypeEnum) {
            case PEAK -> elecPriInfo.getPeakPeriodPrice();
            case HIGH -> elecPriInfo.getHighPeriodPrice();
            case NORMAL -> elecPriInfo.getFlatPeriodPrice();
            case OFF_PEAK -> elecPriInfo.getValleyPeriodPrice();
            case DEEP_VALLEY -> elecPriInfo.getDeepValleyPeriodPrice();
        };
        return price;
    }

    public void recalculatePlan(String dispatchAuditId, String projectId, String planDate) {
        // 初始化当前时间
        LocalDateTime startTime;
        LocalDateTime endTime;
        CiesSpecialIndicatorsUpdateRequest request = new CiesSpecialIndicatorsUpdateRequest();
        // 获取日充电量业务关联
        request.setIndicatorName("日充电量");
        request.setProjectId(projectId);
        CiesSpecialIndicatorsResponse specialIndicator = iCiesSpecialIndicatorsService.findSpecialIndicator(request);
        // 获取日放电量业务关联
        request.setIndicatorName("日放电量");
        CiesSpecialIndicatorsResponse specialIndicator1 = iCiesSpecialIndicatorsService.findSpecialIndicator(request);
        // 初始化指标数据
        BigDecimal dailyCharge = BigDecimal.ZERO;
        BigDecimal dailyDischarge = BigDecimal.ZERO;
        BigDecimal dailyChargeCost = BigDecimal.ZERO;
        BigDecimal dailyDischargeIncome = BigDecimal.ZERO;

        // 如果是历史日期，日充电量，日放电量从数据中台查询获取
        LocalDate localDate = LocalDate.parse(planDate.replace("/","-"));
        startTime = localDate.atTime(0, 0, 0);
        endTime  = localDate.atTime(23, 59, 59);
        // 获取数据中台时间段内数据
        if (LocalDate.now().toString().equals(planDate.replace("/","-"))) {
            // 如果是当前日期，日充电量，日放电量直接获取业务指标的数据
            // 获取本日充电电量
            if (specialIndicator != null) {
                dailyCharge = specialIndicator.getCurrentData();
            } else {
                log.error("当前项目：{}未配置日充电量业务指标", projectId);
                throw new BusinessException("当前项目:" + projectId + "未配置日充电量业务指标");
            }
            // 获取本日放电电量
            if (specialIndicator1 != null) {
                dailyDischarge = specialIndicator1.getCurrentData();
            } else {
                log.error("当前项目：{}未配置日放电量业务指标", projectId);
                throw new BusinessException("当前项目:" + projectId + "未配置日放电量业务指标");
            }
            endTime = LocalDateTime.now();
        } else {
            Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap = batchQueryIndicatorValue(specialIndicator, startTime, endTime);
            if (!MapUtils.isEmpty(stringListMap)) {
                dailyCharge = stringListMap.values()
                        .stream()
                        .filter(list -> !list.isEmpty())
                        .map(list -> list.get(list.size() - 1))
                        .toList().get(0).getIndicatorValue();
            }

            Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap1 = batchQueryIndicatorValue(specialIndicator1, startTime, endTime);
            if (!MapUtils.isEmpty(stringListMap1)) {
                dailyDischarge = stringListMap1.values()
                        .stream()
                        .filter(list -> !list.isEmpty())
                        .map(list -> list.get(list.size() - 1))
                        .toList().get(0).getIndicatorValue();
            }
        }
        // 获取对应时间的分时阶段价格
        Map<String, BigDecimal> periodPrice = getPeriodPrice(projectId, planDate);

        // 本日充电成本
        request.setIndicatorName("15分钟充电量");
        CiesSpecialIndicatorsResponse specialIndicator2 = iCiesSpecialIndicatorsService.findSpecialIndicator(request);
        if (!ObjectUtils.isEmpty(specialIndicator2) && StringUtils.isNotEmpty(specialIndicator2.getRelateIndicatorId())) {
            Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap2 = batchQueryIndicatorValue(specialIndicator2, startTime, endTime);
            if (!MapUtils.isEmpty(stringListMap2)) {
                List<CiesHisIndicatorsDataResponse> hisResponse = stringListMap2.get(specialIndicator2.getRelateIndicatorId());
                dailyChargeCost = calDailyData(hisResponse, periodPrice);
            }
        } else {
            log.error("当前项目：{}未配置15分钟充电量业务指标", projectId);
            throw new BusinessException("当前项目:" + projectId + "未配置15分钟充电量业务指标");
        }

        // 本日放电收入
        request.setIndicatorName("15分钟放电量");
        CiesSpecialIndicatorsResponse specialIndicator3 = iCiesSpecialIndicatorsService.findSpecialIndicator(request);
        if (!ObjectUtils.isEmpty(specialIndicator3) && StringUtils.isNotEmpty(specialIndicator3.getRelateIndicatorId())) {
            Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap3 = batchQueryIndicatorValue(specialIndicator3, startTime, endTime);
            if (!MapUtils.isEmpty(stringListMap3)) {
                List<CiesHisIndicatorsDataResponse> hisResponse1 = stringListMap3.get(specialIndicator3.getRelateIndicatorId());
                dailyDischargeIncome = calDailyData(hisResponse1, periodPrice);
            }
        } else {
            log.error("当前项目：{}未配置15分钟放电量业务指标", projectId);
            throw new BusinessException("当前项目:" + projectId + "未配置15分钟放电量业务指标");
        }

        // 本日充放电利润
        BigDecimal dailyProfit = dailyDischargeIncome.subtract(dailyChargeCost);
        UpdateWrapper<CiesEssPlanAuditEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper
                .set("daily_charge_energy", dailyCharge)
                .set("daily_discharge_energy", dailyDischarge)
                .set("daily_charge_cost", dailyChargeCost)
                .set("daily_discharge_income",dailyDischargeIncome)
                .set("daily_profit",dailyProfit)
                .set("update_time", LocalDateTime.now())
                .set("update_by", CiesUserContext.getCurrentUser())
                .eq(StringUtils.isNotBlank(dispatchAuditId),"dispatch_audit_id",dispatchAuditId);

        iCiesEssPlanAuditService.update(null, updateWrapper);
    }

    /**
     *全场站计划查询（重置）
     * @param list
     * @return
     */
    public List<CiesEnergyStoragePlanEntity> mergeStationPlan(List<CiesEnergyStoragePlanEntity> list){
        List<CiesEnergyStoragePlanEntity> result = new ArrayList<>();
        // 合并计划
        list.sort(Comparator.comparing(obj ->
                LocalTime.parse(obj.getStartTime())
        ));

        CiesEnergyStoragePlanEntity current = list.get(0);
        result.add(current);

        for (int i = 1; i < list.size();  i++) {
            CiesEnergyStoragePlanEntity next = list.get(i);

            // 如果充放电类型、计划功率、开始时间等于下一结束时间则合并
            if (current.getChargeDischargeType().equals(next.getChargeDischargeType())
                    && current.getEndTime().equals(next.getStartTime()) && (current.getPlannedPower().compareTo(next.getPlannedPower())  == 0)) {
                // 动态合并
                current.setEndTime(next.getEndTime());
                current.setPlannedPower(current.getPlannedPower());
            } else {
                // 不可合并
                current = BeanCopyUtil.copyProperties(next,CiesEnergyStoragePlanEntity::new);
                result.add(current);
            }
        }
        return result;
    }

    public void distributePlan(String projectId,String today) {
        try {
            // 获取审核状态为“已确认”并网点计划
            List<String> projectIdList = new ArrayList<>();
            projectIdList.add(projectId);

            // 获取项目对应并网点集合
            List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(projectIdList);
            // 获取并网点和通道id的映射关系
            Map<String, String> connectionPointToChannelMap = new HashMap<>();
            // 遍历集合
            for (CiesConnectionPointResponse item : connInfo) {
                String connectionPointId = item.getConnectionPointId();
                String channelId = item.getChannelId();
                connectionPointToChannelMap.put(connectionPointId, channelId);
            }
            connInfo.sort(Comparator.comparing(CiesConnectionPointResponse::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));
            Map<String, List<String>> connMap = connInfo.stream().collect(Collectors.groupingBy(
                    CiesConnectionPointResponse::getProjectId,
                    Collectors.mapping(
                            CiesConnectionPointResponse::getConnectionPointId,
                            Collectors.toList()
                    )
            ));

            // 获取项目-并网点信息集合
            Map<String, List<CiesConnectionPointResponse>> connListInfo = connInfo.stream().collect(Collectors.groupingBy(
                    CiesConnectionPointResponse::getProjectId
            ));

            // 获取已审核通过的项目以及计划
            List<CiesEnergyStoragePlanResponse> connPlanList = iCiesEnergyStoragePlanService.findConfirmedPlan(projectIdList, today);
            Map<String, List<CiesEnergyStoragePlanResponse>> planMap = connPlanList.stream().collect(Collectors.groupingBy(
                    CiesEnergyStoragePlanResponse::getConnectionPointId
            ));

            // 项目-并网点-计划
            Map<String, Map<String, List<CiesEnergyStoragePlanResponse>>> planListMap = mergeMaps(connMap, planMap);

            // 根据项目id查询对应的下发规则
            CiesEsPlanRuleResponse rule = iCiesEsPlanRuleService.findByProjectId(projectId);
            if (ObjectUtils.isEmpty(rule)) {
                return;
            }
            CiesEnergyPlanListResponse auditInfo = iCiesEssPlanAuditService.findAuditByProject(projectId, today);

            // 如果是闭环需要下发
            if (CommandDispatchEnum.CLOSED_LOOP.getCode().equals(rule.getCommandType())) {
                // 最终下发计划
                JSONObject finalPlan = new JSONObject();
                // 并网点计划
                JSONArray jsonObject1 = new JSONArray();
                // 按照不同时间维度对计划进行组装
                Map<String, List<CiesEnergyStoragePlanResponse>> connPlan = planListMap.get(projectId);
                connPlan.forEach((connPointId, plan) -> {
                    List<CiesEnergyStoragePlanResponse> resultPlan;
                    // 按照不同时间维度对计划进行组装
                    resultPlan = spiltPlan(plan, rule);
                    // 根据下发规则替换实际下发计划的计划功率值
                    coverPowerByRule(resultPlan, connPointId, rule);
                    JSONObject jsonObject = assemblyIssuePlan(resultPlan, connPointId, connectionPointToChannelMap, rule);
                    jsonObject1.add(jsonObject);
                });
                finalPlan.put("plan_lst", jsonObject1);
                finalPlan.put("projectId", projectId);
                // 下发到数据中台
                log.info("项目：{}下发计划数据：{}", projectId, finalPlan.toJSONString());
                client.distributePlan(finalPlan);
            }
            // 保存下发记录
            String hisRecordId = IdGenUtil.genUniqueId();
            CiesControlHisRecordsEntity entity = BeanCopyUtil.copyProperties(rule, CiesControlHisRecordsEntity::new);
            entity.setHistoryRecordId(hisRecordId);
            entity.setControlMode("自动模式");
            entity.setIsAdjusted(auditInfo.getIsAdjusted());
            entity.setCreateBy(CiesUserContext.getCurrentUser());
            entity.setCreateTime(LocalDateTime.now());
            iCiesControlHisRecordsService.save(entity);
            // 保存历史并网点信息
            List<CiesConnectionPointResponse> ciesConnectionPointResponses = connListInfo.get(projectId);
            List<CiesConnectionPointEntity> ciesConnectionPointEntities = BeanCopyUtil.copyListProperties(ciesConnectionPointResponses, CiesConnectionPointEntity::new);
            ciesConnectionPointEntities.forEach(conn -> {
                String connPointId = IdGenUtil.genUniqueId();
                // 保存历史并网点计划
                List<CiesEnergyStoragePlanResponse> ciesEnergyStoragePlan = planMap.get(conn.getConnectionPointId());
                List<CiesEnergyStoragePlanEntity> connPlan = BeanCopyUtil.copyListProperties(ciesEnergyStoragePlan, CiesEnergyStoragePlanEntity::new);
                connPlan.forEach(plan -> {
                    plan.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
                    plan.setConnectionPointId(connPointId);
                    plan.setCreateBy(CiesUserContext.getCurrentUser());
                    plan.setCreateTime(LocalDateTime.now());
                });
                iCiesEnergyStoragePlanService.saveBatch(connPlan);
                conn.setConnectionPointId(connPointId);
                conn.setHistoryRecordId(hisRecordId);
                conn.setCreateBy(CiesUserContext.getCurrentUser());
                conn.setCreateTime(LocalDateTime.now());

            });
            iCiesConnectionPointService.saveBatch(ciesConnectionPointEntities);
            // 保存历史全场站计划
            CiesEnergyPlanListRequest request = new CiesEnergyPlanListRequest();
            request.setProjectId(projectId);
            request.setPlanDate(today);
            List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
            List<CiesEnergyStoragePlanEntity> ciesEnergyStoragePlanEntities = BeanCopyUtil.copyListProperties(stationEnergyPlan, CiesEnergyStoragePlanEntity::new);
            ciesEnergyStoragePlanEntities.forEach(plan -> {
                plan.setEnergyStoragePlanId(IdGenUtil.genUniqueId());
                plan.setHistoryRecordId(hisRecordId);
                plan.setCreateBy(CiesUserContext.getCurrentUser());
                plan.setCreateTime(LocalDateTime.now());
                plan.setProjectId(null);
            });
            iCiesEnergyStoragePlanService.saveBatch(ciesEnergyStoragePlanEntities);
            // 更新审核状态为已下发
            CiesAuditOperateRequest auditStatusRequest = new CiesAuditOperateRequest();
            auditStatusRequest.setDispatchStatus(DispatchStatusEnum.ISSUED.getCode());
            auditStatusRequest.setProjectId(projectId);
            List<String> list = new ArrayList<>();
            list.add(today);
            auditStatusRequest.setPlanDateList(list);
            auditStatusRequest.setDr(YesOrNo.NO.getCode());
            iCiesEssPlanAuditService.updatePlanAuditStatus(auditStatusRequest);
        }catch (Exception e){
            log.error("项目：{}下发储能计划失败,失败原因为“{}",projectId,e.getMessage());
            throw new BusinessException("项目："+projectId+"下发储能计划失败，失败原因："+e.getMessage());
        }
    }

    /**
     * 合并为项目-并网点-计划关系
     *
     * @param projectConnMap
     * @param connPlanMap
     * @return
     */
    Map<String, Map<String, List<CiesEnergyStoragePlanResponse>>> mergeMaps(Map<String, List<String>> projectConnMap,
                                                                            Map<String, List<CiesEnergyStoragePlanResponse>> connPlanMap) {

        Map<String, Map<String, List<CiesEnergyStoragePlanResponse>>> result = new HashMap<>();

        for (Map.Entry<String, List<String>> projectEntry : projectConnMap.entrySet()) {
            Map<String, List<CiesEnergyStoragePlanResponse>> innerMap = new HashMap<>();

            for (String connId : projectEntry.getValue()) {
                if (connPlanMap.containsKey(connId)) {
                    innerMap.put(connId, connPlanMap.get(connId));
                }
            }

            if (!innerMap.isEmpty()) {
                result.put(projectEntry.getKey(), innerMap);
            }
        }

        return result;
    }

    /**
     * 根据下发规则替换设点和计划功率
     *
     * @param originalPlans
     * @param connPointId
     * @param rule
     */
    private void coverPowerByRule(List<CiesEnergyStoragePlanResponse> originalPlans, String connPointId, CiesEsPlanRuleResponse rule) {
        CiesConnectionPointResponse connPointInfo = iCiesConnectionPointService.findConnByConnPointId(connPointId);
        String dispatchRule = rule.getDispatchRule();
        // 如果并网点关联柜子数量为0 那么计划功率全部为0
        boolean flag = false;
        if (connPointInfo.getNum() == null || connPointInfo.getNum() == 0) {
            flag = true;
        }
        for (CiesEnergyStoragePlanResponse plan : originalPlans) {
            if (flag){
                plan.setPlannedPower(BigDecimal.ZERO);
                continue;
            }
            if (DispatchRuleEnum.EQUAL_POINT_DISPATCH.getCode().equals(dispatchRule)) {
                plan.setPlannedPower(plan.getPlannedPower().divide(new BigDecimal(connPointInfo.getNum()),
                        4,
                        RoundingMode.HALF_UP));
            }
        }
    }

    /**
     * 拆分计划
     * @param plan
     * @param rule
     * @return
     */
    private List<CiesEnergyStoragePlanResponse> spiltPlan(List<CiesEnergyStoragePlanResponse> plan, CiesEsPlanRuleResponse rule) {
        List<CiesEnergyStoragePlanResponse> sortedPlan = plan.stream().sorted(Comparator.comparing(CiesEnergyStoragePlanResponse::getStartTime)).toList();
        List<CiesEnergyStoragePlanResponse> result;
        boolean flag = is15MinuteMultiple(sortedPlan.get(0).getEndTime(), sortedPlan.get(0).getStartTime());

        if (TimeDimensionEnum.MINUTES_15.getCode().equals(rule.getTimeDimension())) {
            if (flag) {
                result = splitTo15MinIntervals(sortedPlan);
            } else {
                throw new BusinessException("并网点计划：" + sortedPlan.get(0).getConnectionPointId() + "时间维度发生变更，请管理员处理");
            }
        } else {
            if (flag) {
                throw new BusinessException("并网点计划：" + sortedPlan.get(0).getConnectionPointId() + "时间维度发生变更，请管理员处理");
            } else {
                result = splitToMinuteIntervals1(sortedPlan);
            }
        }
        LocalTime currentTime = LocalTime.now();  // 示例中为18:42

        // 如果是下个时间节点覆盖，则需要截取计划
        if (CoverageRuleEnum.NEXT_TIME_NODE_COVERAGE.getCode().equals(rule.getDailyOverrideRule())) {
            // 截取计划
            result = result.stream()
                    .filter(plan2 -> {
                        LocalTime planStart = LocalTime.parse(plan2.getStartTime());
                        return !planStart.isBefore(currentTime);  // 包含当前时间
                    })
                    .collect(Collectors.toList());
        }
        return result;
    }


    /**
     * 判断是否时间间隔是15分钟的倍数
     *
     * @param time1
     * @param time2
     * @return
     */
    private boolean is15MinuteMultiple(String time1, String time2) {
        LocalTime t1 = LocalTime.parse(time1);
        LocalTime t2 = LocalTime.parse(time2);
        long minutesBetween = ChronoUnit.MINUTES.between(t1, t2);
        return minutesBetween % 15 == 0 && minutesBetween != 0;
    }

    /**
     * 原始15分钟维度拆分为15分钟计划
     *
     * @param originalPlans
     * @return
     */
    private List<CiesEnergyStoragePlanResponse> splitTo15MinIntervals(List<CiesEnergyStoragePlanResponse> originalPlans) {
        List<CiesEnergyStoragePlanResponse> result = new ArrayList<>();

        for (CiesEnergyStoragePlanResponse plan : originalPlans) {
            LocalTime start = LocalTime.parse(plan.getStartTime());
            LocalTime end = LocalTime.parse(plan.getEndTime());

            // 处理跨天情况（end <= start表示跨天）
            boolean isCrossDay = !end.isAfter(start);
            LocalTime currentStart = start;
            do {
                LocalTime currentEnd = currentStart.plusMinutes(15);

                // 如果当前段跨越午夜，需要截断到end时间
                if (isCrossDay && currentEnd.isBefore(currentStart))  {
                    currentEnd = end;
                }

                CiesEnergyStoragePlanResponse splitPlan = BeanCopyUtil.copyProperties(plan,  CiesEnergyStoragePlanResponse::new);
                splitPlan.setStartTime(currentStart.toString());
                splitPlan.setEndTime(currentEnd.toString());

                result.add(splitPlan);
                currentStart = currentEnd;

            } while (currentStart.isBefore(end)  || (isCrossDay && !currentStart.equals(end)));
        }
        return result;
    }

    /**
     * 原始1分钟维度拆分为1分钟计划
     *
     * @param originalPlans
     * @return
     */
    private List<CiesEnergyStoragePlanResponse> splitToMinuteIntervals1(List<CiesEnergyStoragePlanResponse> originalPlans) {
        return originalPlans.stream()
                .flatMap(plan -> {
                    LocalTime start = LocalTime.parse(plan.getStartTime());
                    LocalTime end = LocalTime.parse(plan.getEndTime());

                    // 处理跨天场景（end <= start时表示跨天）
                    boolean isCrossDay = !end.isAfter(start);
                    long durationMinutes = isCrossDay
                            ? ChronoUnit.MINUTES.between(start,  LocalTime.MAX) + 1  // 23:59→00:00算1分钟
                            + ChronoUnit.MINUTES.between(LocalTime.MIN,  end)
                            : ChronoUnit.MINUTES.between(start,  end);

                    // 生成时间流（自动处理跨天）
                    return Stream.iterate(start,  time -> {
                                LocalTime next = time.plusMinutes(1);
                                return next.equals(LocalTime.MIN)  ? null : next; // 00:00时终止
                            })
                            .limit(durationMinutes)
                            .map(minuteStart -> {
                                CiesEnergyStoragePlanResponse splitPlan = BeanCopyUtil.copyProperties(plan,  CiesEnergyStoragePlanResponse::new);
                                splitPlan.setStartTime(minuteStart.toString());
                                splitPlan.setEndTime(
                                        minuteStart.equals(LocalTime.MAX)
                                                ? LocalTime.MIN.toString()   // 23:59→00:00
                                                : minuteStart.plusMinutes(1).toString()
                                );
                                return splitPlan;
                            });
                })
                .collect(Collectors.toList());
    }

    private String getIndicatorId(CiesSpecialIndicatorsResponse indicatorsResponse) {
        if (ObjectUtils.isEmpty(indicatorsResponse)){
            return null;
        }
        String indicatorId = indicatorsResponse.getRelateIndicatorId();
        if ("测点".equals(indicatorsResponse.getRelateDataType())) {
            indicatorId = String.format("%s-%s-%s-%s", indicatorsResponse.getProjectId(), indicatorsResponse.getCId(), indicatorsResponse.getEquipId(), indicatorsResponse.getRelateIndicatorId());
        }
        return indicatorId;
    }

    /**
     * 批量获取指标数据
     * @param indicatorsResponse
     * @param startTime
     * @param endTime
     * @return
     */
    private  Map<String,List<CiesHisIndicatorsDataResponse>> batchQueryIndicatorValue(CiesSpecialIndicatorsResponse indicatorsResponse,LocalDateTime startTime,LocalDateTime endTime){
        String indicatorId = getIndicatorId(indicatorsResponse);
        if (indicatorId == null){
            return null;
        }
        List<String>  specialIds= new ArrayList<>();
        specialIds.add(indicatorId);
        CiesQueryIndicatorsRequest request1 = new CiesQueryIndicatorsRequest();
        request1.setIndicatorIds(specialIds);
        request1.setStartTime(startTime);
        request1.setEndTime(endTime);
        request1.setDataType(indicatorsResponse.getRelateDataType());
        Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap = client.batchQueryHisData(request1);
        log.info("重新计算获取数据，请求参数：{}，响应结果：{}", JSONObject.toJSONString(request1), stringListMap);
        return stringListMap;
    }

    /**
     * 计算充放电累计结果
     * @param list
     * @param periodPrice
     * @return
     */
    private BigDecimal calDailyData(List<CiesHisIndicatorsDataResponse> list, Map<String, BigDecimal> periodPrice) {
        BigDecimal result = BigDecimal.ZERO;
        // 定义目标格式（不含毫秒）
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        list.forEach(obj -> {
            // 截断毫秒（保留到秒）
            LocalDateTime truncatedTime = obj.getUpdateTime().truncatedTo(ChronoUnit.SECONDS);

            // 格式化为字符串
            String formattedTime = truncatedTime.format(outputFormatter);
            obj.setUpdateTimeStr(formattedTime);
        });
        for (CiesHisIndicatorsDataResponse indicatorValue : list) {
            BigDecimal priceValue = periodPrice.get(indicatorValue.getUpdateTimeStr());
            if (priceValue == null) {
                priceValue = BigDecimal.ZERO;
            }
            BigDecimal temp = priceValue.multiply(indicatorValue.getIndicatorValue()).setScale(4, RoundingMode.HALF_UP);
            result = result.add(temp);
        }
        return result;
    }


    /**
     * 组装单个并网点计划报文（下发数据中台）
     * @param coverResultPlan
     * @param connId
     * @param map
     * @return
     */
    private JSONObject assemblyIssuePlan(List<CiesEnergyStoragePlanResponse> coverResultPlan, String connId, Map<String, String> map,CiesEsPlanRuleResponse rule) {
        JSONObject result = new JSONObject();
        List<BigDecimal> power = new ArrayList<>();
        for (CiesEnergyStoragePlanResponse item : coverResultPlan) {
            JSONObject jsonObject = new JSONObject();
            if (EnergyStateEnum.CHARGE.getCode().equals(item.getChargeDischargeType())){
                power.add(item.getPlannedPower().negate());
            }else{
                power.add(item.getPlannedPower());
            }
            jsonObject.put("chargeDischargeType", item.getChargeDischargeType());
        }
        result.put("val_array", power);
        // 判断项目规则是0点开始还是当前时间的下一个时间开始
        // 获取初始时间
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalDate date = LocalDate.parse(coverResultPlan.get(0).getPlanDate().replace("/",  "-"));
        LocalDateTime dateTime = date.atTime(
                LocalTime.parse(coverResultPlan.get(0).getStartTime(),  timeFormatter).withSecond(0)  // 强制秒数为00
        );
        // 获取毫秒数（系统默认时区）
        long milliseconds = dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        result.put("start_timestamp", milliseconds);
        result.put("channelId", map.get(connId));
        // 填充设点起始id
        CiesConnectionPointEntity byId = iCiesConnectionPointService.getById(connId);
        if (byId.getPlanStartId() != null) {
            int planStart = byId.getPlanStartId();
            TimeSlotConverter timeSlotConverter;
            if (TimeDimensionEnum.MINUTES_15.getCode().equals(rule.getTimeDimension())) {
                timeSlotConverter = new TimeSlotConverter(planStart, 15, 96);
            } else {
                timeSlotConverter = new TimeSlotConverter(planStart, 1, 1440);
            }
            Integer index = timeSlotConverter.getIndex(coverResultPlan.get(0).getStartTime());
            result.put("planStartId", index);
        }
        // 时间维度15min对应3  1min对应5
        result.put("plan_type", TimeDimensionEnum.MINUTES_15.getCode().equals(rule.getTimeDimension()) ? "3" : "5");
        return result;
    }
}
