package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("cies_indicators_info")
@ApiModel(value = "CiesIndicatorsInfoEntity对象", description = "测点与指标信息")
public class CiesIndicatorsInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("指标主键")
    @TableId
    private String indicatorId;

    @ApiModelProperty("数据类型")
    private String dataType;

    @ApiModelProperty("数据名称")
    private String dataName;

    @ApiModelProperty("页面展示名称")
    private String pageDisplayName;

    @ApiModelProperty("分类内排序")
    private Integer sort;

    @ApiModelProperty("是否显示(0:不显示 1:显示)")
    private Integer isShow;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("最新数据")
    private BigDecimal currentValue;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("设备主键")
    private String equipId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
