package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@Schema(description = "获取指标数据")
public class CiesQueryIndicatorsRequest implements Serializable {

    @Schema(description = "指标ID集合")
    private List<String> indicatorIds;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "数据类型")
    private String dataType;
}
