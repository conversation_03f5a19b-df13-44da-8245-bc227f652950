package com.zwy.common.utils;

import java.util.regex.*;

/**
 * <AUTHOR>
 * 2024/12/12 9:39
 */
public class MyStringUtils {

    public static String camelToSnake(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 使用正则表达式匹配大写字母
        Matcher m = Pattern.compile("([a-z])([A-Z])").matcher(input);
        // 将大写字母前加上下划线并转换为小写
        return m.replaceAll("$1_$2").toLowerCase();
    }
}
