package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 储能计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Getter
@Setter
@TableName("cies_energy_storage_plan")
@ApiModel(value = "CiesEnergyStoragePlanEntity对象", description = "储能计划表")
public class CiesEnergyStoragePlanEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("储能计划主键")
    @TableId
    private String energyStoragePlanId;

    @ApiModelProperty("计划日期")
    private String planDate;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("充放电类型")
    private String chargeDischargeType;

    @ApiModelProperty("计划功率")
    private BigDecimal plannedPower;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("并网点主键")
    private String connectionPointId;

    @ApiModelProperty("历史记录ID")
    private String historyRecordId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Byte dr;
}
