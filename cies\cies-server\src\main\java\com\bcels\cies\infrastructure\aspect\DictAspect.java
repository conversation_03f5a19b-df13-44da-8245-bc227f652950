/*
 * Copyright © 2024 富鸿资本（湖南）融资租赁有限公司 版权所有
 */
package com.bcels.cies.infrastructure.aspect;

import com.bcels.cies.repository.entity.CiesDictEntity;
import com.bcels.cies.service.ICiesDictService;
import com.zwy.common.utils.annotation.Dict;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Aspect
@Component
public class DictAspect {

    private static final Logger logger = LoggerFactory.getLogger(DictAspect.class);

    @Autowired
    private ICiesDictService iCiesDictService;

    /**
     * 环绕通知，处理Controller方法返回值中的 @Dict 注解
     */
    @Around("@annotation(com.zwy.common.utils.annotation.Dict)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行原方法
        Object result = joinPoint.proceed();

        if (result == null) {
            return result;
        }

        // 处理返回值中的字段
        processDictAnnotation(result);

        return result;
    }

    /**
     * 处理对象中的 @Dict 注解 - 重构为两阶段处理模式
     */
    private void processDictAnnotation(Object obj) {
        if (obj == null) {
            return;
        }

        long startTime = System.currentTimeMillis();
        DictTranslationContext context = new DictTranslationContext();

        try {
            // 第一阶段：收集所有需要翻译的字段
            collectDictFields(obj, context);

            // 第二阶段：批量查询并应用翻译
            batchTranslateAndApply(context);

            long endTime = System.currentTimeMillis();
            logger.info("字典翻译完成，耗时: {}ms, 处理字段数: {}",
                        endTime - startTime, context.getDictFields().size());

        } catch (Exception e) {
            logger.error("字典翻译处理异常", e);
        }
    }

    /**
     * 第一阶段：收集所有需要翻译的字段信息
     */
    private void collectDictFields(Object obj, DictTranslationContext context) {
        if (obj == null) {
            logger.debug("对象为null，跳过处理");
            return;
        }

        if (context.exceedsMaxDepth()) {
            logger.debug("超过最大递归深度，跳过处理");
            return;
        }

        // 防止循环引用
        if (context.isVisited(obj)) {
            logger.debug("对象已访问过，跳过处理: {}", obj.getClass().getSimpleName());
            return;
        }

        logger.debug("开始收集对象字段: {}", obj.getClass().getSimpleName());
        context.markVisited(obj);
        context.incrementDepth();

        try {
            // 特殊处理Map对象（如R对象继承HashMap）
            if (obj instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) obj;
                for (Map.Entry<?, ?> entry : map.entrySet()) {
                    collectDictFields(entry.getValue(), context);
                }
                return;
            }

            // 处理集合类型
            if (obj instanceof Collection) {
                Collection<?> collection = (Collection<?>) obj;
                for (Object item : collection) {
                    collectDictFields(item, context);
                }
                return;
            }

            // 跳过基本类型
            if (isBasicType(obj.getClass())) {
                return;
            }

            // 处理对象的所有字段
            Field[] fields = obj.getClass().getDeclaredFields();
            logger.debug("对象 {} 共有 {} 个字段", obj.getClass().getSimpleName(), fields.length);

            for (Field field : fields) {
                try {
                    // 跳过某些特殊字段，避免访问JDK内部字段
                    if (shouldSkipField(field)) {
                        logger.debug("跳过字段: {} (类型: {})", field.getName(), field.getType().getSimpleName());
                        continue;
                    }

                    field.setAccessible(true);

                    // 检查字段是否有 @Dict 注解
                    Dict dictAnnotation = field.getAnnotation(Dict.class);
                    if (dictAnnotation != null) {
                        logger.info("发现@Dict注解字段: {}, 字典类型: {}", field.getName(), dictAnnotation.code());
                        Object fieldValue = field.get(obj);
                        if (fieldValue != null) {
                            // 收集需要翻译的字段信息
                            context.addDictField(new DictFieldInfo(obj, field, dictAnnotation, fieldValue));
                            logger.info("成功收集字典字段: {}, 值: {}", field.getName(), fieldValue);
                        } else {
                            logger.debug("字典字段 {} 的值为null", field.getName());
                        }
                    } else {
                        // 递归处理非基本类型字段
                        Object fieldValue = field.get(obj);
                        if (fieldValue != null && !isBasicType(fieldValue.getClass()) && !isJdkInternalType(fieldValue.getClass())) {
                            logger.debug("递归处理字段: {} (类型: {})", field.getName(), fieldValue.getClass().getSimpleName());
                            collectDictFields(fieldValue, context);
                        }
                    }
                } catch (Exception e) {
                    // 降低日志级别，避免大量JDK内部字段访问异常日志
                    logger.debug("收集字段信息异常：{}, 类型: {}, 异常: {}",
                               field.getName(), obj.getClass().getSimpleName(), e.getMessage());
                }
            }
        } finally {
            context.decrementDepth();
        }
    }

    /**
     * 第二阶段：批量查询并应用翻译结果
     */
    private void batchTranslateAndApply(DictTranslationContext context) {
        List<DictFieldInfo> dictFields = context.getDictFields();
        if (dictFields.isEmpty()) {
            logger.debug("没有找到需要翻译的字典字段");
            return;
        }

        logger.info("开始批量翻译，共找到 {} 个字典字段", dictFields.size());

        // 按字典类型分组，准备批量查询
        Map<String, Set<String>> dictTypeToCodesMap = new HashMap<>();
        for (DictFieldInfo fieldInfo : dictFields) {
            String dictType = fieldInfo.getDictAnnotation().code();
            String dictCode = fieldInfo.getFieldValue().toString();

            logger.debug("收集字典字段：类型={}, 编码={}, 字段={}",
                        dictType, dictCode, fieldInfo.getField().getName());

            dictTypeToCodesMap.computeIfAbsent(dictType, k -> new HashSet<>()).add(dictCode);
        }

        // 批量查询字典数据
        for (Map.Entry<String, Set<String>> entry : dictTypeToCodesMap.entrySet()) {
            String dictType = entry.getKey();
            try {
                logger.debug("查询字典类型: {}", dictType);
                Map<String, String> dictMap = iCiesDictService.findDictByDictType(dictType);
                if (dictMap != null && !dictMap.isEmpty()) {
                    context.putDictCache(dictType, dictMap);
                    logger.info("字典类型 {} 查询成功，共 {} 条记录: {}", dictType, dictMap.size(), dictMap);
                } else {
                    logger.warn("字典类型 {} 查询结果为空", dictType);
                }
            } catch (Exception e) {
                logger.error("批量查询字典异常，dictType: {}", dictType, e);
            }
        }

        // 应用翻译结果
        int successCount = 0;
        for (DictFieldInfo fieldInfo : dictFields) {
            try {
                boolean applied = applyDictTranslation(fieldInfo, context);
                if (applied) {
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("应用字典翻译异常，字段: {}", fieldInfo.getField().getName(), e);
            }
        }

        logger.info("字典翻译完成，成功翻译 {}/{} 个字段", successCount, dictFields.size());
    }

    /**
     * 应用字典翻译到具体字段
     * @return 是否成功应用翻译
     */
    private boolean applyDictTranslation(DictFieldInfo fieldInfo, DictTranslationContext context) {
        try {
            String dictType = fieldInfo.getDictAnnotation().code();
            String dictCode = fieldInfo.getFieldValue().toString();
            String fieldName = fieldInfo.getField().getName();

            logger.debug("尝试翻译字段: {}, 字典类型: {}, 字典编码: {}", fieldName, dictType, dictCode);

            // 从缓存中获取翻译结果
            String dictValue = context.getDictValue(dictType, dictCode);

            if (dictValue != null) {
                // 替换字段值
                Object oldValue = fieldInfo.getFieldValue();
                fieldInfo.getField().set(fieldInfo.getTargetObject(), dictValue);
                logger.debug("字段 {} 翻译成功: {} -> {}", fieldName, oldValue, dictValue);
                return true;
            } else {
                // 如果有默认值，使用默认值
                String defaultValue = fieldInfo.getDictAnnotation().defaultValue();
                if (!defaultValue.isEmpty()) {
                    fieldInfo.getField().set(fieldInfo.getTargetObject(), defaultValue);
                    logger.debug("字段 {} 使用默认值: {}", fieldName, defaultValue);
                    return true;
                } else {
                    logger.debug("字段 {} 未找到字典值，字典类型: {}, 编码: {}", fieldName, dictType, dictCode);
                    return false;
                }
            }
        } catch (IllegalAccessException e) {
            logger.error("设置字段值异常，字段: {}", fieldInfo.getField().getName(), e);
            return false;
        }
    }

    /**
     * 查询字典值
     */
    private String findDictValue(String dictType, String dictCode) {
        try {
            CiesDictEntity dictEntity = iCiesDictService.findDictByDictCodeAndName(dictType, dictCode);
            if (dictEntity != null) {
                return dictEntity.getDictDesc();
            }
        } catch (Exception e) {
            logger.error("查询字典异常", e);
        }
        return null;
    }

    /**
     * 判断是否为基本类型
     */
    /**
     * 判断是否应该跳过某个字段的处理
     */
    private boolean shouldSkipField(Field field) {
        String fieldName = field.getName();
        Class<?> fieldType = field.getType();

        // 跳过序列化相关字段
        if ("serialVersionUID".equals(fieldName)) {
            return true;
        }

        // 跳过静态字段
        if (java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
            return true;
        }

        // 只有在递归处理时才跳过JDK内部复杂类型，但不跳过基本类型字段的@Dict注解检查
        // 这里不应该跳过String等基本类型，因为它们可能有@Dict注解
        return false;
    }

    private boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() ||
               clazz == String.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Double.class ||
               clazz == Float.class ||
               clazz == Boolean.class ||
               clazz == Byte.class ||
               clazz == Short.class ||
               clazz == Character.class ||
               Number.class.isAssignableFrom(clazz) ||
               // 添加时间类型，避免反射访问JDK内部字段
               clazz == LocalDateTime.class ||
               clazz == LocalDate.class ||
               clazz == LocalTime.class ||
               clazz == ZonedDateTime.class ||
               clazz == OffsetDateTime.class ||
               clazz == Instant.class ||
               clazz == Duration.class ||
               clazz == Period.class ||
               // 添加其他常见的不可变类型
               clazz == BigDecimal.class ||
               clazz == java.util.Date.class ||
               clazz == java.sql.Date.class ||
               clazz == java.sql.Timestamp.class ||
               // 添加枚举类型
               clazz.isEnum();
    }

    /**
     * 字典翻译上下文，管理翻译过程中的状态
     */
    private static class DictTranslationContext {
        // 使用 IdentityHashMap 防止循环引用
        private final IdentityHashMap<Object, Boolean> visitedObjects = new IdentityHashMap<>();
        // 收集需要翻译的字段信息
        private final List<DictFieldInfo> dictFields = new ArrayList<>();
        // 缓存字典查询结果，避免重复查询
        private final Map<String, Map<String, String>> dictCache = new ConcurrentHashMap<>();
        // 递归深度计数器
        private int recursionDepth = 0;
        // 最大递归深度限制
        private static final int MAX_RECURSION_DEPTH = 50;

        public boolean isVisited(Object obj) {
            return visitedObjects.containsKey(obj);
        }

        public void markVisited(Object obj) {
            visitedObjects.put(obj, Boolean.TRUE);
        }

        public void addDictField(DictFieldInfo fieldInfo) {
            dictFields.add(fieldInfo);
        }

        public List<DictFieldInfo> getDictFields() {
            return dictFields;
        }

        public void putDictCache(String dictType, Map<String, String> dictMap) {
            dictCache.put(dictType, dictMap);
        }

        public String getDictValue(String dictType, String dictCode) {
            Map<String, String> dictMap = dictCache.get(dictType);
            return dictMap != null ? dictMap.get(dictCode) : null;
        }

        public boolean exceedsMaxDepth() {
            return recursionDepth >= MAX_RECURSION_DEPTH;
        }

        public void incrementDepth() {
            recursionDepth++;
        }

        public void decrementDepth() {
            recursionDepth--;
        }
    }

    /**
     * 字典字段信息，存储需要翻译的字段详情
     */
    private static class DictFieldInfo {
        private final Object targetObject;
        private final Field field;
        private final Dict dictAnnotation;
        private final Object fieldValue;

        public DictFieldInfo(Object targetObject, Field field, Dict dictAnnotation, Object fieldValue) {
            this.targetObject = targetObject;
            this.field = field;
            this.dictAnnotation = dictAnnotation;
            this.fieldValue = fieldValue;
        }

        public Object getTargetObject() {
            return targetObject;
        }

        public Field getField() {
            return field;
        }

        public Dict getDictAnnotation() {
            return dictAnnotation;
        }

        public Object getFieldValue() {
            return fieldValue;
        }
    }
}
