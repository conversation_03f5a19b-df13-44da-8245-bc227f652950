/*
 * Copyright © 2024 富鸿资本（湖南）融资租赁有限公司 版权所有
 */
package com.bcels.cies.infrastructure.aspect;

import com.bcels.cies.repository.entity.CiesDictEntity;
import com.bcels.cies.service.ICiesDictService;
import com.zwy.common.utils.annotation.Dict;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Map;

@Aspect
@Component
public class DictAspect {

    private static final Logger logger = LoggerFactory.getLogger(DictAspect.class);

    @Autowired
    private ICiesDictService iCiesDictService;

    /**
     * 环绕通知，处理Controller方法返回值中的 @Dict 注解
     */
    @Around("@annotation(com.zwy.common.utils.annotation.Dict)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 执行原方法
        Object result = joinPoint.proceed();

        if (result == null) {
            return result;
        }

        // 处理返回值中的字段
        processDictAnnotation(result);

        return result;
    }

    /**
     * 处理对象中的 @Dict 注解
     */
    private void processDictAnnotation(Object obj) {
        if (obj == null) {
            return;
        }


        // 特殊处理R对象（继承HashMap）
        if (obj instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) obj;
            for (Map.Entry<?, ?> entry : map.entrySet()) {
                // 递归处理Map中的值
                processDictAnnotation(entry.getValue());
            }
            return;
        }

        // 处理集合类型
        if (obj instanceof Collection) {
            Collection<?> collection = (Collection<?>) obj;
            for (Object item : collection) {
                processDictAnnotation(item);
            }
            return;
        }

        // 跳过基本类型
        if (isBasicType(obj.getClass())) {
            return;
        }

        // 处理对象的所有字段
        Field[] fields = obj.getClass().getDeclaredFields();

        for (Field field : fields) {
            try {
                // 使私有字段可访问
                field.setAccessible(true);
                
                // 检查字段是否有 @Dict 注解
                Dict dictAnnotation = field.getAnnotation(Dict.class);
                if (dictAnnotation != null) {

                    // 获取字段值
                    Object fieldValue = field.get(obj);
                    if (fieldValue == null) {
                        continue;
                    }

                    String originalValue = fieldValue.toString();

                    // 查询字典值
                    String keyName = dictAnnotation.code();
                    String dictValue = findDictValue(keyName, originalValue);

                    if (dictValue != null) {
                        // 替换字段值
                        field.set(obj, dictValue);
                    } else {
                        // 如果有默认值，使用默认值
                        if (!dictAnnotation.defaultValue().isEmpty()) {
                            field.set(obj, dictAnnotation.defaultValue());
                        }
                    }
                } else {
                    // 获取字段值，递归处理
                    Object fieldValue = field.get(obj);

                    if (fieldValue != null) {
                        // 如果是集合，递归处理
                        if (fieldValue instanceof Collection) {
                            processDictAnnotation(fieldValue);
                        } 
                        // 如果是对象，递归处理
                        else if (!isBasicType(fieldValue.getClass())) {
                            processDictAnnotation(fieldValue);
                        }
                    }
                }

            } catch (Exception e) {
                logger.error("处理字段异常：{}", field.getName(), e);
            }
        }
    }

    /**
     * 查询字典值
     */
    private String findDictValue(String dictType, String dictCode) {
        try {
            CiesDictEntity dictEntity = iCiesDictService.findDictByDictCodeAndName(dictType, dictCode);
            if (dictEntity != null) {
                return dictEntity.getDictDesc();
            }
        } catch (Exception e) {
            logger.error("查询字典异常", e);
        }
        return null;
    }

    /**
     * 判断是否为基本类型
     */
    private boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() || 
               clazz == String.class || 
               clazz == Integer.class || 
               clazz == Long.class || 
               clazz == Double.class || 
               clazz == Float.class || 
               clazz == Boolean.class || 
               clazz == Byte.class || 
               clazz == Short.class || 
               clazz == Character.class ||
               Number.class.isAssignableFrom(clazz);
    }
}
