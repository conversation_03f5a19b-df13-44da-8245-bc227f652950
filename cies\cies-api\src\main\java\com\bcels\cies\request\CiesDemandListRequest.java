package com.bcels.cies.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@Schema(description = "需量下发")
public class CiesDemandListRequest implements Serializable {

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "需量列表")
    private List<CiesDemandRequest> list;

}
