package com.bcels.cies.repository.entity;

import java.util.*;

public class DynamicEntity {
    private final LinkedHashMap<String, Object> properties;
    private final List<String> headerTemplate;

    public DynamicEntity(List<String> headers) {
        this.headerTemplate  = new ArrayList<>(headers);
        this.properties  = new LinkedHashMap<>();
        headers.forEach(h  -> properties.put(h,  null)); // 预初始化
    }

    /**
     * 类型安全设置属性
     * @param column
     * @param value
     * @param <T>
     */
    public <T> void setProperty(String column, T value) {
        if (!headerTemplate.contains(column))  {
            throw new IllegalArgumentException("无效列名: " + column);
        }
        properties.put(column,  value);
    }

    /**
     * 类型安全获取属性
     * @param column
     * @param type
     * @return
     * @param <T>
     */
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String column, Class<T> type) {
        Object value = properties.get(column);
        if (value != null && !type.isInstance(value))  {
            throw new ClassCastException("类型不匹配");
        }
        return (T) value;
    }

    /**
     * 转换为二维数组（行x列）
     * @return
     */
    public Object[][] to2DArray() {
        Object[][] result = new Object[1][headerTemplate.size()]; // 单行数据
        for (int i = 0; i < headerTemplate.size();  i++) {
            String header = headerTemplate.get(i);
            result[0][i] = properties.get(header);
        }
        return result;
    }

    /**
     * 批量实体转二维数组（静态方法）
     * @param entities
     * @return
     */
    public static Object[][] to2DArray(List<DynamicEntity> entities) {
        if (entities.isEmpty())  return new Object[0][0];

        Object[][] result = new Object[entities.size()][];
        for (int i = 0; i < entities.size();  i++) {
            result[i] = entities.get(i).toRowArray();
        }
        return result;
    }

    /**
     * 转换为单行数组
     * @return
     */
    private Object[] toRowArray() {
        Object[] row = new Object[headerTemplate.size()];
        for (int i = 0; i < headerTemplate.size();  i++) {
            row[i] = properties.get(headerTemplate.get(i));
        }
        return row;
    }
}
