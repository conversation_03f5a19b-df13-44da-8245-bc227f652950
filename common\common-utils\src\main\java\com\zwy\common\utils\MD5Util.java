package com.zwy.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;

@Slf4j
public class MD5Util {


    public MD5Util() {
    }

    public static String encodeMD5(String str, String key) {
        try {
            return MD5(str + key);
        } catch (Exception var3) {
            log.error("encodeMD5 异常", var3);
            return null;
        }
    }

    private static String MD5(String str) {
        char[] hexDigits = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

        try {
            if (str == null) {
                log.info("MD5 加密字符串为空");
                return null;
            } else {
                byte[] btInput = str.getBytes();
                MessageDigest mdInst = MessageDigest.getInstance("MD5");
                mdInst.update(btInput);
                byte[] md = mdInst.digest();
                int j = md.length;
                char[] strs = new char[j * 2];
                int k = 0;

                for (int i = 0; i < j; ++i) {
                    byte byte0 = md[i];
                    strs[k++] = hexDigits[byte0 >>> 4 & 15];
                    strs[k++] = hexDigits[byte0 & 15];
                }

                return (new String(strs)).toLowerCase();
            }
        } catch (Exception var10) {
            log.error("MD5 异常：" + var10.getMessage());
            return null;
        }
    }
}
