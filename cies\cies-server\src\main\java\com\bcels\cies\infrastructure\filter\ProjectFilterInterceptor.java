//package com.bcels.cies.infrastructure.filter;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.bcels.cies.infrastructure.utils.RedisUtils;
//import com.bcels.cies.infrastructure.utils.RestTemplateUtil;
//import jakarta.servlet.http.HttpServletRequest;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.executor.statement.StatementHandler;
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.plugin.Interceptor;
//import org.apache.ibatis.plugin.Intercepts;
//import org.apache.ibatis.plugin.Invocation;
//import org.apache.ibatis.plugin.Signature;
//import org.apache.ibatis.reflection.MetaObject;
//import org.apache.ibatis.reflection.SystemMetaObject;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//import org.springframework.web.util.UriComponentsBuilder;
//
//import java.nio.charset.StandardCharsets;
//import java.sql.Connection;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Set;
//import java.util.concurrent.TimeUnit;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
//@Intercepts({
//        @Signature(
//                type = StatementHandler.class,   // 拦截StatementHandler
//                method = "prepare",            // 拦截prepare方法
//                args = {Connection.class, Integer.class}
//        )
//})
//@Component
//@Slf4j
//public class ProjectFilterInterceptor implements Interceptor {
//
//    @Value("${app.auth.permission-url}")
//    private String permissionUrl;
//
//    @Autowired
//    private RestTemplateUtil restTemplateUtil;
//
//    @Autowired
//    private RedisUtils redisUtils;
//
//    private static final String SUCCESS_CODE = "200";
//
//    // 配置需要过滤的目标表
//    private static final Set<String> TARGET_TABLES = Set.of("cies_project_info");
//
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        StatementHandler handler = (StatementHandler) invocation.getTarget();
//        BoundSql boundSql = handler.getBoundSql();
//        String originalSql = boundSql.getSql();
//
//        // 仅处理目标表相关SQL
//        if (isTargetTableInvolved(originalSql)) {
//            /**
//             *非web请求不处理
//             */
//            if (RequestContextHolder.getRequestAttributes() == null) {
//                return invocation.proceed();
//            }
//            HttpServletRequest request = ((ServletRequestAttributes)
//                    RequestContextHolder.getRequestAttributes()).getRequest();
//            String token = request.getHeader("Authorization");
//
//            String tenantCode = request.getHeader("tenantCode");
//            if (tenantCode == null) {
//                return invocation.proceed();
//            }
//            String jsonStr = JSONObject.toJSONString(request.getHeader("X-SSO-USER"));
//            JSONObject jsonObj = JSONObject.parseObject(jsonStr);
//            String userCode = jsonObj.getString("userCode");
//
//            // 3. 获取当前用户有权限的项目ID
//            String cacheKey1 = "user:perms:" + token;
//            List<String> projectIds;
//             projectIds = (List<String>) redisUtils.get(cacheKey1);
//            if (CollectionUtils.isEmpty(projectIds)) {
//                projectIds = fetchPermissions(tenantCode, userCode);
//                redisUtils.set(
//                        "user:perms:" + token,
//                        projectIds,
//                        24, TimeUnit.HOURS
//                );
//            }
//            log.info("查询当前用户权限为：{}", projectIds);
//            if (projectIds != null && !projectIds.isEmpty()) {
//                String newSql = injectCondition(originalSql, projectIds);
//                resetSql(handler, newSql);
//            }
//        }
//        return invocation.proceed();
//    }
//
//    private boolean isTargetTableInvolved(String sql) {
//        String lowerSql = sql.toLowerCase();
//        return TARGET_TABLES.stream().anyMatch(table ->
//                lowerSql.contains(" " + table.toLowerCase() + " ") ||
//                        lowerSql.contains(" " + table.toLowerCase() + ",") ||
//                        lowerSql.contains(" " + table.toLowerCase() + ".")
//        );
//    }
//
//    private String injectCondition(String originalSql, List<String> projectIds) {
//        try {
//            // 标准化SQL分析（保留原始大小写）
//            String normalizedSql = originalSql.replaceAll("\\s+", " ").trim();
//
//            // 定位关键子句位置
//            int fromIndex = findKeywordPosition(normalizedSql, "from");
//            int whereIndex = findKeywordPosition(normalizedSql, "where");
//            int groupByIndex = findKeywordPosition(normalizedSql, "group by");
//            int orderByIndex = findKeywordPosition(normalizedSql, "order by");
//            int limitIndex = findKeywordPosition(normalizedSql, "limit");
//
//            //  识别目标表别名（增强版正则）
//            Pattern pattern = Pattern.compile(
//                    "(?:from|join)\\s+(?:cies_project_info|`cies_project_info`|\"cies_project_info\")\\s+(?:as\\s+)?(\\w+|`[^`]+`|\"[^\"]+\")(?=\\s+on)",
//                    Pattern.CASE_INSENSITIVE
//            );
//            Matcher tableMatcher = pattern.matcher(normalizedSql.substring(fromIndex));
//
//            String tableAlias = null; // 默认别名
//            if (tableMatcher.find()) {  // 直接取第一个匹配（不再需要 while 循环）
//                tableAlias = tableMatcher.group(1);   // 别名是 group(1)
//            }
//            String condition = "";
//            if (tableAlias == null) {
//                //  构建条件片段（无别名）
//                condition = String.format("project_id  IN ('%s')",
//                        String.join("','", projectIds)
//                );
//            } else {
//                //  构建条件片段(有别名)
//                condition = String.format("%s.project_id  IN ('%s')",
//                        tableAlias,
//                        String.join("','", projectIds)
//                );
//            }
//
//            //  动态插入条件
//            StringBuilder newSql = new StringBuilder(normalizedSql);
//            int insertPos;
//
//            if (whereIndex > 0) {
//                insertPos = whereIndex + " where ".length()-1;
//                newSql.insert(insertPos, condition + " AND ");
//            } else {
//                if (groupByIndex > 0) insertPos = groupByIndex;
//                else if (orderByIndex > 0) insertPos = orderByIndex;
//                else if (limitIndex > 0) insertPos = limitIndex;
//                else insertPos = newSql.length();
//
//                newSql.insert(insertPos, " WHERE " + condition);
//            }
//            return newSql.toString();
//        } catch (StringIndexOutOfBoundsException e) {
//            log.error("SQL  处理越界，保留原始SQL: {}", originalSql);
//            return originalSql;
//        }
//    }
//
//
//    private void resetSql(StatementHandler handler, String newSql) {
//        MetaObject metaObject = SystemMetaObject.forObject(handler);
//        metaObject.setValue("delegate.boundSql.sql", newSql);
//
//        // 处理参数映射
//        if (handler.getParameterHandler() != null) {
//            metaObject.setValue("delegate.parameterHandler.parameterObject",
//                    handler.getBoundSql().getParameterObject());
//        }
//    }
//
//    private List<String> fetchPermissions(String tenantCode, String userCode) {
//        List<String> list = new ArrayList<>();
//        try {
//            String url = UriComponentsBuilder
//                    .fromHttpUrl(permissionUrl)
//                    .queryParam("userCode", userCode)
//                    .queryParam("tenantCode", tenantCode)
//                    .encode(StandardCharsets.UTF_8)
//                    .toUriString();
//
//            JSONObject jsonObject = restTemplateUtil.get(url, null, JSONObject.class);
//            if (SUCCESS_CODE.equals(jsonObject.getString("code")) && jsonObject.getJSONObject("data").containsKey("permissionsByProjectResponses")) {
//                JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("permissionsByProjectResponses");
//                if (jsonArray != null && jsonArray.size() > 0) {
//                    for (int i = 0; i < jsonArray.size(); i++) {
//                        String permission = jsonArray.getJSONObject(i).getString("projectId");
//                        list.add(permission);
//                    }
//                }
//            }
//            return list;
//        } catch (Exception e) {
//            // 不做处理，获取权限
//            log.error("权限查询报错，错误信息为：{}",e.getMessage());
//        }
//        return null;
//    }
//
//    private int findKeywordPosition(String sql, String keyword) {
//        Pattern pattern = Pattern.compile("\\b" + keyword + "\\b", Pattern.CASE_INSENSITIVE);
//        Matcher matcher = pattern.matcher(sql);
//        return matcher.find() ? matcher.start() : -1;
//    }
//}