package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "操作记录")
public class CiesOperationLogResponse implements Serializable {

    @Schema(description = "操作记录主键")
    private String logId;

    @Schema(description = "变更内容")
    private String changeContent;

    @Schema(description = "反馈内容")
    private String feedbackContent;

    @Schema(description = "变更时间")
    private LocalDateTime changeTime;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "储能结算主键")
    private String essBillId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;
}
