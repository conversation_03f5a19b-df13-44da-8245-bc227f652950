package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "指标实时数据")
public class CiesLatestIndicatorsDataResponse implements Serializable {

    @Schema(description = "指标主键")
    private String indicatorId;

    @Schema(description = "最新数据")
    private BigDecimal latestData;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 无需查询返回
     */
    @Schema(description = "指标展示名称")
    private String pageDisplayName;
}
