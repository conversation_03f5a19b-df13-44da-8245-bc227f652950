package com.bcels.cies.emuns;

import lombok.Getter;

/**
 * 半小时时间槽枚举（00:00-24:00）
 */
@Getter
public enum TimeSlotEnum {
    // 00:00-12:00
    SLOT_0005("00:00-00:30", "period0005"),
    SLOT_0510("00:30-01:00", "period0510"),
    SLOT_1015("01:00-01:30", "period1015"),
    SLOT_1520("01:30-02:00", "period1520"),
    SLOT_2025("02:00-02:30", "period2025"),
    SLOT_2530("02:30-03:00", "period2530"),
    SLOT_3035("03:00-03:30", "period3035"),
    SLOT_3540("03:30-04:00", "period3540"),
    SLOT_4045("04:00-04:30", "period4045"),
    SLOT_4550("04:30-05:00", "period4550"),
    SLOT_5055("05:00-05:30", "period5055"),
    SLOT_5560("05:30-06:00", "period5560"),
    SLOT_6065("06:00-06:30", "period6065"),
    SLOT_6570("06:30-07:00", "period6570"),
    SLOT_7075("07:00-07:30", "period7075"),
    SLOT_7580("07:30-08:00", "period7580"),
    SLOT_8085("08:00-08:30", "period8085"),
    SLOT_8590("08:30-09:00", "period8590"),
    SLOT_9095("09:00-09:30", "period9095"),
    SLOT_95100("09:30-10:00", "period95100"),
    SLOT_100105("10:00-10:30", "period100105"),
    SLOT_105110("10:30-11:00", "period105110"),
    SLOT_110115("11:00-11:30", "period110115"),
    SLOT_115120("11:30-12:00", "period115120"),

    // 12:00-24:00
    SLOT_120125("12:00-12:30", "period120125"),
    SLOT_125130("12:30-13:00", "period125130"),
    SLOT_130135("13:00-13:30", "period130135"),
    SLOT_135140("13:30-14:00", "period135140"),
    SLOT_140145("14:00-14:30", "period140145"),
    SLOT_145150("14:30-15:00", "period145150"),
    SLOT_150155("15:00-15:30", "period150155"),
    SLOT_155160("15:30-16:00", "period155160"),
    SLOT_160165("16:00-16:30", "period160165"),
    SLOT_165170("16:30-17:00", "period165170"),
    SLOT_170175("17:00-17:30", "period170175"),
    SLOT_175180("17:30-18:00", "period175180"),
    SLOT_180185("18:00-18:30", "period180185"),
    SLOT_185190("18:30-19:00", "period185190"),
    SLOT_190195("19:00-19:30", "period190195"),
    SLOT_195200("19:30-20:00", "period195200"),
    SLOT_200205("20:00-20:30", "period200205"),
    SLOT_205210("20:30-21:00", "period205210"),
    SLOT_210215("21:00-21:30", "period210215"),
    SLOT_215220("21:30-22:00", "period215220"),
    SLOT_220225("22:00-22:30", "period220225"),
    SLOT_225230("22:30-23:00", "period225230"),
    SLOT_230235("23:00-23:30", "period230235"),
    SLOT_235240("23:30-24:00", "period235240");

    private final String timeRange; // 时间区间（如"08:00-08:30"）
    private final String fieldName; // 对应字段名（如"period8085"）

    TimeSlotEnum(String timeRange, String fieldName) {
        this.timeRange  = timeRange;
        this.fieldName  = fieldName;
    }

    public String getTimeRange() {
        return timeRange;
    }

    public String getFieldName() {
        return fieldName;
    }

    /**
     * 根据时间区间字符串获取枚举实例
     */
    public static TimeSlotEnum fromTimeRange(String timeRange) {
        for (TimeSlotEnum slot : values()) {
            if (slot.timeRange.equals(timeRange))  {
                return slot;
            }
        }
        return null;
    }
}
