package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.repository.entity.CiesSpecialIndicatorsEntity;
import com.bcels.cies.repository.mapper.CiesSpecialIndicatorsMapper;
import com.bcels.cies.repository.mapper.DcpChannelConfigMapper;
import com.bcels.cies.request.CiesPointAndIndicatorRequest;
import com.bcels.cies.request.CiesSpecialIndicatorsListRequest;
import com.bcels.cies.request.CiesSpecialIndicatorsUpdateRequest;
import com.bcels.cies.response.CiesSpecialIndicatorsResponse;
import com.bcels.cies.service.ICiesSpecialIndicatorsService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 特殊指标 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Service
public class CiesSpecialIndicatorsServiceImpl extends ServiceImpl<CiesSpecialIndicatorsMapper, CiesSpecialIndicatorsEntity> implements ICiesSpecialIndicatorsService {

    @Autowired
    private CiesSpecialIndicatorsMapper ciesSpecialIndicatorsMapper;

    @Autowired
    private DcpChannelConfigMapper dcpChannelConfigMapper;
    @Override
    public PageResponse<CiesSpecialIndicatorsResponse> findSpecialIndicatorsForPage(CiesSpecialIndicatorsListRequest request) {
        Page<CiesSpecialIndicatorsResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesSpecialIndicatorsResponse> pageResult = ciesSpecialIndicatorsMapper.findSpecialIndicatorsForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }

    @Override
    public void updateSpecialIndicator(CiesSpecialIndicatorsUpdateRequest request) {
        // 默认数据名称是指标名称
        String dataName = request.getIndicatorName();
        String channelName = null;
        String equipName = dcpChannelConfigMapper.findEquipById(request.getEquipId());
        String relateIndicatorName;
        CiesPointAndIndicatorRequest request1 = new CiesPointAndIndicatorRequest();
        // 测点数据名称需要拼接通道-设备-测点
        if (StringUtils.isNotEmpty(request.getRelateDataType()) && "测点".equals(request.getRelateDataType())) {
            request1.setTestPointId(request.getRelateIndicatorId());
            String testPointId = request.getRelateIndicatorId().substring(request.getRelateIndicatorId().lastIndexOf("-") + 1);
            relateIndicatorName = dcpChannelConfigMapper.findTestPointById(testPointId).getRelateIndicatorName();
            channelName = dcpChannelConfigMapper.findChannelById(request.getCId());
            dataName = String.format("%s-%s-%s",
                    channelName,
                    equipName,
                    relateIndicatorName);
        }else if ("一次计算指标".equals(request.getRelateDataType())){
            request1.setOneIndicatorId(request.getRelateIndicatorId());
            relateIndicatorName = dcpChannelConfigMapper.findIndicatorForOne(request1).get(0).getRelateIndicatorName();
            dataName = relateIndicatorName;
        }else{
            request1.setTwoIndicatorId(request.getRelateIndicatorId());
            relateIndicatorName = dcpChannelConfigMapper.findIndicatorForTwo(request1).get(0).getRelateIndicatorName();
            dataName = relateIndicatorName;
        }
        LambdaUpdateWrapper<CiesSpecialIndicatorsEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesSpecialIndicatorsEntity::getSpecialIndicatorsId, request.getSpecialIndicatorsId())
                .set(CiesSpecialIndicatorsEntity::getRelateDataType, request.getRelateDataType())
                .set(CiesSpecialIndicatorsEntity::getChannelName, channelName)
                .set(CiesSpecialIndicatorsEntity::getCId, request.getCId())
                .set(CiesSpecialIndicatorsEntity::getEquipName, equipName)
                .set(CiesSpecialIndicatorsEntity::getEquipId, request.getEquipId())
                .set(CiesSpecialIndicatorsEntity::getRelateIndicatorName, relateIndicatorName)
                .set(CiesSpecialIndicatorsEntity::getRelateIndicatorId, request.getRelateIndicatorId())
                .set(CiesSpecialIndicatorsEntity::getIndicatorName, request.getIndicatorName())
                .set(CiesSpecialIndicatorsEntity::getDataName, dataName)
                .set(CiesSpecialIndicatorsEntity::getUpdateBy, CiesUserContext.getCurrentUser())
                .set(CiesSpecialIndicatorsEntity::getUpdateTime, LocalDateTime.now());
        ciesSpecialIndicatorsMapper.update(null, wrapper);
    }

    @Override
    public CiesSpecialIndicatorsResponse findSpecialIndicatorById(String specialIndicatorsId) {
       return ciesSpecialIndicatorsMapper.findSpecialIndicatorsById(specialIndicatorsId);
    }

    @Override
    public void deleteIndicatorsById(CiesSpecialIndicatorsUpdateRequest request) {
        LambdaUpdateWrapper<CiesSpecialIndicatorsEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesSpecialIndicatorsEntity::getSpecialIndicatorsId, request.getSpecialIndicatorsId())
                .set(CiesSpecialIndicatorsEntity::getDr, YesOrNo.YES.getCode())
                .set(CiesSpecialIndicatorsEntity::getUpdateBy, CiesUserContext.getCurrentUser())
                .set(CiesSpecialIndicatorsEntity::getUpdateTime, LocalDateTime.now());
        ciesSpecialIndicatorsMapper.update(null, wrapper);
    }

    @Override
    public CiesSpecialIndicatorsResponse findSpecialIndicator(CiesSpecialIndicatorsUpdateRequest request) {
        QueryWrapper<CiesSpecialIndicatorsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(request.getIndicatorName()),"indicator_name",request.getIndicatorName());
        wrapper.eq(StringUtils.isNotEmpty(request.getProjectId()),"project_id",request.getProjectId());
        CiesSpecialIndicatorsEntity entity = getOne(wrapper);
        if (ObjectUtils.isEmpty(entity)){
            return null;
        }
        return BeanCopyUtil.copyProperties(entity,CiesSpecialIndicatorsResponse::new);
    }

    @Override
    public List<CiesSpecialIndicatorsResponse> findSpecialIndicatorList(List<String> indicatorName, String projectId) {
        QueryWrapper<CiesSpecialIndicatorsEntity> wrapper = new QueryWrapper<>();
        wrapper.in(!CollectionUtils.isEmpty(indicatorName), "indicator_name", indicatorName);
        wrapper.eq("project_id", projectId);
        List<CiesSpecialIndicatorsEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return BeanCopyUtil.copyListProperties(list, CiesSpecialIndicatorsResponse::new);
    }

    @Override
    public List<CiesSpecialIndicatorsResponse> findSpecialIndicators() {
        QueryWrapper<CiesSpecialIndicatorsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("dr", YesOrNo.NO.getCode());
        List<CiesSpecialIndicatorsEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyListProperties(list,CiesSpecialIndicatorsResponse::new);
    }

    @Override
    public void updateCurrentData(CiesSpecialIndicatorsEntity entity) {
        LambdaUpdateWrapper<CiesSpecialIndicatorsEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesSpecialIndicatorsEntity::getRelateIndicatorId, entity.getRelateIndicatorId())
                .set(CiesSpecialIndicatorsEntity::getCurrentData,entity.getCurrentData())
                .set(CiesSpecialIndicatorsEntity::getUpdateBy, entity.getUpdateBy())
                .set(CiesSpecialIndicatorsEntity::getUpdateTime, LocalDateTime.now());
        ciesSpecialIndicatorsMapper.update(null, wrapper);
    }

    @Override
    public void updateIndicatorName(CiesSpecialIndicatorsEntity entity) {
        LambdaUpdateWrapper<CiesSpecialIndicatorsEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesSpecialIndicatorsEntity::getSpecialIndicatorsId, entity.getSpecialIndicatorsId())
                .set(CiesSpecialIndicatorsEntity::getIndicatorName,entity.getIndicatorName())
                .set(CiesSpecialIndicatorsEntity::getDataName,entity.getDataName())
                .set(CiesSpecialIndicatorsEntity::getDr,entity.getDr())
                .set(CiesSpecialIndicatorsEntity::getUpdateBy, entity.getUpdateBy())
                .set(CiesSpecialIndicatorsEntity::getUpdateTime, LocalDateTime.now());
        ciesSpecialIndicatorsMapper.update(null, wrapper);
    }

    @Override
    public List<CiesSpecialIndicatorsResponse> findConnSpecialIndicators(String connPointId) {
        QueryWrapper<CiesSpecialIndicatorsEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("dr", YesOrNo.NO.getCode());
        wrapper.eq("connection_point_id", connPointId);
        List<CiesSpecialIndicatorsEntity> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyListProperties(list,CiesSpecialIndicatorsResponse::new);
    }
}
