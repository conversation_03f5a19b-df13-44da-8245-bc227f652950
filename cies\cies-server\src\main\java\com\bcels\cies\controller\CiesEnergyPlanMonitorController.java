package com.bcels.cies.controller;

import com.bcels.cies.api.CiesEnergyPlanMonitorApi;
import com.bcels.cies.domain.CiesEnergyPlanMonitorDomainService;
import com.bcels.cies.request.CiesEnergyPlanMonitorRequest;
import com.bcels.cies.response.CiesPlanMonitorListResponse;
import com.bcels.cies.response.CiesPlanMonitorViewResponse;
import com.zwy.common.utils.bean.ResultData;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/planMonitor/v1")
public class CiesEnergyPlanMonitorController implements CiesEnergyPlanMonitorApi {

     @Autowired
     private CiesEnergyPlanMonitorDomainService ciesEnergyPlanMonitorDomainService;

    @Override
    @PostMapping("query")
    public ResultData<CiesPlanMonitorViewResponse> findForPage(@RequestBody CiesEnergyPlanMonitorRequest request) {
        return ResultData.success(ciesEnergyPlanMonitorDomainService.findPowerBrokenLine(request));
    }

    @Override
    @PostMapping("export")
    public ResultData<Void> export(@RequestBody CiesEnergyPlanMonitorRequest request, HttpServletResponse response) throws IOException {
        ciesEnergyPlanMonitorDomainService.export(request, response);
        return ResultData.success();
    }
}
