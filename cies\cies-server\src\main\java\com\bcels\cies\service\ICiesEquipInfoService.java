package com.bcels.cies.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.repository.entity.CiesEquipInfoEntity;
import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.bcels.cies.response.CiesEquipTreeResponse;
import com.zwy.common.utils.bean.PageResponse;

import java.util.List;

/**
 * <p>
 * 设备信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface ICiesEquipInfoService extends IService<CiesEquipInfoEntity> {


    PageResponse<CiesEquipInfoResponse> findEquipInfoForPage(CiesEquipInfoRequest request);

    void updateEquipInfo(CiesEquipInfoRequest request);

    CiesEquipInfoResponse findEquipInfoById(String equipId);

    List<CiesEquipTreeResponse> findEquipByProjectId(String projectId);

    List<CiesEquipInfoResponse> findParentEquip(CiesEquipInfoRequest request);

    void updateEquip(CiesEquipInfoEntity entity);
}
