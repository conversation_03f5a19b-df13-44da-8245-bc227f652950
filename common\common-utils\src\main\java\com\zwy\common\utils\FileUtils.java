package com.zwy.common.utils;

import com.zwy.common.utils.exception.CommonException;
import lombok.extern.slf4j.Slf4j;

import java.io.*;


@Slf4j
public class FileUtils {

    public static byte[] readFile(File file) {
        byte[] buffer = null;
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             FileInputStream fis = new FileInputStream(file)) {
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            buffer = bos.toByteArray();
            if (file.exists()) {
                file.delete();
            }
        } catch (IOException e) {
            throw new CommonException(e);
        }
        return buffer;
    }

    public static void write2File(byte[] byteArray, String filePath) {
        File file = new File(filePath);
        String path = filePath.substring(0, filePath.lastIndexOf("/"));
        if (!file.exists()) {
            new File(path).mkdirs();
        }
        try (InputStream in = new ByteArrayInputStream(byteArray);
             FileOutputStream fos = new FileOutputStream(file)) {
            int len = 0;
            byte[] buf = new byte[1024];
            while ((len = in.read(buf)) != -1) {
                fos.write(buf, 0, len);
            }
            fos.flush();
        } catch (Exception e) {
            throw new CommonException(e);
        }
    }

    public static String getExtension(String filename) {
        if (filename == null) {
            return null;
        } else {
            int index = indexOfExtension(filename);
            return index == -1 ? "" : filename.substring(index + 1);
        }
    }

    public static String removeExtension(String filename) {
        if (filename == null) {
            return null;
        } else {
            int index = indexOfExtension(filename);
            return index == -1 ? filename : filename.substring(0, index);
        }
    }

    public static int indexOfLastSeparator(String filename) {
        if (filename == null) {
            return -1;
        } else {
            int lastUnixPos = filename.lastIndexOf(47);
            int lastWindowsPos = filename.lastIndexOf(92);
            return Math.max(lastUnixPos, lastWindowsPos);
        }
    }

    public static int indexOfExtension(String filename) {
        if (filename == null) {
            return -1;
        } else {
            int extensionPos = filename.lastIndexOf(46);
            int lastSeparator = indexOfLastSeparator(filename);
            return lastSeparator > extensionPos ? -1 : extensionPos;
        }
    }
}
