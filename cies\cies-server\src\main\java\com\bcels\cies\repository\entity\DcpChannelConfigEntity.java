package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 通道配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Getter
@Setter
@TableName("dcp_channel_config")
@ApiModel(value = "DcpChannelConfigEntity对象", description = "通道配置")
public class DcpChannelConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("通道ID")
    @TableId
    private String cId;

    @ApiModelProperty("通道编码")
    private Integer cCode;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("解析方式类型")
    private String parsingType;

    @ApiModelProperty("下发主题")
    private String issueTopic;

    @ApiModelProperty("订阅主题")
    private String subscribeTopic;

    @ApiModelProperty("协议类型")
    private String protocolType;

    @ApiModelProperty("端口号")
    private Integer portNumber;

    @ApiModelProperty("通道类型")
    private String channelType;

    @ApiModelProperty("通道状态")
    private Byte channelStatus;

    @ApiModelProperty("项目ID")
    private String pId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
