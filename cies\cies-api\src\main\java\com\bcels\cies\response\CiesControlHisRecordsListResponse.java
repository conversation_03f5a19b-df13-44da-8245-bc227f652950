package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "场站控制列表")
public class CiesControlHisRecordsListResponse implements Serializable {

    @Schema(description = "历史记录ID")
    private String historyRecordId;

    @Schema(description = "指令下发类型")
    private String commandType;

    @Schema(description = "控制模式")
    private String controlMode;

    @Schema(description = "控制状态")
    private String controlStatus;

    @Schema(description = "指令功率(kw)")
    private BigDecimal commandPower;

    @Schema(description = "计划时间")
    private String planDate;

    @Schema(description = "时间维度")
    private String timeDimension;

    @Schema(description = "下发规则")
    private String dispatchRule;

    @Schema(description = "是否支持手动控制")
    private String manualControlEnabled;

    @Schema(description = "当日覆盖规则")
    private String dailyOverrideRule;

    @Schema(description = "是否调整")
    private Integer isAdjusted;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;
}
