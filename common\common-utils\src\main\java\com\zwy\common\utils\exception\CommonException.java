package com.zwy.common.utils.exception;

import com.zwy.common.utils.bean.ErrorCode;
import lombok.Getter;

/**
 * @Author: J.T.
 * @Date: 2021/10/12 11:18
 * @Version 1.0
 * 业务异常
 */
public class CommonException extends RuntimeException{

    @Getter
    private ErrorCode errorCode;

    public CommonException() {
        super();
    }

    public CommonException(String message) {
        super(message);
    }

    public CommonException(String message, Throwable cause) {
        super(message, cause);
    }

    public CommonException(Throwable cause) {
        super(cause);
    }

    public CommonException(ErrorCode errorCode) {
        super();
        this.errorCode = errorCode;
    }

    public CommonException(String message, ErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public CommonException(String message, ErrorCode errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public CommonException(Throwable cause, ErrorCode errorCode) {
        super(cause);
        this.errorCode = errorCode;
    }


}
