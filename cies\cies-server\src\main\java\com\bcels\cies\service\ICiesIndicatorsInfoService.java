package com.bcels.cies.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.repository.entity.CiesIndicatorsInfoEntity;
import com.bcels.cies.request.CiesIndicatorHisRequest;
import com.bcels.cies.request.CiesIndicatorsInfoListRequest;
import com.bcels.cies.request.CiesIndicatorsInfoUpdateRequest;
import com.bcels.cies.request.CiesSynIndicatorRequest;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.zwy.common.utils.bean.PageResponse;

import java.util.List;

/**
 * <p>
 * 测点与指标信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public interface ICiesIndicatorsInfoService extends IService<CiesIndicatorsInfoEntity> {

     PageResponse<CiesIndicatorsInfoResponse> findIndicatorsForPage(CiesIndicatorsInfoListRequest request);

    void updateIndicator(CiesIndicatorsInfoUpdateRequest request);

    CiesIndicatorsInfoResponse findIndicatorById(String indicatorId);


    List<CiesIndicatorsInfoResponse> findIndicatorsByEquipId(String equipId);

    List<CiesIndicatorsInfoResponse> findIndicators(String projectId);

    void updateSynIndicator(CiesSynIndicatorRequest request);

    void updateCurrentData(CiesIndicatorsInfoEntity entity);
}
