package com.bcels.cies.controller;

import com.bcels.cies.api.CiesEssMonthlyBillApi;
import com.bcels.cies.domain.CiesEssMonthlyBillDomainService;
import com.bcels.cies.request.CiesEssMonthlyBilCalRequest;
import com.bcels.cies.request.CiesEssMonthlyBillIncomeRequest;
import com.bcels.cies.request.CiesEssMonthlyBillRequest;
import com.bcels.cies.response.CiesEssMonthlyBillResponse;
import com.bcels.cies.response.CiesSettlementBillDetailResponse;
import com.bcels.cies.request.CiesMobileIncomeRequest;
import com.bcels.cies.response.CiesMobileIncomeResponse;
import com.bcels.cies.task.Task;
import com.zwy.common.utils.annotation.Dict;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/ciesEssMonthlyBill/v1")
public class CiesEssMonthlyBillController implements CiesEssMonthlyBillApi {
    @Autowired
    private CiesEssMonthlyBillDomainService ciesEssMonthlyBillService;

    @Autowired
    private Task task;

    @Override
    @PostMapping("page")
    @Dict(code = "")
    public ResultData<PageResponse<CiesEssMonthlyBillResponse>> findForPage(@RequestBody CiesEssMonthlyBillRequest request) {
        return ResultData.success(ciesEssMonthlyBillService.findForPage(request));
    }


    @PostMapping("save")
    public ResultData<Void> save(@RequestBody CiesEssMonthlyBillRequest request) {
        ciesEssMonthlyBillService.save(request);
        return ResultData.success();
    }

    @PostMapping("update")
    public ResultData<Void> update(@RequestBody CiesEssMonthlyBillRequest request) {
        ciesEssMonthlyBillService.update(request);
        return ResultData.success();
    }

    @PostMapping("reviewUpdate")
    public ResultData<Void> reviewUpdate(@RequestBody CiesEssMonthlyBillRequest request) {
        ciesEssMonthlyBillService.reviewUpdate(request);
        return ResultData.success();
    }

    @PostMapping("historySettlementPage")
    @Dict(code = "")
    public ResultData<PageResponse<CiesEssMonthlyBillResponse>> historySettlementPage(@RequestBody CiesEssMonthlyBillRequest request) {
        return ResultData.success(ciesEssMonthlyBillService.historySettlementPage(request));
    }

    @Override
    @GetMapping("findById")
    public ResultData<CiesSettlementBillDetailResponse> findById(@RequestParam("essBillId") String essBillId) {
        return ResultData.success(ciesEssMonthlyBillService.findById(essBillId));
    }

    @Override
    @PostMapping("generateDeep")
    public ResultData<Void> generateDeep(@RequestBody CiesEssMonthlyBillRequest request) {
        ciesEssMonthlyBillService.generateDeepDetail(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("reCalculate")
    public ResultData<Void> reCalculate(@RequestBody CiesEssMonthlyBillIncomeRequest request) {
        ciesEssMonthlyBillService.reCalculate(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("submit")
    public ResultData<Void> submit(@RequestBody CiesEssMonthlyBillIncomeRequest request) {
        ciesEssMonthlyBillService.submit(request);
        return ResultData.success();
    }

    @Override
    @PostMapping("upload")
    public ResultData<String> upload(@RequestPart("file") MultipartFile file){
        return ResultData.success(ciesEssMonthlyBillService.upload(file));
    }

    @Override
    @GetMapping("downloadFile")
    public ResultData<Void> downloadFile(@RequestParam("fileName") String fileName,HttpServletResponse response){
        ciesEssMonthlyBillService.downloadFile(fileName,response);
        return ResultData.success();
    }

    @GetMapping("deleteFile")
    public ResultData<Void> deleteFile(@RequestParam("fileName") String fileName){
        ciesEssMonthlyBillService.deleteFile(fileName);
        return ResultData.success();
    }

    @GetMapping("findByBillId")
    public ResultData<CiesSettlementBillDetailResponse> findByBillId(@RequestParam("essBillId") String essBillId) {
        return ResultData.success(ciesEssMonthlyBillService.findByBillId(essBillId));
    }

    @PostMapping("statisticIncome")
    public ResultData<CiesMobileIncomeResponse> statisticIncome(@RequestBody CiesMobileIncomeRequest request) {
        return ResultData.success(ciesEssMonthlyBillService.statisticIncome(request));
    }

    @GetMapping("appealSettle")
    public ResultData<Void> appealSettle(@RequestParam("essBillId") String essBillId) {
        ciesEssMonthlyBillService.appealSettle(essBillId);
        return ResultData.success();
    }

    @GetMapping("task")
    public ResultData<Void> task(){
        task.generateSettlementBill();
        return ResultData.success();
    }
}
