package com.bcels.cies.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "用户代购电-电价列表")
public class CiesMarketElecPriRequest implements Serializable {

    @Schema(description = "电价主键")
    private String elecPriId;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "月份")
    private String month;

    @Schema(description = "尖峰电量电价")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal peakPeriodPrice;

    @Schema(description = "高峰电量电价")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal highPeriodPrice;

    @Schema(description = "平时电量电价")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal flatPeriodPrice;

    @Schema(description = "低谷电量电价")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal valleyPeriodPrice;

    @Schema(description = "深谷电量电价")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal deepValleyPeriodPrice;

    @Schema(description = "需量电价(元/千瓦·月)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal demandPrice;

    @Schema(description = "容量电价(元/千伏安·月)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal capacityPrice;

    @Schema(description = "项目id")
    private String projectId;
}
