package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Schema(description = "场站储能计划保存")
public class CiesStationPlanRequest implements Serializable {

    @Schema(description = "计划开始日期")
    private String planStartTime;

    @Schema(description = "计划结束日期")
    private String planEndTime;

    @Schema(description = "计划日期")
    private String planDate;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "时间维度")
    private String timeDimension;

    @Schema(description = "指令下发类型")
    private String commandType;

    @Schema(description = "下发规则")
    private String dispatchRule;

    @Schema(description = "是否支持手动控制")
    private String manualControlEnabled;

    @Schema(description = "当日覆盖规则")
    private String dailyOverrideRule;

    @Schema(description = "储能计划明细")
    private List<CiesEnergyPlanRequest> plan;
}
