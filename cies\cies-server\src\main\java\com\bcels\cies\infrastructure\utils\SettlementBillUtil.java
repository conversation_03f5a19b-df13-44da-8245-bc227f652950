package com.bcels.cies.infrastructure.utils;

import com.bcels.cies.emuns.ElectricityPriceTypeEnum;
import com.bcels.cies.response.CiesPeakValleyPriceGapResponse;
import com.bcels.cies.response.CiesSettlementBillGenerateResponse;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.zwy.common.utils.exception.BusinessException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SettlementBillUtil {

    // 时段列表
    private static final List<String> TIME_PERIODS = List.of(" 尖", "峰", "平", "谷", "深谷");

    public static byte[] generateBillExcel(CiesSettlementBillGenerateResponse bill) {
        byte[] excelBytes;
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(" 储能项目结算单");

            // 设置列宽
            setupColumnWidths(sheet);

            // 创建单元格样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle noteStyle = createNoteStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle mergedHeaderStyle = createMergedHeaderStyle(workbook);

            // 生成标题行
            createTitleRows(sheet, titleStyle, headerStyle, noteStyle, bill);

            // 生成数据表格
            int dataRow = createDataTable(sheet, dataStyle, mergedHeaderStyle, bill);

            // 生成底部汇总行
            createSummaryRows(sheet, dataStyle, headerStyle, dataRow, bill);

            // 输出到文件
            try (ByteArrayOutputStream fileOut = new ByteArrayOutputStream()) {
                workbook.write(fileOut);
                excelBytes = fileOut.toByteArray();
            }
        } catch (IOException e) {
           throw new BusinessException("生成结算单excel文件失败",e);
        }
        return excelBytes;
    }

    /**
     * 将 Excel 字节数组转为 PDF 字节数组
     */
    public static byte[] convertExcelToPdfBytes(byte[] excelBytes) throws Exception {
        ByteArrayOutputStream pdfOut = new ByteArrayOutputStream();
        Document pdfDoc = new Document(PageSize.A4.rotate());

        try (Workbook workbook = new XSSFWorkbook(new ByteArrayInputStream(excelBytes))) {
            PdfWriter.getInstance(pdfDoc, pdfOut);
            pdfDoc.open();

            // 遍历所有 Sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                PdfPTable pdfTable = new PdfPTable(sheet.getRow(0).getLastCellNum());

                // 填充表格数据
                for (Row row : sheet) {
                    for (Cell cell : row) {
                        pdfTable.addCell(getCellValueAsString(cell));
                    }
                }

                pdfDoc.add(pdfTable);
                pdfDoc.newPage();
            }
        } finally {
            pdfDoc.close();
        }
        return pdfOut.toByteArray();
    }

    /**
     * 安全获取单元格值的字符串表示
     */
    public static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return new DataFormatter().formatCellValue(cell); // 自动处理日期/数字格式
            case BOOLEAN:
                return cell.getBooleanCellValue() ? "TRUE" : "FALSE";
            case FORMULA:
                return evaluateFormulaCell(cell);
            case BLANK:
                return "";
            case ERROR:
                return "#ERROR";
            default:
                return "";
        }
    }

    /**
     * 解析公式单元格的值
     */
    private static String evaluateFormulaCell(Cell cell) {
        try {
            Workbook workbook = cell.getSheet().getWorkbook();
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            CellValue cellValue = evaluator.evaluate(cell);
            switch (cellValue.getCellType()) {
                case STRING:
                    return cellValue.getStringValue();
                case NUMERIC:
                    return new DataFormatter().formatCellValue(cell, evaluator);
                default:
                    return "";
            }
        } catch (Exception e) {
            return "#FORMULA_ERROR";
        }
    }

    /**
     * 设置列宽
     */
    private static void setupColumnWidths(Sheet sheet) {
        // 调整列宽，根据内容设置合适的宽度
        sheet.setColumnWidth(0, 3000);  // 并网点
        sheet.setColumnWidth(1, 3000);  // 统计阶段
        sheet.setColumnWidth(2, 1500);  // 时段
        for (int i = 3; i <= 12; i++) {  // 数据列
            sheet.setColumnWidth(i, 2200);
        }
    }

    /**
     * 创建标题行
     */
    private static void createTitleRows(Sheet sheet, CellStyle titleStyle, CellStyle headerStyle, CellStyle noteStyle, CiesSettlementBillGenerateResponse bill) {
        // 标题行
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(bill.getBillTitle());
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));

        // 项目名称行
        Row projectRow = sheet.createRow(1);
        Cell projectCell = projectRow.createCell(0);
        projectCell.setCellValue(" 项目名称");
        projectCell.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 3));

        Cell projectValueCell = projectRow.createCell(4);
        projectValueCell.setCellValue(bill.getProjectName());
        projectValueCell.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 4, 12));

        // 备注行
        Row noteRow1 = sheet.createRow(2);
        Cell noteCell1 = noteRow1.createCell(0);
        noteCell1.setCellValue(bill.getRemark());
        noteCell1.setCellStyle(noteStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 4, 0, 12));
    }

    /**
     * 创建数据表格
     */
    private static int createDataTable(Sheet sheet, CellStyle dataStyle, CellStyle mergedHeaderStyle, CiesSettlementBillGenerateResponse bill) {
        int startRow = 5;
        String templateType = bill.getTemplateType();

        // 创建表头
        createTableHeaders(sheet, startRow, mergedHeaderStyle);
        // 创建数据行
        int currentRow = startRow + 2;
        // 记录峰谷价差总收益起始行
        int beginRow = startRow + 2;

        Map<String, List<CiesPeakValleyPriceGapResponse>> groupedMap = bill.getBillList().stream()
                .collect(Collectors.groupingBy(
                        CiesPeakValleyPriceGapResponse::getConnectionPointName
                ));

        // 动态生成数据
        for (Map.Entry<String, List<CiesPeakValleyPriceGapResponse>> entry : groupedMap.entrySet()) {
            String connectionPointName = entry.getKey();
            List<CiesPeakValleyPriceGapResponse> dataList = entry.getValue();

            // 生成单个并网点数据，并返回实际占用的行数
            currentRow = generateGridConnectionData(sheet, currentRow, connectionPointName, dataStyle, dataList, templateType);
        }
        // 生成峰谷价差收益数据
        Row summaryRow = sheet.createRow(currentRow);

        Cell cell = summaryRow.createCell(12);
        cell.setCellStyle(dataStyle);
        cell.setCellValue(bill.getPeakValleyRevenue() != null ? bill.getPeakValleyRevenue().doubleValue() : BigDecimal.ZERO.doubleValue());
        sheet.addMergedRegion(new CellRangeAddress(beginRow, currentRow, 12, 12));
        return currentRow;
    }

    /**
     * 创建表格表头
     */
    private static void createTableHeaders(Sheet sheet, int startRow, CellStyle mergedHeaderStyle) {
        // 第一行表头（合并行）
        Row headerRow1 = sheet.createRow(startRow);
        // 第二行表头（仅用于"抄见电量"和"结算电量"的单位）
        Row headerRow2 = sheet.createRow(startRow + 1);

        // 表头内容定义
        String[] headers1 = {"并网点", "统计阶段", "时段", "上月读数", "本月读数", "倍率",
                "抄见电量", "结算电量", "电价(元/kwh)", "金额 (元)", "分项合计 (元)", "峰谷价差总收益(元)"};
        String[] units = {"", "", "", "", "", "",
                "(kwh)", "(kwh)", "", "", "", ""}; // 仅填充需要单位的列

        // 填充第一行（所有列）
        for (int i = 0; i < headers1.length; i++) {
            Cell cell = headerRow1.createCell(i);
            cell.setCellValue(headers1[i]);
            cell.setCellStyle(mergedHeaderStyle);
        }

        // 填充第二行（仅"抄见电量"和"结算电量"的单位）
        for (int i = 0; i < units.length; i++) {
            if (!units[i].isEmpty()) {
                Cell cell = headerRow2.createCell(i);
                cell.setCellValue(units[i]);
                cell.setCellStyle(mergedHeaderStyle);
            }
        }

        // 合并除"抄见电量"和"结算电量"外的所有列
        for (int i = 0; i < headers1.length; i++) {
            if (units[i].isEmpty()) {
                // 合并两行（从startRow到startRow+1）
                sheet.addMergedRegion(new CellRangeAddress(startRow, startRow + 1, i, i));
            }
        }
    }

    /**
     * 生成并网点数据
     */
    private static int generateGridConnectionData(Sheet sheet, int startRow, String connectionName, CellStyle dataStyle, List<CiesPeakValleyPriceGapResponse> billList, String templateType) {
        int currentRow = startRow;

        Map<String, List<CiesPeakValleyPriceGapResponse>> groupedByStatPeriod = billList.stream()
                .collect(Collectors.groupingBy(
                        item -> {
                            // 根据业务规则匹配充电/放电类型
                            if ("充电（正向有功）".equals(item.getStatPeriod())) {
                                return "充电";
                            } else if ("放电（反向有功）".equals(item.getStatPeriod())) {
                                return "放电";
                            }
                            return "其他"; // 处理可能的异常值
                        }
                ));
        // 充电（正向有功）数据
        List<CiesPeakValleyPriceGapResponse> chargingData = groupedByStatPeriod.get("充电");
        List<CiesPeakValleyPriceGapResponse> sortedChargingData = chargingData.stream()
                .sorted(Comparator.comparingInt(
                        item -> TIME_PERIODS.indexOf(item.getTimeRange())  // 通过顺序列表定位优先级
                ))
                .collect(Collectors.toList());
        currentRow = generateDirectionData(sheet, currentRow, connectionName, "充电(正向有功)",
                sortedChargingData, dataStyle, templateType);

        // 放电（反向有功）数据
        List<CiesPeakValleyPriceGapResponse> dischargingData = groupedByStatPeriod.get("放电");
        List<CiesPeakValleyPriceGapResponse> sortedDischargingData = dischargingData.stream()
                .sorted(Comparator.comparingInt(
                        item -> TIME_PERIODS.indexOf(item.getTimeRange())  // 通过顺序列表定位优先级
                ))
                .collect(Collectors.toList());


        currentRow = generateDirectionData(sheet, currentRow, connectionName, "放电(反向有功)",
                sortedDischargingData, dataStyle, templateType);
        return currentRow;
    }

    /**
     * 生成充放电方向数据
     */
    private static int generateDirectionData(Sheet sheet, int startRow, String connectionName,
                                             String direction, List<CiesPeakValleyPriceGapResponse> sortedChargingData, CellStyle dataStyle, String templateType) {
        int currentRow = startRow;

        // 创建方向合并单元格
        Row directionRow = sheet.createRow(currentRow);
        for (int i = 0; i < 12; i++) {
            Cell cell = directionRow.createCell(i);
            cell.setCellStyle(dataStyle);
            if (i == 0) {
                cell.setCellValue(connectionName);
            } else if (i == 1) {
                cell.setCellValue(direction);
            }
        }
        List<String> timePeriods;
        // 如果是非深谷
        if (ElectricityPriceTypeEnum.STANDARD_NO_DEEP_VALLEY.getCode().equals(templateType)) {
            timePeriods = TIME_PERIODS.stream()
                    .filter(p -> !"深谷".equals(p))
                    .collect(Collectors.toList());  // 生成新集合
        } else {
            timePeriods = TIME_PERIODS;
        }
        // 填充各时段数据
        for (int i = 0; i < timePeriods.size(); i++) {
            Row dataRow = sheet.createRow(currentRow);
            CiesPeakValleyPriceGapResponse priceGapResponse = sortedChargingData.get(i);

            for (int j = 0; j < 12; j++) {
                Cell cell = dataRow.createCell(j);
                cell.setCellStyle(dataStyle);

                switch (j) {
                    case 0: // 并网点
                        if (i == 0) cell.setCellValue(connectionName);
                        break;
                    case 1: // 统计阶段
                        if (i == 0) cell.setCellValue(direction);
                        break;
                    case 2: // 时段
                        cell.setCellValue(timePeriods.get(i));
                        break;
                    case 3: // 上月读数
                        cell.setCellValue(priceGapResponse.getLastMonthRead() != null ? priceGapResponse.getLastMonthRead().doubleValue() : BigDecimal.ZERO.doubleValue());
                        break;
                    case 4: // 本月读数
                        cell.setCellValue(priceGapResponse.getCurrentMonthRead() != null ? priceGapResponse.getCurrentMonthRead().doubleValue() : BigDecimal.ZERO.doubleValue());
                        break;
                    case 5: // 倍率
                        cell.setCellValue(priceGapResponse.getMeterMultiplier() != null ? priceGapResponse.getMeterMultiplier().doubleValue() : BigDecimal.ZERO.doubleValue());
                        break;
                    case 6: // 抄见电量
                        cell.setCellValue(priceGapResponse.getMeteredUsage() != null ? priceGapResponse.getMeteredUsage().doubleValue() : BigDecimal.ZERO.doubleValue());
                        break;
                    case 7: // 结算电量
                        cell.setCellValue(priceGapResponse.getBilledUsage() != null ? priceGapResponse.getBilledUsage().doubleValue() : BigDecimal.ZERO.doubleValue());
                        break;
                    case 8: // 电价
                        cell.setCellValue(priceGapResponse.getUnitPrice() != null ? priceGapResponse.getUnitPrice().doubleValue() : BigDecimal.ZERO.doubleValue());
                        break;
                    case 9: // 金额
                        cell.setCellValue(priceGapResponse.getAmount() != null ? priceGapResponse.getAmount().doubleValue() : BigDecimal.ZERO.doubleValue());
                        break;
                    case 10: // 分项合计
                        if (i == timePeriods.size() - 1) {
                            cell.setCellValue(priceGapResponse.getTotalAmount() != null ? priceGapResponse.getTotalAmount().doubleValue() : BigDecimal.ZERO.doubleValue());
                            sheet.addMergedRegion(new CellRangeAddress(startRow, currentRow, 11, 11));
                        }
                        break;
                }

                // 设置数字类型
                if (j > 3 && j <= 12) {
                    cell.setCellType(CellType.NUMERIC);
                }
            }
            currentRow++;
        }

        return currentRow;
    }

    /**
     * 生成底部汇总行
     */
    private static void createSummaryRows(Sheet sheet, CellStyle dataStyle, CellStyle headerStyle, int dataRow, CiesSettlementBillGenerateResponse bill) {
        int startRow = dataRow + 1; // 根据前面数据行数调整
        if (ElectricityPriceTypeEnum.WITH_TAX_DIFF_AND_DEEP_VALLEY.getCode().equals(bill.getTemplateType())) {
            for (int j = 0; j < 8; j++) {
                Row row = sheet.createRow(dataRow);
                switch (j) {
                    case 0:// 甲方分成比例标题
                        Cell cell = row.createCell(j);
                        cell.setCellStyle(headerStyle);
                        cell.setCellValue("甲方分成比例");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 0, 1));
                        break;
                    case 1: // 甲方分成比例数值
                        Cell cell1 = row.createCell(2);
                        cell1.setCellStyle(headerStyle);
                        cell1.setCellValue(bill.getPartyARatio() + "%");
                        break;
                    case 2: // 甲方分成金额（元）标题
                        Cell cell2 = row.createCell(3);
                        cell2.setCellStyle(headerStyle);
                        cell2.setCellValue("甲方分成金额（元）");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 3, 4));
                        break;
                    case 3: // 甲方分成金额（元）数值
                        Cell cell3 = row.createCell(5);
                        cell3.setCellStyle(headerStyle);
                        cell3.setCellValue(bill.getPartyAAmount() != null ? bill.getPartyAAmount().doubleValue() : BigDecimal.ZERO.doubleValue());
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 5, 6));
                        break;
                    case 4: // // 税差计算比例标题
                        Cell cell4 = row.createCell(7);
                        cell4.setCellStyle(headerStyle);
                        cell4.setCellValue("税差计算比例");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 7, 8));
                        break;
                    case 5: // 税差计算比例数值
                        Cell cell5 = row.createCell(9);
                        cell5.setCellStyle(headerStyle);
                        cell5.setCellValue(bill.getTaxAdjustmentRate() + "%");
                        break;
                    case 6: // 甲方收益（元）标题
                        Cell cell6 = row.createCell(10);
                        cell6.setCellStyle(headerStyle);
                        cell6.setCellValue("甲方收益（元）");
                        break;
                    case 7: // 甲方收益（元）数值
                        Cell cell7 = row.createCell(11);
                        cell7.setCellStyle(headerStyle);
                        cell7.setCellValue(bill.getPartyAIncome() != null ? bill.getPartyAIncome().doubleValue() : BigDecimal.ZERO.doubleValue());
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 11, 12));
                        break;
                }
            }
            startRow++;
            // 含税需要统计两行数据
            for (int k = 0; k < 8; k++) {
                Row row = sheet.createRow(startRow);
                switch (k) {
                    case 0:// 乙方分成比例标题
                        Cell cell = row.createCell(k);
                        cell.setCellStyle(headerStyle);
                        cell.setCellValue("乙方分成比例");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 0, 1));
                        break;
                    case 1: // 乙方分成比例数值
                        Cell cell1 = row.createCell(2);
                        cell1.setCellStyle(headerStyle);
                        cell1.setCellValue(bill.getPartyBRatio() + "%");
                        break;
                    case 2: // 乙方分成金额（元）标题
                        Cell cell2 = row.createCell(3);
                        cell2.setCellStyle(headerStyle);
                        cell2.setCellValue("乙方分成金额（元）");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 3, 4));
                        break;
                    case 3: // 乙方分成金额（元）数值
                        Cell cell3 = row.createCell(5);
                        cell3.setCellStyle(headerStyle);
                        cell3.setCellValue(bill.getPartyBAmount() != null ? bill.getPartyBAmount().doubleValue() : BigDecimal.ZERO.doubleValue());
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 5, 6));
                        break;
                    case 4: // // 税差金额（元）标题
                        Cell cell4 = row.createCell(7);
                        cell4.setCellStyle(headerStyle);
                        cell4.setCellValue("税差金额（元）");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 7, 8));
                        break;
                    case 5: // 税差金额（元）数值
                        Cell cell5 = row.createCell(9);
                        cell5.setCellStyle(headerStyle);
                        cell5.setCellValue(bill.getTaxAdjustmentAmount() + "%");
                        break;
                    case 6: // 乙方收益（元）标题
                        Cell cell6 = row.createCell(10);
                        cell6.setCellStyle(headerStyle);
                        cell6.setCellValue("乙方收益（元）");
                        break;
                    case 7: // 乙方收益（元）数值
                        Cell cell7 = row.createCell(11);
                        cell7.setCellStyle(headerStyle);
                        cell7.setCellValue(bill.getPartyBIncome() != null ? bill.getPartyBIncome().doubleValue() : BigDecimal.ZERO.doubleValue());
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 11, 12));
                        break;
                }
            }
        } else {
            for (int j = 0; j < 8; j++) {
                Row row = sheet.createRow(dataRow);
                switch (j) {
                    case 0:// 甲方分成比例标题
                        Cell cell = row.createCell(j);
                        cell.setCellStyle(headerStyle);
                        cell.setCellValue("甲方分成比例");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 0, 1));
                        break;
                    case 1: // 甲方分成比例数值
                        Cell cell1 = row.createCell(2);
                        cell1.setCellStyle(headerStyle);
                        cell1.setCellValue(bill.getPartyARatio() + "%");
                        break;
                    case 2: // 甲方收益（元）标题
                        Cell cell2 = row.createCell(3);
                        cell2.setCellStyle(headerStyle);
                        cell2.setCellValue("甲方收益（元）");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 3, 4));
                        break;
                    case 3: // 甲方收益（元）数值
                        Cell cell3 = row.createCell(5);
                        cell3.setCellStyle(headerStyle);
                        cell3.setCellValue(bill.getPartyAIncome() != null ? bill.getPartyAIncome().doubleValue() : BigDecimal.ZERO.doubleValue());
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 5, 6));
                        break;
                    case 4: // // 乙方分成比例标题
                        Cell cell4 = row.createCell(7);
                        cell4.setCellStyle(headerStyle);
                        cell4.setCellValue("乙方分成比例");
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 7, 8));
                        break;
                    case 5: // 乙方分成比例数值
                        Cell cell5 = row.createCell(9);
                        cell5.setCellStyle(headerStyle);
                        cell5.setCellValue(bill.getPartyBRatio() + "%");
                        break;
                    case 6: // 甲方收益（元）标题
                        Cell cell6 = row.createCell(10);
                        cell6.setCellStyle(headerStyle);
                        cell6.setCellValue("乙方收益（元）");
                        break;
                    case 7: // 乙方收益（元）数值
                        Cell cell7 = row.createCell(11);
                        cell7.setCellStyle(headerStyle);
                        cell7.setCellValue(bill.getPartyBIncome() != null ? bill.getPartyBIncome().doubleValue() : BigDecimal.ZERO.doubleValue());
                        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 11, 12));
                        break;
                }
            }
        }
        startRow++;

        // 备注行
        Row noteRow = sheet.createRow(startRow);
        Cell noteCell = noteRow.createCell(0);
        noteCell.setCellValue("( 以上乙方收益为乙方应从甲方收取的含税收益，乙方提供6%增值税发票)");
        noteCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 0, 12));
        startRow++;

        for (int j = 0; j < 4; j++) {
            Row row = sheet.createRow(startRow);
            switch (j) {
                case 0:
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(dataStyle);
                    cell.setCellValue("甲方");
                    sheet.addMergedRegion(new CellRangeAddress(startRow, startRow + 1, 0, 1));
                    break;
                case 1: // 甲方公司
                    Cell cell1 = row.createCell(2);
                    cell1.setCellStyle(dataStyle);
                    cell1.setCellValue(bill.getPartyAName());
                    sheet.addMergedRegion(new CellRangeAddress(startRow, startRow + 1, 2, 6));

                    break;
                case 2:
                    Cell cell2 = row.createCell(7);
                    cell2.setCellStyle(dataStyle);
                    cell2.setCellValue("乙方");
                    sheet.addMergedRegion(new CellRangeAddress(startRow, startRow + 1, 7, 8));
                    break;
                case 3: // 乙方公司
                    Cell cell3 = row.createCell(9);
                    cell3.setCellStyle(dataStyle);
                    cell3.setCellValue(bill.getPartyBName());
                    sheet.addMergedRegion(new CellRangeAddress(startRow, startRow + 1, 9, 12));
                    break;
            }
        }
        startRow = startRow + 2;

        // 日期行
        Row dateRow = sheet.createRow(startRow);
        Cell dateCell1 = dateRow.createCell(0);
        dateCell1.setCellValue(" 日期");
        dateCell1.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow, 0, 1));


        Cell cell2 = dateRow.createCell(7);
        cell2.setCellStyle(dataStyle);
        cell2.setCellValue("日期");
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow + 1, 7, 8));

        Cell cell3 = dateRow.createCell(9);
        cell3.setCellStyle(dataStyle);
        cell3.setCellValue(bill.getGenerateDate());
        sheet.addMergedRegion(new CellRangeAddress(startRow, startRow + 1, 9, 12));
    }

    /**
     * 创建标题样式
     */
    private static CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 16);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        return style;
    }

    /**
     * 创建表头样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 1. 字体设置（原代码基础上补充）
        Font font = workbook.createFont();
        font.setBold(true);  // 加粗
        font.setFontHeightInPoints((short) 11); // 字号
        font.setFontName(" 宋体"); // 补充字体名称
        style.setFont(font);

        // 2. 对齐方式（新增）
        style.setAlignment(HorizontalAlignment.CENTER);  // 水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中
        style.setWrapText(true);  // 自动换行

        // 3. 边框样式（新增）
        style.setBorderTop(BorderStyle.THIN);  // 上边框
        style.setBorderBottom(BorderStyle.THIN);  // 下边框
        style.setBorderLeft(BorderStyle.THIN);  // 左边框
        style.setBorderRight(BorderStyle.THIN);  // 右边框

        // 4. 背景色（可选新增）
        style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());  // 背景色
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);  // 填充模式

        return style;
    }

    private static CellStyle createNoteStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName(" 宋体");
        font.setFontHeightInPoints((short) 9); // 小字号
        font.setColor(IndexedColors.GREY_50_PERCENT.getIndex());  // 灰色字体
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);  // 左对齐
        style.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中
        style.setBorderBottom(BorderStyle.THIN);  // 细边框
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        return style;
    }

    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName(" 宋体");
        font.setFontHeightInPoints((short) 11); // 标准字号
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.RIGHT);  // 数值右对齐
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        // 设置数字格式（保留两位小数）
        style.setDataFormat(workbook.createDataFormat().getFormat("0.00"));
        return style;
    }

    private static CellStyle createMergedHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName(" 黑体");
        font.setFontHeightInPoints((short) 12); // 标题字号
        font.setBold(true);  // 加粗
        font.setColor(IndexedColors.WHITE.getIndex());  // 白色字体
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);  // 居中对齐
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置背景色（深蓝色）
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.MEDIUM);  // 粗边框
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);
        style.setBorderTop(BorderStyle.MEDIUM);
        return style;
    }
}
