package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesEnterpriseInfoEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.request.CiesEnterpriseInfoRequest;
import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.response.CiesEnterpriseInfoResponse;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.zwy.common.utils.bean.PageResponse;

import java.util.List;

/**
 * <p>
 * 企业信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface ICiesEnterpriseInfoService extends IService<CiesEnterpriseInfoEntity> {


    PageResponse<CiesEnterpriseInfoResponse> findEnterpriseForPage(CiesEnterpriseInfoRequest request);

    void updateEnterpriseInfo(CiesEnterpriseInfoRequest request);

    void addEnterpriseInfo(CiesEnterpriseInfoRequest request);

    List<CiesEnterpriseInfoResponse> findEnterprise(String enterpriseName);

    List<CiesEnterpriseInfoResponse> findEnergyEnterprise(String projectType,String enterpriseName);
    
}
