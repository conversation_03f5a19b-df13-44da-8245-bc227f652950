package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.client.CiesInternalInterfaceClient;
import com.bcels.cies.emuns.ProjectTypeEnum;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.repository.entity.CiesProjectInfoEntity;
import com.bcels.cies.repository.mapper.CiesProjectInfoMapper;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.request.CiesQueryIndicatorsRequest;
import com.bcels.cies.response.*;
import com.bcels.cies.service.ICiesEssMonthlyBillService;
import com.bcels.cies.service.ICiesProjectInfoService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.ConvertUtils;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CiesProjectInfoServiceImpl extends ServiceImpl<CiesProjectInfoMapper, CiesProjectInfoEntity> implements ICiesProjectInfoService {

    @Autowired
    private CiesProjectInfoMapper ciesProjectInfoMapper;
    @Autowired
    private ICiesEssMonthlyBillService iCiesEssMonthlyBillService;
    @Autowired
    private CiesInternalInterfaceClient ciesInternalInterfaceClient;

    /**
     * 按条件查询项目列表
     * @param request
     * @return
     */
    @Override
    public PageResponse<CiesProjectInfoResponse> findProjectInfoForPage(CiesProjectInfoRequest request) {
        Page<CiesProjectInfoResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesProjectInfoResponse> pageResult = ciesProjectInfoMapper.findProjectInfoForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }

    @Override
    public PageResponse<CiesProjectInfoResponse> findMarKetForPage(CiesProjectInfoRequest request) {
        Page<CiesProjectInfoResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesProjectInfoResponse> pageResult = ciesProjectInfoMapper.findMarKetForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }

    @Override
    public void updateProjectInfo(CiesProjectInfoRequest request) {
        CiesProjectInfoEntity ciesProjectInfoEntity = BeanCopyUtil.copyProperties(request, CiesProjectInfoEntity::new);
        ciesProjectInfoEntity.setUpdateTime(LocalDateTime.now());
        ciesProjectInfoEntity.setUpdateBy(CiesUserContext.getCurrentUser());
        ciesProjectInfoEntity.setDr(YesOrNo.NO.getCode());
        this.updateById(ciesProjectInfoEntity);
    }

    @Override
    public CiesProjectInfoResponse findEnterpriseByProjectId(String projectId) {
        return ciesProjectInfoMapper.findEnterpriseByProjectId(projectId);
    }

    @Override
    public List<CiesProjectInfoResponse> findProjectByEnterpriseId(CiesProjectInfoRequest request) {
        QueryWrapper<CiesProjectInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(request.getEnterpriseId()),"enterprise_id",request.getEnterpriseId());
        queryWrapper.eq(StringUtils.isNotBlank(request.getProjectType()),"project_type",request.getProjectType());
        queryWrapper.eq("dr",YesOrNo.NO.getCode());
        List<CiesProjectInfoEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyListProperties(list,CiesProjectInfoResponse::new);
    }


    @Override
    public void updateBaseProject(CiesProjectInfoEntity entity) {
        LambdaUpdateWrapper<CiesProjectInfoEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesProjectInfoEntity::getProjectId,  entity.getProjectId())
                .set(CiesProjectInfoEntity::getProName, entity.getProName())
                .set(CiesProjectInfoEntity::getProjectType, entity.getProjectType())
                .set(CiesProjectInfoEntity::getTenantCode, entity.getTenantCode())
                .set(CiesProjectInfoEntity::getUpdateBy, entity.getUpdateBy())
                .set(CiesProjectInfoEntity::getUpdateTime, LocalDateTime.now())
                .set(CiesProjectInfoEntity::getCreateBy, entity.getCreateBy())
                .set(CiesProjectInfoEntity::getCreateTime, entity.getCreateTime());
        ciesProjectInfoMapper.update(null,  wrapper);
    }

    /**
     * 按条件检索用户代购电
     * @param request
     * @return
     */
    @Override
    public PageResponse<CiesProjectInfoResponse> findMarketProjectInfoForPage(CiesMarketConfigRequest request) {
        Page<CiesProjectInfoResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesProjectInfoResponse> pageResult = ciesProjectInfoMapper.findMarketProjectInfoForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }


    /**
     * 查询项目（不带分页）
     * @param projectId
     * @return
     */
    @Override
    public List<CiesProjectInfoResponse> findProjectInfo(String projectId) {
        return ciesProjectInfoMapper.findProjectInfo(projectId);
    }

    @Override
    public CiesProjectInfoResponse findProjectInfoById(String projectId) {
        CiesProjectInfoEntity ciesProjectInfoEntity = ciesProjectInfoMapper.selectById(projectId);
        return BeanCopyUtil.copyProperties(ciesProjectInfoEntity,CiesProjectInfoResponse::new);
    }

    @Override
    public CiesStatisticsProjectResponse statisticsProject() {
        CiesStatisticsProjectResponse result = new CiesStatisticsProjectResponse();
        CiesStatisticsProjectResponse response = ciesProjectInfoMapper.queryByEnergyStorageType();
        if (!ObjectUtils.isEmpty(response)){
            BeanUtils.copyProperties(response, result);
            result.setConstructingTotalCapacity(result.getConstructingTotalCapacity().divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP));
            result.setOperatingTotalCapacity(result.getOperatingTotalCapacity().divide(new BigDecimal("1000"), 4, RoundingMode.HALF_UP));
            result.setTotalCapacity(result.getOperatingTotalCapacity().add(result.getConstructingTotalCapacity()));

            result.setTotalProjectCount(result.getConstructingProjectCount()+result.getOperatingProjectCount());
        }
        CiesStatisticsProjectResponse response1 = ciesProjectInfoMapper.queryByProvince();
        if (!ObjectUtils.isEmpty(response1)){
            result.setCoveredProvinceCount(response1.getCoveredProvinceCount());
        }
        return result;
    }

    @Override
    public List<CiesProjectInfoResponse> findAllProject() {
        QueryWrapper<CiesProjectInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_type", ProjectTypeEnum.ENERGY_STORAGE.getCode());
        queryWrapper.eq("dr",YesOrNo.NO.getCode());
        List<CiesProjectInfoEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyListProperties(list,CiesProjectInfoResponse::new);
    }

    @Override
    public Map<String, Object> getProvinceProject(CiesProjectInfoRequest request) {
        LambdaQueryWrapper<CiesProjectInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StringUtils.isNotBlank(request.getProvince()), CiesProjectInfoEntity::getProvince, request.getProvince());
        wrapper.eq(StringUtils.isNotBlank(request.getProjectId()), CiesProjectInfoEntity::getProjectId, request.getProjectId());

        // Fetch project data
        List<CiesProjectInfoEntity> ciesProjectInfoEntities = ciesProjectInfoMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(ciesProjectInfoEntities)){
            return null;
        }
        // Map to response class
//        List<CiesProjectInfoResponse> ciesProjectInfoResponses = ConvertUtils.sourceToTarget(ciesProjectInfoEntities, CiesProjectInfoResponse.class);

        // Calculate project statistics
        BigDecimal runningCount = new BigDecimal(ciesProjectInfoEntities.stream().filter(project -> "1".equals(project.getProjectStatus())).count());
        BigDecimal totalCount = new BigDecimal(ciesProjectInfoEntities.size());
        BigDecimal stoppedCount = totalCount.subtract(runningCount);

        // Percentage calculations
        BigDecimal runningPercentage = runningCount.multiply(new BigDecimal("100")).divide(totalCount, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal stoppedPercentage = stoppedCount.multiply(new BigDecimal("100")).divide(totalCount, 2, BigDecimal.ROUND_HALF_UP);

        // Power and capacity calculations
        BigDecimal totalPower = ciesProjectInfoEntities.stream()
                .map(entity -> entity.getRatedPower() != null ? entity.getRatedPower() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalCapacity = ciesProjectInfoEntities.stream()
                .map(entity -> entity.getRatedCapacity() != null ? entity.getRatedCapacity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Get income data
        List<String> projectIds = ciesProjectInfoEntities.stream().map(CiesProjectInfoEntity::getProjectId).collect(Collectors.toList());
        CiesIncomResponse income = ciesProjectInfoMapper.getIncome(projectIds);

        List<CiesEssMonthlyBillResponse> essMonthlyBill = iCiesEssMonthlyBillService.getEssMonthlyBill(projectIds, Arrays.asList("APPEALED", "CONFIRMED", "ISSUED"));

        // Prepare response
        CiesStatisticsProjectInfoResponse ciesStatisticsProjectInfoResponse = new CiesStatisticsProjectInfoResponse();
        ciesStatisticsProjectInfoResponse.setTotalRatedPower(totalPower);
        ciesStatisticsProjectInfoResponse.setTotalRatedCapacity(totalCapacity);
        ciesStatisticsProjectInfoResponse.setRunningCount(runningCount);
        ciesStatisticsProjectInfoResponse.setStoppedCount(stoppedCount);
        ciesStatisticsProjectInfoResponse.setTotalCount(totalCount);
        ciesStatisticsProjectInfoResponse.setRunningPercentage(runningPercentage);
        ciesStatisticsProjectInfoResponse.setStoppedPercentage(stoppedPercentage);

        if(income!=null){
            // Income details
            ciesStatisticsProjectInfoResponse.setLastMonthPartyAIncome(income.getLastMonthPartyAIncome());
            ciesStatisticsProjectInfoResponse.setLastMonthPartyBIncome(income.getLastMonthPartyBIncome());
            ciesStatisticsProjectInfoResponse.setLastMonthPeakValleyRevenue(income.getLastMonthPeakValleyRevenue());
            ciesStatisticsProjectInfoResponse.setYearToDatePartyAIncome(income.getYearToDatePartyAIncome());
            ciesStatisticsProjectInfoResponse.setYearToDatePartyBIncome(income.getYearToDatePartyBIncome());
            ciesStatisticsProjectInfoResponse.setYearToDatePeakValleyRevenue(income.getYearToDatePeakValleyRevenue());
            ciesStatisticsProjectInfoResponse.setTotalPartyAIncome(income.getTotalPartyAIncome());
            ciesStatisticsProjectInfoResponse.setTotalPartyBIncome(income.getTotalPartyBIncome());
            ciesStatisticsProjectInfoResponse.setTotalPeakValleyRevenue(income.getTotalPeakValleyRevenue());
        }

//        Map<String, List<CiesProjectInfoResponse>> collect =
//                ciesProjectInfoResponses.stream()
//                        .filter(r -> r.getProvince() != null)
//                        .collect(Collectors.groupingBy(CiesProjectInfoResponse::getProvince));

        // Return the grouped data and the calculated statistics
        Map<String, Object> result = new HashMap<>();
        result.put("statistics", ciesStatisticsProjectInfoResponse);
//        result.put("projectsByProvince", collect);
        result.put("settlement",essMonthlyBill);
        return result;
    }

    @Override
    public CiesProjectInfoResponse getPowerCurve(CiesProjectInfoRequest request) {
        List<CiesProjectInfoResponse> powerCurve = ciesProjectInfoMapper.getPowerCurve(request);
        if(CollectionUtils.isEmpty(powerCurve)) {
            return null;
        }
        //todo 查询历史数据
        List<String> specialIds = powerCurve.stream()
                .filter(item -> {
                    String name = item.getIndicatorName();
                    return name != null &&
                            (name.contains("工厂总有功功率") || name.contains("储能站有功功率"));
                })
                .map(CiesProjectInfoResponse::getSpecialIndicatorsId)
                .collect(Collectors.toList());
        Map<String, List<CiesHisIndicatorsDataResponse>> stringListMap = new HashMap<>();
        if (!specialIds.isEmpty()) {
            specialIds.add("67f20fbb8e0473c2a8660e51ef9d0ac1");
            CiesQueryIndicatorsRequest ciesQueryIndicatorsRequest = new CiesQueryIndicatorsRequest();
            ciesQueryIndicatorsRequest.setDataType("指标数据");
            ciesQueryIndicatorsRequest.setStartTime(request.getStartTime());
            ciesQueryIndicatorsRequest.setEndTime(request.getEndTime());
            ciesQueryIndicatorsRequest.setIndicatorIds(specialIds);
            stringListMap = ciesInternalInterfaceClient.batchQueryHisData(ciesQueryIndicatorsRequest);
        }

        // ===== 行转列 =====
        CiesProjectInfoResponse merged = new CiesProjectInfoResponse();
        // 基础字段取第一条
        CiesProjectInfoResponse first = powerCurve.get(0);
        merged.setRatedCapacity(first.getRatedCapacity());
        merged.setRatedPower(first.getRatedPower());
        merged.setProjectStatus(first.getProjectStatus());

        Map<String, List<CiesHisIndicatorsDataResponse>> hisIndicatorsMap = new HashMap<>();
        for (CiesProjectInfoResponse row : powerCurve) {
            String name = row.getIndicatorName();
            String valueWithUnit = row.getCurrentData()==null?"-":row.getCurrentData()+"";

            switch (name) {
                case "充放电状态":
                    merged.setChargeStatus(valueWithUnit);
                    break;
                case "储能站SOC":
                    merged.setSoc(valueWithUnit+"%");
                    break;
                case "储能站有功功率":
                    merged.setActivePower(valueWithUnit+"kw");
                    break;
                default:
                    break;
            }

            // === 把历史数据也映射到 指标名称 ===
            String id = row.getSpecialIndicatorsId();
            if (id != null && stringListMap.containsKey(id)) {
                hisIndicatorsMap.put(name, stringListMap.get(id));
            }
        }
        merged.setCiesHisIndicatorsData(hisIndicatorsMap);

        return merged;
    }


    @Override
    public List<CiesProjectInfoResponse> getSettlementStatement(CiesProjectInfoRequest request) {

        // 根据条件筛选出数据
        List<CiesProjectInfoResponse> settlementStatement = ciesProjectInfoMapper.getSettlementStatement(request);

        //todo 这里看前端需要返回秒,还是返回日期。
      /*  for (CiesProjectInfoResponse resp : settlementStatement) {
            Date disputeDate = resp.getDisputeDate(); // 截止时间
            if (disputeDate != null) {
                // 当前时间
                Date now = new Date();
                // 计算时间差并格式化为 年月日时分秒
                String remainTime = DateUtil.formatBetween(
                        now,
                        disputeDate,
                        BetweenFormatter.Level.SECOND // 显示到秒
                );
                resp.setRemainingTime(remainTime);
            } else {
                resp.setRemainingTime("无截止日期");
            }
        }*/
        return settlementStatement;
    }

    @Override
    public List<CiesProjectInfoResponse> getProjectInfo() {
        LambdaQueryWrapper<CiesProjectInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CiesProjectInfoEntity::getEnergyStorageType,Arrays.asList("OPERATIONAL","SUSPENDED_OPERATION"));
        List<CiesProjectInfoEntity> ciesProjectInfoEntities = ciesProjectInfoMapper.selectList(queryWrapper);
        List<CiesProjectInfoResponse> ciesProjectInfoResponses = ConvertUtils.sourceToTarget(ciesProjectInfoEntities, CiesProjectInfoResponse.class);
        return ciesProjectInfoResponses;
    }

    @Override
    public void verificationStatement(CiesProjectInfoRequest request) {
        request.setSettlementMonth(StringUtils.isNotBlank(request.getSettlementMonth())?request.getSettlementMonth().replaceAll("/","-"):null);
        Integer i = ciesProjectInfoMapper.verificationStatement(request);
        if(i>0){
            throw new IllegalArgumentException("选择的月份已有结算单，请重新选择月份");
        }
    }

}
