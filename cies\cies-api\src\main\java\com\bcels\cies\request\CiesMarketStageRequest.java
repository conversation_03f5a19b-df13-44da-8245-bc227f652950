package com.bcels.cies.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "用户代购电-分时阶段列表")
public class CiesMarketStageRequest implements Serializable {

    @Schema(description = "阶段主键")
    private String stageId;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "月份")
    private String month;

    @Schema(description = "0时-0.5时")
    private String period0005;

    @Schema(description = "0.5时-1时")
    private String period0510;

    @Schema(description = "1时-1.5时")
    private String period1015;

    @Schema(description = "1.5时-2时")
    private String period1520;

    @Schema(description = "2时-2.5时")
    private String period2025;

    @Schema(description = "2.5时-3时")
    private String period2530;

    @Schema(description = "3时-3.5时")
    private String period3035;

    @Schema(description = "3.5时-4时")
    private String period3540;

    @Schema(description = "4时-4.5时")
    private String period4045;

    @Schema(description = "4.5时-5时")
    private String period4550;

    @Schema(description = "5时-5.5时")
    private String period5055;

    @Schema(description = "5.5时-6时")
    private String period5560;

    @Schema(description = "6时-6.5时")
    private String period6065;

    @Schema(description = "6.5时-7时")
    private String period6570;

    @Schema(description = "7时-7.5时")
    private String period7075;

    @Schema(description = "7.5时-8时")
    private String period7580;

    @Schema(description = "8时-8.5时")
    private String period8085;

    @Schema(description = "8.5时-9时")
    private String period8590;

    @Schema(description = "9时-9.5时")
    private String period9095;

    @Schema(description = "9.5时-10时")
    private String period95100;

    @Schema(description = "10时-10.5时")
    private String period100105;

    @Schema(description = "10.5时-11时")
    private String period105110;

    @Schema(description = "11时-11.5时")
    private String period110115;

    @Schema(description = "11.5时-12时")
    private String period115120;

    @Schema(description = "12时-12.5时")
    private String period120125;

    @Schema(description = "12.5时-13时")
    private String period125130;

    @Schema(description = "13时-13.5时")
    private String period130135;

    @Schema(description = "13.5时-14时")
    private String period135140;

    @Schema(description = "14时-14.5时")
    private String period140145;

    @Schema(description = "14.5时-15时")
    private String period145150;

    @Schema(description = "15时-15.5时")
    private String period150155;

    @Schema(description = "15.5时-16时")
    private String period155160;

    @Schema(description = "16时-16.5时")
    private String period160165;

    @Schema(description = "16.5时-17时")
    private String period165170;

    @Schema(description = "17时-17.5时")
    private String period170175;

    @Schema(description = "17.5时-18时")
    private String period175180;

    @Schema(description = "18时-18.5时")
    private String period180185;

    @Schema(description = "18.5时-19时")
    private String period185190;

    @Schema(description = "19时-19.5时")
    private String period190195;

    @Schema(description = "19.5时-20时")
    private String period195200;

    @Schema(description = "20时-20.5时")
    private String period200205;

    @Schema(description = "20.5时-21时")
    private String period205210;

    @Schema(description = "21时-21.5时")
    private String period210215;

    @Schema(description = "21.5时-22时")
    private String period215220;

    @Schema(description = "22时-22.5时")
    private String period220225;

    @Schema(description = "22.5时-23时")
    private String period225230;

    @Schema(description = "23时-23.5时")
    private String period230235;

    @Schema(description = "23.5时-24时")
    private String period235240;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;
}
