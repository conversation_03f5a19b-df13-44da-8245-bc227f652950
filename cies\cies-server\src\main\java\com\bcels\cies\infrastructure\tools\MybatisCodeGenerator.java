package com.bcels.cies.infrastructure.tools;//package com.zwy.southbound.gateway.infrastructure.tools;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.sql.Types;
import java.util.Collections;


public class MybatisCodeGenerator {
//    public static String scanner(String tip) {
//        Scanner scanner = new Scanner(System.in);
//        StringBuilder help = new StringBuilder();
//        help.append("请输入" + tip + "：");
//        System.out.println(help.toString());
//        if(scanner.hasNext()) {
//            String ipt = scanner.next();
//            if(org.apache.commons.lang3.StringUtils.isNotBlank(ipt)) {
//                return ipt;
//            }
//        }
//        throw new MybatisPlusException("请输入正确的" + tip + "！");
//    }

    private static final String url = "jdbc:mysql://**************:3306/bcels-cies?useUnicode=true&useSSL=false&characterEncoding=utf8&serverTimezone=Asia/Shanghai";
//    private static final String url = "jdbc:mysql://**************:3306/bcels-dcp-sys-server?useUnicode=true&useSSL=false&characterEncoding=utf8&serverTimezone=Asia/Shanghai";

    private static final String username = "root";

    private static final String password = "Admin@123";

    public static void main(String[] args) {

        String projectPath = System.getProperty("user.dir");

//        String outputDir = projectPath + "/vpp-crm/crm-server/src/main/java";
        String outputDir = projectPath + "\\bcels-cies\\cies\\cies-server\\src\\main\\java";

        String parent = "com.bcels.cies";

        FastAutoGenerator.create(url, username, password).globalConfig(builder -> {
                    builder.author("system") // 设置作者
                            .enableSwagger() // 开启 swagger 模式
//                            .fileOverride() // 覆盖已生成文件
                            .outputDir(outputDir); // 指定输出目录
                }).dataSourceConfig(builder -> builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                    int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                    if(typeCode == Types.SMALLINT) {
                        // 自定义类型转换
                        return DbColumnType.INTEGER;
                    }
                    return typeRegistry.getColumnType(metaInfo);

                })).packageConfig(builder -> {
                    builder.parent(parent) // 设置父包名
                            .entity("repository.entity")
                            .controller("controller")
                            .mapper("repository.mapper")
                            .service("service")
                            .serviceImpl("service.impl")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, projectPath+"/bcels-cies/cies/cies-server/src/main/java/com/bcels/cies/repository/mapper/")); // 设置mapperXml生成路径
                }).strategyConfig(builder -> {
//                    builder.addInclude(scanner("表名，多个英文逗号分割").split(",")) // 设置需要生成的表名
                    builder.addInclude("cies_valley_config_details") // 设置需要生成的表名
                            .entityBuilder()
                            .columnNaming(NamingStrategy.underline_to_camel)
                            .naming(NamingStrategy.underline_to_camel)
                            .enableLombok()
                            .formatFileName("%sEntity")
                            .mapperBuilder().enableMapperAnnotation(); // 设置过滤表前缀
                }).templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
