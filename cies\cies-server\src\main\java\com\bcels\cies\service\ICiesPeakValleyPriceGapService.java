package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesPeakValleyPriceGapEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.request.CiesPeakValleyPriceGapRequest;
import com.bcels.cies.response.CiesPeakValleyPriceGapResponse;

import java.util.List;

/**
 * <p>
 * 储能峰谷价差信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface ICiesPeakValleyPriceGapService extends IService<CiesPeakValleyPriceGapEntity> {

    List<CiesPeakValleyPriceGapResponse> queryPriceGap(CiesPeakValleyPriceGapRequest request);

    void deleteByBillId(String billId);

}
