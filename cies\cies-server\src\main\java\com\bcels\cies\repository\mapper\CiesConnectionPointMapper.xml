<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesConnectionPointMapper">

    <select id="findConnByProjectIds" resultType="com.bcels.cies.response.CiesConnectionPointResponse">
        select point.*,pro.project_id as projectId
        from cies_connection_point point
        left join cies_project_info pro on pro.project_id = point.project_id
        where point.dr = 0 and point.history_record_id is null
        <if test="projectIds != null and projectIds.size()> 0">
            AND pro.project_id IN
            <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
