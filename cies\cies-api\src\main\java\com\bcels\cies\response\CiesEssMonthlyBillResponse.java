package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zwy.common.utils.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "设备信息")
public class CiesEssMonthlyBillResponse implements Serializable {

    @Schema(description = "企业")
    private String enterpriseName;

    @Schema(description = "企业ID")
    private String enterpriseId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "储能结算主键")
    private String essBillId;

    @Schema(description = "结算月份")
    private String settlementMonth;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "峰谷价差总收益（元）")
    private BigDecimal peakValleyRevenue;

    @Schema(description = "甲方收益（元）")
    private BigDecimal partyAIncome;

    @Schema(description = "乙方收益（元）")
    private BigDecimal partyBIncome;

    @Dict(code = "SETTLEMENT_BILL_STATUS")
    @Schema(description = "结算状态")
    private String settlementStatus;

    @Dict(code = "BILLING_METHOD")
    @Schema(description = "开具方式")
    private String issueMethod;

    @Schema(description = "说明")
    private String remark;

    @Schema(description = "申诉原因")
    private String disputeReason;

    @Schema(description = "申诉到期时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime disputeCountdown;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "用户结算附件地址")
    private String userAttachmentUrl;

    @Schema(description = "登记结算单PDF附件地址")
    private String pdfAttachmentUrl;

    @Schema(description = "登记结算单Excel/Word附件地址")
    private String officeAttachmentUrl;

    @Schema(description = "是否单独计算深谷")
    private Integer isCalDeep;

    @Schema(description = "甲方分成比例(%)")
    private BigDecimal partyARatio;

    @Schema(description = "乙方分成比例(%)")
    private BigDecimal partyBRatio;

    @Schema(description = "甲方分成金额(元)")
    private BigDecimal partyAAmount;

    @Schema(description = "乙方分成金额(元)")
    private BigDecimal partyBAmount;

    @Schema(description = "税差计算比例")
    private BigDecimal taxAdjustmentRate;

    @Schema(description = "税差金额(元)")
    private BigDecimal taxAdjustmentAmount;

    @Schema(description = "审核人")
    private String reviewer;

    @Schema(description = "审核时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;

    @Schema(description = "结算单模板")
    @Dict(code = "ELECTRICITY_PRICE_TYPE")
    private String templateType;

    @Schema(description = "结算单项目名称")
    private String settlementProjectName;

}
