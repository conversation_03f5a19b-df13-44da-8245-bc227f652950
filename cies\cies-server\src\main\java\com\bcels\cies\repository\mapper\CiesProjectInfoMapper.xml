<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesProjectInfoMapper">
    <select id="findEnterpriseByProjectId" resultType="com.bcels.cies.response.CiesProjectInfoResponse">
        select pro.pro_name as proName,
        en.enterprise_name as enterpriseName
        from cies_project_info as pro
        inner join cies_enterprise_info as en on en.enterprise_id = pro.enterprise_id
        where pro.project_id = #{projectId}
    </select>
    <select id="findProjectInfoForPage" resultType="com.bcels.cies.response.CiesProjectInfoResponse">
        SELECT
        pro.project_id,
        pro.pro_name,
        dict.dict_desc as projectType,
        e.enterprise_name,
        pro.project_desc
        FROM
        cies_project_info pro
        left join cies_dict dict on dict.dict_code = pro.project_type
        left join  cies_enterprise_info e ON pro.enterprise_id  = e.enterprise_id
        <where>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND pro.project_id  = #{request.projectId}
            </if>
            <if test="request.enterpriseName  != null and request.enterpriseName  != ''">
                AND e.enterprise_name  = #{request.enterpriseName}
            </if>
            <if test="request.projectType  != null and request.projectType  != ''">
                AND pro.project_type = #{request.projectType}
            </if>
        </where>
        ORDER BY
        CASE WHEN pro.project_type = 'ENERGY_STORAGE' THEN 1
        WHEN pro.project_type = 'LOAD' THEN 2
        WHEN pro.project_type = 'WIND_POWER' THEN 3
        WHEN pro.project_type = 'PHOTOVOLTAIC' THEN 4
        END ASC,pro.create_time desc,e.enterprise_name asc,pro.project_id  ASC
    </select>
    <select id="findMarketProjectInfoForPage" resultType="com.bcels.cies.response.CiesProjectInfoResponse">
        select pro.project_id,pro.pro_name,pro.elec_area,dict1.dict_desc as elecType,dict2.dict_desc as voltageLevel from cies_project_info pro
        left join cies_dict dict1 on dict1.dict_code = pro.elec_type
        left join cies_dict dict2 on dict2.dict_code = pro.voltage_level
        AND  ((pro.elec_area IN ('广东省深圳市', '西藏自治区') AND dict2.dict_type IN ('A', 'B')) OR
        (pro.elec_area NOT IN ('广东省深圳市', '西藏自治区') AND
        dict2.dict_type not IN ('A', 'B')))
        <where>
            <if test="request.projectId  != null and request.projectId  != ''">
                AND pro.project_id = #{request.projectId}
            </if>
            <if test="request.elecArea  != null and request.elecArea  != ''">
                AND pro.elec_market_name LIKE CONCAT('%', #{request.elecArea}, '%')
            </if>
            <if test="request.elecType  != null and request.elecType  != ''">
                AND pro.elecType = #{request.elecType}
            </if>
            <if test="request.voltageLevel  != null and request.voltageLevel  != ''">
                AND dict2.dict_desc LIKE CONCAT('%', #{request.voltageLevel},  '%')
            </if>
        </where>
        order by pro.elec_area,elec_type,pro.project_id asc
    </select>
    <select id="findMarKetForPage" resultType="com.bcels.cies.response.CiesProjectInfoResponse">
        SELECT pro.project_id,
               pro.pro_name,
               e.enterprise_name,
               pro.elec_area,
               '电网代购电' as marketType,
               dict1.dict_desc as elecType,
               dict2.dict_desc as voltageLevel
        FROM cies_project_info pro
                 LEFT JOIN cies_enterprise_info e ON pro.enterprise_id = e.enterprise_id
                 left join cies_dict dict1 on dict1.dict_code = pro.elec_type
                 left join   cies_dict dict2 ON
            dict2.dict_code  = pro.voltage_level
                AND  ((pro.elec_area IN ('广东省深圳市', '西藏自治区') AND dict2.dict_type IN ('A', 'B')) OR
                      (pro.elec_area NOT IN ('广东省深圳市', '西藏自治区') AND
                       dict2.dict_type not IN ('A', 'B')))
        where pro.project_type = 'ENERGY_STORAGE'
        ORDER BY e.enterprise_name asc
    </select>
    <select id="findProjectInfo" resultType="com.bcels.cies.response.CiesProjectInfoResponse">
        SELECT
                 pro.*, dict1.dict_desc as elecType,
                 dict3.dict_desc as industryType,
                 dict2.dict_desc as voltageLevel
        FROM
            cies_project_info pro
                LEFT JOIN
            cies_dict dict1 ON dict1.dict_code  = pro.elec_type
                LEFT JOIN
            cies_dict dict2 ON
                dict2.dict_code  = pro.voltage_level
                    AND  ((pro.elec_area IN ('广东省深圳市', '西藏自治区') AND dict2.dict_type IN ('A', 'B')) OR
                          (pro.elec_area NOT IN ('广东省深圳市', '西藏自治区') AND
                           dict2.dict_type not IN ('A', 'B')))
                LEFT JOIN
            cies_dict dict3 ON dict3.dict_code  = pro.industry_type
        WHERE
            pro.project_id  = #{projectId}
    </select>
    <select id="queryByEnergyStorageType" resultType="com.bcels.cies.response.CiesStatisticsProjectResponse">
        SELECT
            COUNT(CASE WHEN energy_storage_type = 'OPERATIONAL' THEN 1 END) AS operatingProjectCount,
            COALESCE(SUM(CASE WHEN energy_storage_type = 'OPERATIONAL' THEN rated_capacity ELSE 0 END), 0) AS operatingTotalCapacity,
            COUNT(CASE WHEN energy_storage_type = 'UNDER_CONSTRUCTION' THEN 1 END) AS constructingProjectCount,
            COALESCE(SUM(CASE WHEN energy_storage_type = 'UNDER_CONSTRUCTION' THEN rated_capacity ELSE 0 END), 0) AS constructingTotalCapacity         FROM cies_project_info
        WHERE project_type = 'ENERGY_STORAGE' and energy_storage_type IN ('OPERATIONAL', 'UNDER_CONSTRUCTION')
    </select>
    <select id="queryByProvince" resultType="com.bcels.cies.response.CiesStatisticsProjectResponse">
        SELECT
            COUNT(DISTINCT JSON_UNQUOTE(JSON_EXTRACT(area_code, '$[0]'))) AS coveredProvinceCount
        FROM cies_project_info
        WHERE project_type = 'ENERGY_STORAGE' and area_code IS NOT NULL
    </select>

    <select id="getIncome" resultType="com.bcels.cies.response.CiesIncomResponse">
        SELECT
        SUM(CASE
        WHEN MONTH(cem.settlement_month) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH)
        THEN cem.party_a_income
        ELSE 0
        END) AS last_month_party_a_income,

        SUM(CASE
        WHEN MONTH(cem.settlement_month) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH)
        THEN cem.party_b_income
        ELSE 0
        END) AS last_month_party_b_income,

        SUM(CASE
        WHEN MONTH(cem.settlement_month) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH)
        THEN cem.peak_valley_revenue
        ELSE 0
        END) AS last_month_peak_valley_revenue,

        SUM(CASE
        WHEN YEAR(cem.settlement_month) = YEAR(CURRENT_DATE)
        THEN cem.party_a_income
        ELSE 0
        END) AS year_to_date_party_a_income,

        SUM(CASE
        WHEN YEAR(cem.settlement_month) = YEAR(CURRENT_DATE)
        THEN cem.party_b_income
        ELSE 0
        END) AS year_to_date_party_b_income,

        SUM(CASE
        WHEN YEAR(cem.settlement_month) = YEAR(CURRENT_DATE)
        THEN cem.peak_valley_revenue
        ELSE 0
        END) AS year_to_date_peak_valley_revenue,

        SUM(cem.party_a_income) AS total_party_a_income,
        SUM(cem.party_b_income) AS total_party_b_income,
        SUM(cem.peak_valley_revenue) AS total_peak_valley_revenue

        FROM
        cies_project_info cpi
        INNER JOIN
        cies_ess_monthly_bill cem
        ON cpi.project_id = cem.project_id
        WHERE
        cem.settlement_month &lt;= CURRENT_DATE
        AND cem.settlement_status IN ('APPEALED', 'CONFIRMED', 'ISSUED') AND cpi.dr=0 and cem.dr=0
        AND cpi.project_id IN
        <foreach item="projectId" collection="projectIds" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </select>

    <select id="getSettlementStatement" resultType="com.bcels.cies.response.CiesProjectInfoResponse">
        SELECT cpi.project_id,cpi.pro_name,cpi.project_type,
        cemb.ess_bill_id,cemb.settlement_month,cemb.settlement_status,cemb.party_a_income,cemb.party_b_income,cemb.dispute_countdown,
        cemb.peak_valley_revenue
        FROM cies_project_info cpi
        INNER JOIN cies_ess_monthly_bill cemb ON cpi.project_id = cemb.project_id
        <where>
            <if test="province != null and province != ''">
                AND cpi.province = #{province}
            </if>
            <if test="proName != null and proName != ''">
                AND cpi.pro_name = #{proName}
            </if>
            <choose>
                <when test="settlementStatus != null and settlementStatus != ''">
                    AND cemb.settlement_status = #{settlementStatus}
                </when>
                <otherwise>
                    AND cemb.settlement_status IN ('APPEALED', 'CONFIRMED', 'ISSUED')
                </otherwise>
            </choose>
            <if test="settlementMonth != null and settlementMonth != ''">
                AND cemb.settlement_month = #{settlementMonth}
            </if>
            AND cpi.dr=0 AND cemb.dr=0
        </where>
    </select>

    <select id="getPowerCurve" resultType="com.bcels.cies.response.CiesProjectInfoResponse">
        SELECT cpi.rated_capacity,cpi.rated_power,cpi.project_status,csi.special_indicators_id,csi.indicator_name,csi.current_data,csi.unit
        FROM cies_project_info cpi
        INNER JOIN cies_special_indicators csi on cpi.project_id = csi.project_id
        WHERE cpi.dr=0 and csi.dr=0
        <if test="proName != null and proName != ''">
            AND cpi.pro_name = #{proName}
        </if>
        <if test="province != null and province != ''">
            AND cpi.province = #{province}
        </if>
    </select>

    <select id="verificationStatement" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            cies_project_info cpi
                INNER JOIN cies_ess_monthly_bill cemb ON cpi.project_id = cemb.project_id
        WHERE
            cpi.project_id = #{projectId}
          AND cemb.settlement_month = #{settlementMonth}
          AND cpi.dr = 0
          AND cemb.dr =0
    </select>
</mapper>
