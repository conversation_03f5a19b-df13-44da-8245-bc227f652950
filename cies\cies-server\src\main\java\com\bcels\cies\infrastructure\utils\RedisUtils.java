package com.bcels.cies.infrastructure.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class RedisUtils {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 存储键值 + 过期时间
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key,  value, timeout, unit);
    }

    /**
     * 存储键值（永不过期）
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key,  value);
    }

    /**
     * 通过 key 获取缓存值（核心方法）
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 获取并删除键值
     */
    public Object getAndDelete(String key) {
        return redisTemplate.opsForValue().getAndDelete(key);
    }

    // ============================ 过期时间管理 ============================

    /**
     * 单独设置过期时间
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        return redisTemplate.expire(key,  timeout, unit);
    }

    /**
     * 获取剩余过期时间（秒）
     */
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }

    /**
     * 获取剩余过期时间（自定义单位）
     */
    public Long getExpire(String key, TimeUnit unit) {
        return redisTemplate.getExpire(key,  unit);
    }

    // ============================ 键管理 ============================

    /**
     * 检查 key 是否存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 删除 key
     */
    public Boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    // ============================ Hash 操作 ============================

    /**
     * 存储 Hash 键值
     */
    public void hSet(String key, String hashKey, Object value) {
        redisTemplate.opsForHash().put(key,  hashKey, value);
    }

    /**
     * 获取 Hash 值
     */
    public Object hGet(String key, String hashKey) {
        return redisTemplate.opsForHash().get(key,  hashKey);
    }

    /**
     * 删除 Hash 键
     */
    public Long hDelete(String key, String... hashKeys) {
        return redisTemplate.opsForHash().delete(key,  hashKeys);
    }
}