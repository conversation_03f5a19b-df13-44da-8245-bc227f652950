package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@TableName("cies_valley_config_details")
@ApiModel(value = "CiesValleyConfigDetailsEntity对象", description = "深谷电量配置明细表")
public class CiesValleyConfigDetailsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("配置明细主键")
    @TableId
    private String configDetailId;

    @ApiModelProperty("数据类型")
    private String dataType;

    @ApiModelProperty("深谷数据")
    private String deepData;

    @ApiModelProperty("配置主键")
    private String configId;

    @Schema(description = "并网点Id")
    private String connectionPointId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}

