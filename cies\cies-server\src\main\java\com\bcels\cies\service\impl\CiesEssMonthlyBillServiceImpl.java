package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.emuns.SettlementBillStatusEnum;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.repository.entity.CiesEssMonthlyBillEntity;
import com.bcels.cies.repository.entity.CiesOperationLogEntity;
import com.bcels.cies.repository.entity.CiesProjectInfoEntity;
import com.bcels.cies.repository.mapper.CiesEssMonthlyBillMapper;
import com.bcels.cies.repository.mapper.CiesProjectInfoMapper;
import com.bcels.cies.request.CiesEssMonthlyBillIncomeRequest;
import com.bcels.cies.request.CiesEssMonthlyBillRequest;
import com.bcels.cies.response.CiesEssMonthlyBillResponse;
import com.bcels.cies.service.ICiesEssMonthlyBillService;
import com.bcels.cies.service.ICiesOperationLogService;
import com.zwy.common.utils.ConvertUtils;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import com.zwy.common.utils.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;


@Service
public class CiesEssMonthlyBillServiceImpl extends ServiceImpl<CiesEssMonthlyBillMapper, CiesEssMonthlyBillEntity> implements ICiesEssMonthlyBillService {

    @Autowired
    private CiesEssMonthlyBillMapper ciesEssMonthlyBillMapper;
    @Autowired
    private ICiesOperationLogService iCiesOperationLogService;
    @Autowired
    private CiesProjectInfoMapper ciesProjectInfoMapper;
    @Override
    public PageResponse<CiesEssMonthlyBillResponse> findForPage(CiesEssMonthlyBillRequest request) {
        request.setSettlementStartTime(StringUtils.isNotBlank(request.getSettlementStartTime())?request.getSettlementStartTime().replaceAll("/","-"):null);
        request.setSettlementEndTime(StringUtils.isNotBlank(request.getSettlementEndTime())?request.getSettlementEndTime().replaceAll("/","-"):null);

        Page<CiesEssMonthlyBillResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesEssMonthlyBillResponse> pageResult = ciesEssMonthlyBillMapper.findForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());

    }

    @Override
    public void save(CiesEssMonthlyBillRequest request) {
        BigDecimal sum = Optional.ofNullable(request.getPartyAIncome()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(request.getPartyBIncome()).orElse(BigDecimal.ZERO));

        if (request.getPeakValleyRevenue() == null || sum.compareTo(request.getPeakValleyRevenue()) != 0) {
            throw new IllegalArgumentException("甲方乙方收益之和不等于峰谷价差总收益，请重新填写");
        }

        request.setSettlementMonth(StringUtils.isNotBlank(request.getSettlementMonth())?request.getSettlementMonth().replaceAll("/","-"):null);

        CiesEssMonthlyBillEntity ciesEssMonthlyBillEntity = ConvertUtils.sourceToTarget(request, CiesEssMonthlyBillEntity.class);
        ciesEssMonthlyBillEntity.setSettlementMonth(ciesEssMonthlyBillEntity.getSettlementMonth().replaceAll("/","-"));
        ciesEssMonthlyBillEntity.setCreateBy(CiesUserContext.getCurrentUser());
        ciesEssMonthlyBillEntity.setCreateTime(LocalDateTime.now());
        this.save(ciesEssMonthlyBillEntity);

        CiesOperationLogEntity ciesOperationLog = new CiesOperationLogEntity();
        ciesOperationLog.setEssBillId(ciesEssMonthlyBillEntity.getEssBillId());
        ciesOperationLog.setOperator(null);
        ciesOperationLog.setChangeContent("历史结算单登记");
        iCiesOperationLogService.saveOpeationLog(ciesOperationLog);
    }

    @Override
    public void update(CiesEssMonthlyBillRequest request) {
        BigDecimal sum = Optional.ofNullable(request.getPartyAIncome()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(request.getPartyBIncome()).orElse(BigDecimal.ZERO));

        if (request.getPeakValleyRevenue() == null || sum.compareTo(request.getPeakValleyRevenue()) != 0) {
            throw new IllegalArgumentException("甲方乙方收益之和不等于峰谷价差总收益，请重新填写");
        }

        request.setSettlementMonth(StringUtils.isNotBlank(request.getSettlementMonth())?request.getSettlementMonth().replaceAll("/","-"):null);

        CiesEssMonthlyBillEntity ciesEssMonthlyBillEntity = ConvertUtils.sourceToTarget(request, CiesEssMonthlyBillEntity.class);
        ciesEssMonthlyBillEntity.setSettlementMonth(ciesEssMonthlyBillEntity.getSettlementMonth().replaceAll("/","-"));

        if(StringUtils.isNotBlank(request.getEssBillId())){
            LambdaUpdateWrapper<CiesEssMonthlyBillEntity> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(CiesEssMonthlyBillEntity::getEssBillId,request.getEssBillId());
            updateWrapper.set(CiesEssMonthlyBillEntity::getStartTime,request.getStartTime());
            updateWrapper.set(CiesEssMonthlyBillEntity::getEndTime,request.getEndTime());
            updateWrapper.set(CiesEssMonthlyBillEntity::getSettlementStatus,request.getSettlementStatus());
            updateWrapper.set(CiesEssMonthlyBillEntity::getIssueMethod,request.getIssueMethod());
            updateWrapper.set(CiesEssMonthlyBillEntity::getPeakValleyRevenue,request.getPeakValleyRevenue());
            updateWrapper.set(CiesEssMonthlyBillEntity::getPartyAIncome,request.getPartyAIncome());
            updateWrapper.set(CiesEssMonthlyBillEntity::getPartyBIncome,request.getPartyBIncome());
            updateWrapper.set(CiesEssMonthlyBillEntity::getUserAttachmentUrl,request.getUserAttachmentUrl());
            updateWrapper.set(CiesEssMonthlyBillEntity::getPdfAttachmentUrl,request.getPdfAttachmentUrl());
            updateWrapper.set(CiesEssMonthlyBillEntity::getOfficeAttachmentUrl,request.getOfficeAttachmentUrl());
            updateWrapper.set(CiesEssMonthlyBillEntity::getUpdateBy, CiesUserContext.getCurrentUser());
            updateWrapper.set(CiesEssMonthlyBillEntity::getUpdateTime, LocalDateTime.now());
            ciesEssMonthlyBillMapper.update(null, updateWrapper);
        }else {
            ciesEssMonthlyBillEntity.setCreateBy(CiesUserContext.getCurrentUser());
            ciesEssMonthlyBillEntity.setCreateTime(LocalDateTime.now());
            this.save(ciesEssMonthlyBillEntity);
        }
    }

    @Override
    public void reviewUpdate(CiesEssMonthlyBillRequest request) {
        if(StringUtils.isNotBlank(request.getEssBillId())){
            if(SettlementBillStatusEnum.ISSUED.getCode().equals(request.getSettlementStatus())){
                CiesEssMonthlyBillEntity entity = ciesEssMonthlyBillMapper.selectById(request.getEssBillId());
                CiesProjectInfoEntity projectInfo = ciesProjectInfoMapper.selectById(entity.getProjectId());
                if (StringUtils.isEmpty(projectInfo.getDisputeDays())){
                    throw new BusinessException("当前结算单未配置结算单异议等待工作日，不支持提出异议");
                }
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime futureDateTime = now.plusDays(Integer.parseInt(projectInfo.getDisputeDays()));
                request.setDisputeCountdown(futureDateTime);
            }

            LambdaUpdateWrapper<CiesEssMonthlyBillEntity> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(CiesEssMonthlyBillEntity::getEssBillId,request.getEssBillId());
            updateWrapper.set(request.getDisputeCountdown()!=null,CiesEssMonthlyBillEntity::getDisputeCountdown,request.getDisputeCountdown());
            updateWrapper.set(CiesEssMonthlyBillEntity::getSettlementStatus,request.getSettlementStatus());
            ciesEssMonthlyBillMapper.update(null, updateWrapper);
        }else {
            throw new BusinessException("结算单主键不能为空！");
        }
    }

    @Override
    public PageResponse<CiesEssMonthlyBillResponse> historySettlementPage(CiesEssMonthlyBillRequest request) {
        Page<CiesEssMonthlyBillResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesEssMonthlyBillResponse> pageResult = ciesEssMonthlyBillMapper.historySettlementPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }

    @Override
    public void updateMonthlyBill(CiesEssMonthlyBillIncomeRequest request) {
        LambdaUpdateWrapper<CiesEssMonthlyBillEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesEssMonthlyBillEntity::getEssBillId, request.getEssBillId())
                .set(request.getPartyAIncome() != null, CiesEssMonthlyBillEntity::getPartyAIncome, request.getPartyAIncome())
                .set(request.getPartyBIncome() != null, CiesEssMonthlyBillEntity::getPartyBIncome, request.getPartyBIncome())
                .set(request.getPartyAAmount() != null, CiesEssMonthlyBillEntity::getPartyAAmount, request.getPartyAAmount())
                .set(request.getPartyBAmount() != null, CiesEssMonthlyBillEntity::getPartyBAmount, request.getPartyBAmount())
                .set(request.getPartyARatio() != null, CiesEssMonthlyBillEntity::getPartyARatio, request.getPartyARatio())
                .set(request.getPartyBRatio() != null, CiesEssMonthlyBillEntity::getPartyBRatio, request.getPartyBRatio())
                .set(request.getTaxAdjustmentRate() != null, CiesEssMonthlyBillEntity::getTaxAdjustmentRate, request.getTaxAdjustmentRate())
                .set(request.getPdfAttachmentUrl() != null, CiesEssMonthlyBillEntity::getPdfAttachmentUrl, request.getPdfAttachmentUrl())
                .set(request.getStartTime() != null, CiesEssMonthlyBillEntity::getStartTime, request.getStartTime())
                .set(request.getEndTime() != null, CiesEssMonthlyBillEntity::getEndTime, request.getEndTime())
                .set(request.getOfficeAttachmentUrl() != null, CiesEssMonthlyBillEntity::getOfficeAttachmentUrl, request.getOfficeAttachmentUrl())
                .set(request.getTaxAdjustmentAmount() != null, CiesEssMonthlyBillEntity::getTaxAdjustmentAmount, request.getTaxAdjustmentAmount())
                .set(request.getRemark() != null, CiesEssMonthlyBillEntity::getRemark, request.getRemark())
                .set(request.getUserAttachmentUrl() != null, CiesEssMonthlyBillEntity::getUserAttachmentUrl, request.getUserAttachmentUrl())
                .set(request.getSettlementStatus() != null, CiesEssMonthlyBillEntity::getSettlementStatus, request.getSettlementStatus())
                .set(request.getDisputeCountdown() != null, CiesEssMonthlyBillEntity::getDisputeCountdown, request.getDisputeCountdown())
                .set(CiesEssMonthlyBillEntity::getUpdateBy, request.getUpdateBy())
                .set(CiesEssMonthlyBillEntity::getUpdateTime, LocalDateTime.now());
        ciesEssMonthlyBillMapper.update(null, wrapper);
    }

    @Override
    public List<CiesEssMonthlyBillResponse> getEssMonthlyBill(List<String> projectIds,List<String> settlementStatus) {
        LambdaUpdateWrapper<CiesEssMonthlyBillEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(CiesEssMonthlyBillEntity::getProjectId, projectIds);
        wrapper.in(CiesEssMonthlyBillEntity::getSettlementStatus, settlementStatus);
        List<CiesEssMonthlyBillEntity> ciesEssMonthlyBillEntities = ciesEssMonthlyBillMapper.selectList(wrapper);
        List<CiesEssMonthlyBillResponse> ciesEssMonthlyBillResponses = ConvertUtils.sourceToTarget(ciesEssMonthlyBillEntities, CiesEssMonthlyBillResponse.class);
        return ciesEssMonthlyBillResponses;
    }

    @Override
    public List<CiesEssMonthlyBillResponse> getEssMonthlyBillByMonth(List<String> projectIds, List<String> month) {
        QueryWrapper<CiesEssMonthlyBillEntity> wrapper = new QueryWrapper<>();
        wrapper.in("project_id", projectIds);
        wrapper.in("settlement_month", month);
        List<CiesEssMonthlyBillEntity> ciesEssMonthlyBillEntities = ciesEssMonthlyBillMapper.selectList(wrapper);
        List<CiesEssMonthlyBillResponse> ciesEssMonthlyBillResponses = ConvertUtils.sourceToTarget(ciesEssMonthlyBillEntities, CiesEssMonthlyBillResponse.class);
        return ciesEssMonthlyBillResponses;
    }

    @Override
    public List<CiesEssMonthlyBillResponse> getBillByStatus(String settlementStatus) {
        QueryWrapper<CiesEssMonthlyBillEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("settlement_status", settlementStatus);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        List<CiesEssMonthlyBillEntity> ciesEssMonthlyBillEntities = ciesEssMonthlyBillMapper.selectList(queryWrapper);
        List<CiesEssMonthlyBillResponse> ciesEssMonthlyBillResponses = ConvertUtils.sourceToTarget(ciesEssMonthlyBillEntities, CiesEssMonthlyBillResponse.class);
        return ciesEssMonthlyBillResponses;
    }

    @Override
    public void batchUpdateMonthlyBill(List<String> essBillIds, String status) {
        LambdaUpdateWrapper<CiesEssMonthlyBillEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(CiesEssMonthlyBillEntity::getEssBillId, essBillIds)
                .eq(CiesEssMonthlyBillEntity::getDr, YesOrNo.NO.getCode())
                .set(CiesEssMonthlyBillEntity::getSettlementStatus, status)
                .set(CiesEssMonthlyBillEntity::getUpdateBy, CiesUserContext.getCurrentUser())
                .set(CiesEssMonthlyBillEntity::getUpdateTime, LocalDateTime.now());
    }
}
