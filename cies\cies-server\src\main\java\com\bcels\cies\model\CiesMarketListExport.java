package com.bcels.cies.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @decription 用户代购电响应列表
 * <AUTHOR>
 */
@Data
@ToString
public class CiesMarketListExport {
    
    @ApiModelProperty("项目名称")
    private String proName;

    @ApiModelProperty("地区")
    private String region;

    @ApiModelProperty("用电类型")
    private String electricityType;

    @ApiModelProperty("电压等级")
    private String voltageLevel;

    @ApiModelProperty("尖峰电量电价")
    private BigDecimal peakEnergyPrice;

    @ApiModelProperty("尖峰时段")
    private String peakPeriod;

    @ApiModelProperty("高峰电量电价")
    private BigDecimal highEnergyPrice;

    @ApiModelProperty("高峰时段")
    private String highPeriod;

    @ApiModelProperty("平时电量电价")
    private BigDecimal normalEnergyPrice;

    @ApiModelProperty("平时段")
    private String normalPeriod;

    @ApiModelProperty("低谷电量电价")
    private BigDecimal offPeakEnergyPrice;

    @ApiModelProperty("低谷时段")
    private String offPeakPeriod;

    @ApiModelProperty("深谷电量电价")
    private BigDecimal valleyEnergyPrice;

    @ApiModelProperty("深谷时段")
    private String valleyPeriod;

    @ApiModelProperty("需量电价(元/千瓦·月)")
    private BigDecimal demandPrice;

    @ApiModelProperty("容量电价(元/千伏安·月)")
    private BigDecimal capacityPrice;
}
