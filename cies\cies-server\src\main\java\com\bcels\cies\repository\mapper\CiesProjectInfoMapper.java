package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.repository.entity.CiesProjectInfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.response.CiesIncomResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import com.bcels.cies.response.CiesStatisticsProjectInfoResponse;
import com.bcels.cies.response.CiesStatisticsProjectResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 项目信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Mapper
public interface CiesProjectInfoMapper extends BaseMapper<CiesProjectInfoEntity> {
    List<CiesProjectInfoResponse> findProjectInfo(@Param("projectId") String projectId);

    CiesProjectInfoResponse findEnterpriseByProjectId(@Param("projectId") String projectId);

    IPage<CiesProjectInfoResponse> findProjectInfoForPage(@Param("page") Page<CiesProjectInfoResponse> page, @Param("request") CiesProjectInfoRequest request);

    IPage<CiesProjectInfoResponse> findMarKetForPage(@Param("page") Page<CiesProjectInfoResponse> page, @Param("request") CiesProjectInfoRequest request);

    IPage<CiesProjectInfoResponse> findMarketProjectInfoForPage(@Param("page") IPage<CiesProjectInfoResponse> page, @Param("request") CiesMarketConfigRequest request);

    CiesStatisticsProjectResponse queryByEnergyStorageType();

    CiesStatisticsProjectResponse queryByProvince();

    CiesIncomResponse getIncome(@Param("projectIds") List<String> projectIds);

    List<CiesProjectInfoResponse> getSettlementStatement(CiesProjectInfoRequest request);

    List<CiesProjectInfoResponse> getPowerCurve(CiesProjectInfoRequest request);

    Integer verificationStatement(CiesProjectInfoRequest request);

}
