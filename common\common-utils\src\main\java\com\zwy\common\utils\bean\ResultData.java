package com.zwy.common.utils.bean;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


@Data
@ToString
public class ResultData<T> implements Serializable {

    public static final int SUCCESS = 200;
    public static final int ERROR = 500;
    public static final String SUCCESS_MESSAGE = "成功";

    private static final long serialVersionUID = -6843333389447091780L;
    private int code;
    private String message;
    private T data;

    public boolean hasSuccessed() {
        return this.code == 200;
    }

    public static <T> ResultData success() {
        ResultData<T> respData = new ResultData<>();
        respData.setCode(SUCCESS);
        respData.setMessage(SUCCESS_MESSAGE);
        return respData;
    }

    public static <T> ResultData<T> success(T data) {
        ResultData<T> respData = new ResultData<>();
        respData.setCode(SUCCESS);
        respData.setMessage(SUCCESS_MESSAGE);
        respData.setData(data);
        return respData;
    }

    public static <T> ResultData<T> fail(String message) {
        ResultData<T> respData = new ResultData<>();
        respData.setCode(ERROR);
        respData.setMessage(message);
        return respData;
    }

    public static <T> ResultData<T> fail(int code, String message) {
        ResultData<T> respData = new ResultData<>();
        respData.setCode(code);
        respData.setMessage(message);
        return respData;
    }

    public static <T> ResultData<T> fail(int code, String message, T data) {
        ResultData<T> respData = new ResultData<>();
        respData.setCode(code);
        respData.setMessage(message);
        respData.setData(data);
        return respData;
    }

    public static <T> ResultData<T> fail(ErrorCode errorCode) {
        ResultData<T> respData = new ResultData<>();
        respData.setCode(errorCode.getCode());
        respData.setMessage(errorCode.getMessage());
        return respData;
    }


    public static <T> ResultData<T> fail(ErrorCode errorCode,String message) {
        ResultData<T> respData = new ResultData<>();
        respData.setCode(errorCode.getCode());
        respData.setMessage(message);
        return respData;
    }

    public static <T> ResultData<T> fail(ErrorCode errorCode, T data) {
        ResultData<T> respData = new ResultData<>();
        respData.setCode(errorCode.getCode());
        respData.setMessage(errorCode.getMessage());
        respData.setData(data);
        return respData;
    }


}
