package com.bcels.cies.domain;


import com.aliyuncs.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.bcels.cies.emuns.ElectricityPriceTypeEnum;
import com.bcels.cies.emuns.SettlementBillStatusEnum;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.infrastructure.config.OssConfig;
import com.bcels.cies.infrastructure.scheduler.BillTaskScheduler;
import com.bcels.cies.infrastructure.utils.ComparisonUtils;
import com.bcels.cies.infrastructure.utils.SettlementBillUtil;
import com.bcels.cies.repository.entity.*;
import com.bcels.cies.request.*;
import com.bcels.cies.response.*;
import com.bcels.cies.service.*;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.IdGenUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import com.zwy.common.utils.exception.BusinessException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class CiesEssMonthlyBillDomainService {

    private static final List<String> TIME_PERIODS = List.of(" 尖", "峰", "平", "谷","深谷");
    private static final List<String> TIME_STAGE = List.of(" 充电（正向有功）", "放电（反向有功）");

    private static final String SUFFIX = "调深谷";

    private static final Map<String, String> DEEP_NAME_MAP = Map.of(
            "topTime", "尖调深谷",
            "lowTime", "谷调深谷",
            "highTime", "峰调深谷",
            "flatTime", "平调深谷"
    );

    @Value("${mftcc.aliyun.oss.billUrl}")
    private String billUrl;

    @Autowired
    private OssConfig ossConfig;

    @Autowired
    private ICiesEssMonthlyBillService iCiesEssMonthlyBillService;

    @Autowired
    private ICiesConnectionPointService iCiesConnectionPointService;

    @Autowired
    private ICiesPeakValleyPriceGapService iCiesPeakValleyPriceGapService;

    @Autowired
    private ICiesValleyPowerConfigService iCiesValleyPowerConfigService;

    @Autowired
    private ICiesMarketStageService iCiesMarketStageService;

    @Autowired
    private ICiesMarketElecPriService iCiesMarketElecPriService;

    @Autowired
    private ICiesProjectInfoService iCiesProjectInfoService;

    @Autowired
    private ICiesValleyConfigDetailsService iCiesValleyConfigDetailsService;

    @Autowired
    private BillTaskScheduler taskScheduler;

    public PageResponse<CiesEssMonthlyBillResponse> findForPage(CiesEssMonthlyBillRequest request) {
        return iCiesEssMonthlyBillService.findForPage(request);
    }

    public void save(CiesEssMonthlyBillRequest request) {
        iCiesEssMonthlyBillService.save(request);
    }

    public void update(CiesEssMonthlyBillRequest request) {
        iCiesEssMonthlyBillService.update(request);
    }

    public void reviewUpdate(CiesEssMonthlyBillRequest request) {
        iCiesEssMonthlyBillService.reviewUpdate(request);
    }

    public PageResponse<CiesEssMonthlyBillResponse> historySettlementPage(CiesEssMonthlyBillRequest request) {
        return iCiesEssMonthlyBillService.historySettlementPage(request);
    }

    /**
     * 查看结算单详情
     * @param essBillId
     * @return
     */
    public CiesSettlementBillDetailResponse findById(String essBillId){

        List<CiesPeakValleyPriceGapResponse> billList = new ArrayList<>();
        // 获取结算单详情
        CiesEssMonthlyBillEntity entity = iCiesEssMonthlyBillService.getById(essBillId);
        CiesSettlementBillDetailResponse response = BeanCopyUtil.copyProperties(entity, CiesSettlementBillDetailResponse::new);
        // 结算月份
        response.setSettlementMonth(response.getSettlementMonth().replace("-","年")+"月");
        // 下发状态
        response.setSettlementStatus(SettlementBillStatusEnum.getDescByCode(response.getSettlementStatus()));
        // 申诉倒计时
//        response.setDisputeCountdown(calculateTimeRemaining(response.getDisputeCountdown()));
        response.setDisputeCountdown(response.getDisputeCountdown());
        // 收益信息
        copyBillDetail(response,entity);
        // 组装峰谷价差信息
        CiesPeakValleyPriceGapRequest request = new CiesPeakValleyPriceGapRequest();
        request.setEssBillId(essBillId);
        List<String> projectIdList = new ArrayList<>();
        projectIdList.add(entity.getProjectId());
        List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(projectIdList);
        if (CollectionUtils.isEmpty(connInfo)){
            return response;
        }
        // 并网点统一排序
        connInfo.sort(
                Comparator.comparing(
                        CiesConnectionPointResponse::getSortOrder,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CiesConnectionPointResponse::getChannelName,
                        Comparator.nullsLast(Comparator.naturalOrder())
                )
        );
        for (CiesConnectionPointResponse connectionPointResponse : connInfo) {
            // 充电正向有功
            request.setConnectionPointId(connectionPointResponse.getConnectionPointId());
            request.setStatPeriod("充电（正向有功）");
            List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses = iCiesPeakValleyPriceGapService.queryPriceGap(request);
            List<CiesPeakValleyPriceGapResponse> sortedChargingData = ciesPeakValleyPriceGapResponses.stream()
                    .sorted(Comparator.comparingInt(
                            item -> TIME_PERIODS.indexOf(item.getTimeRange())  // 通过顺序列表定位优先级
                    ))
                    .toList();
            billList.addAll(sortedChargingData);
            // 放电反向有功
            request.setStatPeriod("放电（反向有功）");
            List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses1 = iCiesPeakValleyPriceGapService.queryPriceGap(request);
            List<CiesPeakValleyPriceGapResponse> sortedChargingData1 = ciesPeakValleyPriceGapResponses1.stream()
                    .sorted(Comparator.comparingInt(
                            item -> TIME_PERIODS.indexOf(item.getTimeRange())  // 通过顺序列表定位优先级
                    ))
                    .toList();
            billList.addAll(sortedChargingData1);
            billList.forEach(item->{
                item.setConnectionPointName(connectionPointResponse.getConnectionPointName());
                item.setPeakValleyRevenue(entity.getPeakValleyRevenue());
            });
        }
        response.setBillList(billList);
        // 组装深谷信息
        if (YesOrNo.YES.getCode() == entity.getIsCalDeep()){
            CiesDeepResponse response1 = iCiesValleyConfigDetailsService.queryDeepInfo(essBillId);
            // 还原表头
            String[] title = response1.getTitle()
                    .replaceAll("^\\[|\\]$", "") // 去除首尾方括号
                    .split(",\\s*"); // 按逗号分割（兼容空格）
            // 还原完整深谷数据行
            String[][] data = Arrays.stream(response1.getData().split("(?<=]),\\s*"))
                    .map(row -> row.replaceAll("^\\[ |\\]$", "").split(",\\s*"))
                    .toArray(String[][]::new);

            response.setTitle(title);
            response.setData(data);
        }
        return response;
    }


    /**
     * 生成深谷数据并重新计算
     * @param request
     */
    public void generateDeepDetail(CiesEssMonthlyBillRequest request) {
        String essBillId = request.getEssBillId();
        CiesValleyPowerConfigResponse ciesValleyPowerConfigResponse = iCiesValleyPowerConfigService.queryValleyPowerConfig(request.getEssBillId());
        if (!ObjectUtils.isEmpty(ciesValleyPowerConfigResponse)){
            // 删除深谷配置
            iCiesValleyPowerConfigService.removeById(ciesValleyPowerConfigResponse.getConfigId());
            // 删除深谷数据
            QueryWrapper<CiesValleyConfigDetailsEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("config_id",ciesValleyPowerConfigResponse.getConfigId());
            iCiesValleyConfigDetailsService.remove(queryWrapper);
        }
        // 深谷配置落库
        CiesValleyPowerConfigEntity configEntity = new CiesValleyPowerConfigEntity();
        configEntity.setEssBillId(essBillId);
        configEntity.setConfigId(IdGenUtil.genUniqueId());
        configEntity.setDate(request.getDeepDate());
        configEntity.setTimeRangeName(request.getDeepTime());
        configEntity.setCreateTime(LocalDateTime.now());
        configEntity.setCreateBy(CiesUserContext.getCurrentUser());
        iCiesValleyPowerConfigService.save(configEntity);
        // 应用深谷到峰谷价差参数
        HashMap<String,BigDecimal> bigDecimalHashMap = new HashMap<>();
        CiesEssMonthlyBillEntity entity = iCiesEssMonthlyBillService.getById(essBillId);
        String month = entity.getSettlementMonth();
        List<String> projectIdList = new ArrayList<>();
        projectIdList.add(entity.getProjectId());
        List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(projectIdList);
        if (CollectionUtils.isEmpty(connInfo)) {
            return;
        }
        // 并网点统一排序
        connInfo.sort(
                Comparator.comparing(
                        CiesConnectionPointResponse::getSortOrder,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CiesConnectionPointResponse::getChannelName,
                        Comparator.nullsLast(Comparator.naturalOrder())
                )
        );
        String projectId = connInfo.get(0).getProjectId();
        // 获取日期
        List<String> date = Arrays.stream(request.getDeepDate().split(",")).toList();
        String[] timeRange = request.getDeepTime().split(",");
        // 获取深谷时段
        List<String> deepTimeRange = getDeepTimeRange(timeRange, month, projectId);
        // 完整表头
        List<String> modifiedList = Stream.concat(
                Stream.of(" 并网点名称", "统计阶段", "日期", "倍率"), // 头部插入4个元素
                Stream.concat(
                        deepTimeRange.stream(),
                        Stream.of(" 深谷合计电量") // 尾部追加1个元素
                )
        ).collect(Collectors.toList());

        // 深谷合计落库
        CiesValleyConfigDetailsEntity entity1 = new CiesValleyConfigDetailsEntity();
        entity1.setConfigId(configEntity.getConfigId());
        entity1.setConfigDetailId(IdGenUtil.genUniqueId());
        entity1.setDataType("DEEP_TITLE");
        entity1.setDeepData(Arrays.toString(modifiedList.toArray()));
        entity1.setCreateTime(LocalDateTime.now());
        entity1.setCreateBy(CiesUserContext.getCurrentUser());
        iCiesValleyConfigDetailsService.save(entity1);

        CiesSettlementBillDetailResponse response = new CiesSettlementBillDetailResponse();
        response.setTitle((String[]) modifiedList.toArray());
        // 填充第一列数据
        DynamicEntity dynamicEntity = new DynamicEntity(modifiedList);
        // 初始化第一行
        for (String title : modifiedList) {
            dynamicEntity.setProperty(title, "");
            if (title.contains("#")) {
                String deepName = title.split("#")[1];
                dynamicEntity.setProperty(title, DEEP_NAME_MAP.get(deepName));
            }
        }
        List<DynamicEntity> employeeList = new ArrayList<>();
        for (CiesConnectionPointResponse connectionPointResponse : connInfo) {
            for (String stage : TIME_STAGE) {
                // 识别所有需要合计项
                List<String> sumKeys = modifiedList.stream()
                        .filter(title -> title.contains("#"))
                        .toList();
                Map<String, BigDecimal> totalSumMap = Collections.synchronizedMap(new LinkedHashMap<>());
                sumKeys.forEach(key -> totalSumMap.put(key, BigDecimal.ZERO));
                for (String current : date) {
                    DynamicEntity employee = new DynamicEntity(modifiedList);
                    for (String title : modifiedList) {
                        // todo 读取此时指标数据
                        BigDecimal indicatorValue = BigDecimal.ZERO;
                        employee.setProperty(title, indicatorValue != null ? indicatorValue.toString() : "");
                        if (title.contains("#")) {
                            String dataTime = title.substring(0, title.indexOf("电"));
                            String startTime = dataTime.split("-")[0] + "抄表数据（kwh）";
                            String endTime = dataTime.split("-")[1] + "抄表数据（kwh）";
                            BigDecimal value1 = new BigDecimal(employee.getProperty(startTime, String.class));
                            BigDecimal value2 = new BigDecimal(employee.getProperty(endTime, String.class));
                            BigDecimal tempValue = value2.subtract(value1);
                            employee.setProperty(title, tempValue != null ? tempValue.toString() : "");
                            // 临时保存合计数据
                            totalSumMap.computeIfPresent(title, (k, v) -> v.add(tempValue));
                            String deepName = title.split("#")[1];
                            employee.setProperty(title, DEEP_NAME_MAP.get(deepName));
                        }
                        if ("并网点名称".equals(title)) {
                            employee.setProperty(title, connectionPointResponse.getConnectionPointName());
                        }
                        if ("统计阶段".equals(title)) {
                            employee.setProperty(title, stage);
                        }
                        if ("日期".equals(title)) {
                            employee.setProperty(title, current);
                        }
                        employeeList.add(employee);
                    }
                }
                // 合计行
                DynamicEntity employeeTotal = new DynamicEntity(modifiedList);

                for (String title : modifiedList) {
                    employeeTotal.setProperty(title, "");
                    if ("并网点名称".equals(title)) {
                        employeeTotal.setProperty(title, connectionPointResponse.getConnectionPointName());
                    }
                    if ("统计阶段".equals(title)) {
                        employeeTotal.setProperty(title, stage);
                    }
                    if ("日期".equals(title)) {
                        employeeTotal.setProperty(title, "合计");
                    }
                    if (title.contains("#")) {
                        String[] split = title.split("#");
                        employeeTotal.setProperty(title, totalSumMap.get(title));
                        bigDecimalHashMap.put(connectionPointResponse.getConnectionPointName()+"-"+stage+"-"+DEEP_NAME_MAP.get(split[1]),totalSumMap.get(title));
                    }
                    if ("深谷合计电量".equals(title)) {
                        BigDecimal safeSum = totalSumMap.values().stream()
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, (a, b) -> a.add(
                                        Optional.ofNullable(b).orElse(BigDecimal.ZERO)
                                ));
                        employeeTotal.setProperty(title, safeSum.toString());
                        employeeTotal.to2DArray();
                        bigDecimalHashMap.put(connectionPointResponse.getConnectionPointName()+"-"+stage+"-"+"深谷",safeSum);
                    }
                }
                // 深谷合计落库
                CiesValleyConfigDetailsEntity entity2 = new CiesValleyConfigDetailsEntity();
                entity2.setConfigId(configEntity.getConfigId());
                entity2.setConfigDetailId(IdGenUtil.genUniqueId());
                entity2.setDataType("DEEP_TOTAL");
                entity2.setConnectionPointId(connectionPointResponse.getConnectionPointId());
                entity2.setDeepData(bigDecimalHashMap.toString());
                entity2.setCreateTime(LocalDateTime.now());
                entity2.setCreateBy(CiesUserContext.getCurrentUser());
                iCiesValleyConfigDetailsService.save(entity2);

                employeeList.add(employeeTotal);
                // 深谷明细落库
                CiesValleyConfigDetailsEntity entity3 = new CiesValleyConfigDetailsEntity();
                entity3.setConfigId(configEntity.getConfigId());
                entity3.setConfigDetailId(IdGenUtil.genUniqueId());
                entity3.setDataType("DEEP_DATA");
                entity3.setDeepData(employeeList.toString());
                entity3.setCreateTime(LocalDateTime.now());
                entity3.setCreateBy(CiesUserContext.getCurrentUser());
                iCiesValleyConfigDetailsService.save(entity3);
            }
        }
        CiesSettlementBillGenerateResponse response1 = generateDeepPeakValleyPriceGap(essBillId, bigDecimalHashMap);
        generateSettlementBillFile(response1);
    }

    /**
     * 重新计算
     * @param request
     */
    public void reCalculate(CiesEssMonthlyBillIncomeRequest request) {
        String essBillId = request.getEssBillId();
        CiesEssMonthlyBillEntity entity = iCiesEssMonthlyBillService.getById(essBillId);
        // 查询项目指定月电价
        CiesMarketElecPriEntity elecPriInfo = iCiesMarketElecPriService.findElecPriInfo(entity.getSettlementMonth().replace("-","/"), entity.getProjectId());
        // 初始化峰谷价差总收益（元）
        BigDecimal peakValleyRevenue = BigDecimal.ZERO;
        // 查询最新峰谷价差信息
        List<CiesPeakValleyPriceGapResponse> gapResponseList = new ArrayList<>();
        List<CiesPeakValleyPriceRequest> list = request.getBillList()   ;
        List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponse = BeanCopyUtil.copyListProperties(list, CiesPeakValleyPriceGapResponse::new);

        Map<String, List<CiesPeakValleyPriceGapResponse>> groupedMap = ciesPeakValleyPriceGapResponse.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getConnectionPointName() + "-" + item.getStatPeriod()
                ));

        CiesProjectInfoEntity projectInfo = iCiesProjectInfoService.getById(entity.getProjectId());
        if (ElectricityPriceTypeEnum.STANDARD_WITH_DEEP_VALLEY.getCode().equals(projectInfo.getTemplateType()) || ElectricityPriceTypeEnum.WITH_TAX_DIFF_AND_DEEP_VALLEY.getCode().equals(projectInfo.getTemplateType())) {
            // 含深谷计算
            CiesDeepResponse response1 = iCiesValleyConfigDetailsService.queryDeepInfoByDataType(essBillId, "DEEP_TOTAL");
            // 深谷数据的map
            Map<String, BigDecimal> bigDecimalHashMap = new HashMap<>();
            Pattern pattern = Pattern.compile("([^=,]+)=([^,}]+)");
            Matcher matcher = pattern.matcher(response1.getData());

            while (matcher.find()) {
                String key = matcher.group(1).trim();
                BigDecimal value = new BigDecimal(matcher.group(2).trim());
                bigDecimalHashMap.put(key, value);
            }
            // 峰谷价差总收益
            for (Map.Entry<String, List<CiesPeakValleyPriceGapResponse>> entry : groupedMap.entrySet()) {
                List<CiesPeakValleyPriceGapResponse> valueList = entry.getValue();
                // 统计分项合计
                //充电（正向有功）
                BigDecimal chargeTotalAmount = BigDecimal.ZERO;
                // 放电（反向有功）
                BigDecimal dischargeTotalAmount = BigDecimal.ZERO;
                String connPointId = null;
                // 遍历每一个并网点下面的每一个统计阶段
                for (CiesPeakValleyPriceGapResponse item : valueList) {
                    connPointId = item.getConnectionPointId();
                    String gapName = String.join("-",
                            item.getConnectionPointName(),
                            item.getStatPeriod(),
                            item.getTimeRange()
                    ) + SUFFIX;
                    if (bigDecimalHashMap.containsKey(gapName)) {
                        BigDecimal elecPrice = getElecPrice(item.getTimeRange(), elecPriInfo);
                        // 如果匹配到深谷对应的尖峰平谷，那么需要结算电量减去深谷部分数据
                        item.setBilledUsage(item.getBilledUsage().subtract(bigDecimalHashMap.get(gapName)));
                        item.setAmount(elecPrice.multiply(item.getBilledUsage()).setScale(2, RoundingMode.HALF_UP));
                    }

                    if ("放电（反向有功）".equals(item.getStatPeriod())) {
                        dischargeTotalAmount.add(item.getAmount());
                    } else {
                        chargeTotalAmount.add(item.getAmount());
                    }
                }
                // 处理深谷数据
                BigDecimal elecPrice1 = getElecPrice("深谷", elecPriInfo);
                if (entry.getKey().contains("放电（反向有功）")) {
                    dischargeTotalAmount.add(bigDecimalHashMap.get(entry.getKey() + "-" + "深谷").multiply(elecPrice1).setScale(2, RoundingMode.HALF_UP));
                } else {
                    chargeTotalAmount.add(bigDecimalHashMap.get(entry.getKey() + "-" + "深谷").multiply(elecPrice1).setScale(2, RoundingMode.HALF_UP));
                }
                // 过滤带深谷数据（避免考虑无深谷切换有深谷，有深谷切换有深谷）
                List<CiesPeakValleyPriceGapResponse> filteredList = valueList.stream()
                        .filter(deep -> !"深谷".equals(deep.getTimeRange()))
                        .toList();
                List<CiesPeakValleyPriceGapEntity> ciesPeakValleyPriceGapEntities = generateDeepRecord(connPointId, essBillId);
                List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses1 = BeanCopyUtil.copyListProperties(ciesPeakValleyPriceGapEntities, CiesPeakValleyPriceGapResponse::new);
                filteredList.addAll(ciesPeakValleyPriceGapResponses1);
                // 峰谷价差总收益 = 放电-充电
                peakValleyRevenue = dischargeTotalAmount.subtract(chargeTotalAmount).add(peakValleyRevenue);
                gapResponseList.addAll(filteredList);
            }
        } else {
            for (Map.Entry<String, List<CiesPeakValleyPriceGapResponse>> entry : groupedMap.entrySet()) {
                List<CiesPeakValleyPriceGapResponse> valueList = entry.getValue();
                // 不含深谷计算
                //充电（正向有功）
                BigDecimal chargeTotalAmount = BigDecimal.ZERO;
                // 放电（反向有功）
                BigDecimal dischargeTotalAmount = BigDecimal.ZERO;
                // 遍历每一个并网点下面的每一个统计阶段
                for (CiesPeakValleyPriceGapResponse item : valueList) {
                    BigDecimal elecPrice = getElecPrice(item.getTimeRange(), elecPriInfo);
                    item.setAmount(elecPrice.multiply(item.getBilledUsage()).setScale(2, RoundingMode.HALF_UP));
                    if ("放电（反向有功）".equals(item.getStatPeriod())) {
                        dischargeTotalAmount.add(item.getAmount());
                    } else {
                        chargeTotalAmount.add(item.getAmount());
                    }
                }
                // 峰谷价差总收益 = 放电-充电
                peakValleyRevenue = dischargeTotalAmount.subtract(chargeTotalAmount).add(peakValleyRevenue);
                gapResponseList.addAll(valueList);
            }
        }
        // 更新峰谷价差数据
        List<CiesPeakValleyPriceGapEntity> ciesPeakValleyPriceGapEntities = BeanCopyUtil.copyListProperties(gapResponseList, CiesPeakValleyPriceGapEntity::new);
        iCiesPeakValleyPriceGapService.deleteByBillId(essBillId);
        ciesPeakValleyPriceGapEntities.forEach(item->{
            item.setCreateTime(LocalDateTime.now());
            item.setEssBillId(essBillId);
            item.setPriceGapId(IdGenUtil.genUniqueId());
            item.setCreateBy(CiesUserContext.getCurrentUser());
        });
        iCiesPeakValleyPriceGapService.saveBatch(ciesPeakValleyPriceGapEntities);

        CiesSettlementBillGenerateResponse response = calIncome(peakValleyRevenue, entity.getProjectId(), entity.getEssBillId());
        response.setBillList(gapResponseList);
        response.setRemark(entity.getRemark());
        String monthDesc = entity.getSettlementMonth().replace("/", "年") + "月";
        response.setBillTitle("储能项目结算单-" + monthDesc);
        response.setEssBillId(entity.getEssBillId());
        // 生成结算单文件
        generateSettlementBillFile(response);
        // 重新生成说明
        String remark = generateRemark(request);
        CiesEssMonthlyBillIncomeRequest request1 = new CiesEssMonthlyBillIncomeRequest();
        request1.setEssBillId(essBillId);
        request1.setRemark(remark);
        request1.setUserAttachmentUrl(request.getUserAttachmentUrl());
        request1.setStartTime(request.getStartTime());
        request1.setEndTime(request.getEndTime());
        request1.setSettlementStatus(SettlementBillStatusEnum.PENDING_DISPATCH.getCode());
        request1.setUpdateBy(CiesUserContext.getCurrentUser());
        iCiesEssMonthlyBillService.updateMonthlyBill(request1);
    }


    public void submit(CiesEssMonthlyBillIncomeRequest request){
        // 获取提交的峰谷价差结果
        List<CiesPeakValleyPriceRequest> list = request.getBillList();
        List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses1 = BeanCopyUtil.copyListProperties(list, CiesPeakValleyPriceGapResponse::new);
        // 校验数据是否是结算单对应的数据
        String essBillId = request.getEssBillId();
        CiesEssMonthlyBillEntity entity = iCiesEssMonthlyBillService.getById(essBillId);
        if (!(entity.getStartTime().equals(request.getStartTime()) && entity.getEndTime().equals(request.getEndTime()))){
            throw new BusinessException("修改结算信息后必须重新计算，请点击重新计算按钮重新生成结算单信息");
        }
        CiesPeakValleyPriceGapRequest request1 = new CiesPeakValleyPriceGapRequest();
        request1.setEssBillId(essBillId);
        List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses = iCiesPeakValleyPriceGapService.queryPriceGap(request1);
        ComparisonUtils.checkBilledUsageConsistency(ciesPeakValleyPriceGapResponses, ciesPeakValleyPriceGapResponses1);
        // 校验通过基础信息落库,附件名称，说明，审核状态
        CiesEssMonthlyBillIncomeRequest updateRequest = new CiesEssMonthlyBillIncomeRequest();
        updateRequest.setEssBillId(essBillId);
        updateRequest.setRemark(request.getRemark());
        updateRequest.setUserAttachmentUrl(request.getUserAttachmentUrl());
        updateRequest.setStartTime(request.getStartTime());
        updateRequest.setEndTime(request.getEndTime());
        updateRequest.setSettlementStatus(SettlementBillStatusEnum.PENDING_REVIEW.getCode());
        updateRequest.setUpdateBy(CiesUserContext.getCurrentUser());
        iCiesEssMonthlyBillService.updateMonthlyBill(updateRequest);
    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    public String upload(MultipartFile file) {
        try {
            return ossConfig.upload(file, billUrl);
        } catch (Exception e) {
            throw new BusinessException("上传结算单附件失败");
        }
    }

    /**
     * 下载文件
     * @param fileName
     * @param response
     */
    public void downloadFile(String fileName,HttpServletResponse response) {
        String downloadPath = billUrl + fileName;
        ossConfig.downloadFile(downloadPath, response, fileName);
    }

    /**
     * 删除文件
     * @param fileName
     */
    public void deleteFile(String fileName) {
        String downloadPath = billUrl + fileName;
        ossConfig.deteleOss(downloadPath);
    }

    private String generateRemark(CiesEssMonthlyBillIncomeRequest request){
        String essBillId = request.getEssBillId();
        CiesEssMonthlyBillEntity entity = iCiesEssMonthlyBillService.getById(essBillId);
        CiesProjectInfoEntity projectInfo = iCiesProjectInfoService.getById(entity.getProjectId());

        LocalDateTime startTime = request.getStartTime();
        LocalDateTime endTime = request.getEndTime();
        // 获取说明变量
        DateTimeFormatter chineseFormatter = DateTimeFormatter.ofPattern("yyyy年M月dd日 HH:mm:ss");
        String chineseStartTime = startTime.format(chineseFormatter);
        String chineseEndTime = endTime.format(chineseFormatter);
        String monthDesc = request.getSettlementMonth().replace("/","年")+"月";
        String template;
        if (ElectricityPriceTypeEnum.WITH_TAX_DIFF_AND_DEEP_VALLEY.getCode().equals(projectInfo.getTemplateType())){
            // 原始字符串模板(含税)
            template = "（1）以下数据为从%s - %s的结算数据。\n" +
                    "（2）电价为企业%s份供电局电费单中各时段的电能电费单价、输配电费单价、基金及附加费单价、市场化分摊电费单价、系统运行费单价之和。\n" +
                    "（3）峰谷价差总收益=放电电金额之和-充电金额之和；甲方分成金额=峰谷价差总收益*甲方分成比例；乙方分成金额=峰谷价差总收益*乙方分成比例；\n"+
                     "税差金额=乙方分成金额*税差计算比例；甲方收益=甲方分成比例+税差金额；乙方收益=乙方分成金额-税差金额。";
        }else{
            // 原始字符串模板（不含税）
            template = "（1）以下数据为从%s - %s的结算数据。\n" +
                    "（2）电价为企业%s份供电局电费单中各时段的电能电费单价、输配电费单价、基金及附加费单价、市场化分摊电费单价、系统运行费单价之和。\n" +
                    "（3）峰谷价差总收益=放电电金额之和-充电金额之和；甲方收益=峰谷价差总收益*甲方分成比例；乙方收益=峰谷价差总收益*乙方分成比例。";
        }
        // 动态替换时间变量
        return String.format(template, chineseStartTime, chineseEndTime, monthDesc);
    }

    /**
     * 查看结算单详情(小程序)
     * @param essBillId
     * @return
     */
    public CiesSettlementBillDetailResponse findByBillId(String essBillId){
        List<CiesPeakValleyPriceGapResponse> billList = new ArrayList<>();
        // 获取结算单详情
        CiesEssMonthlyBillEntity entity = iCiesEssMonthlyBillService.getById(essBillId);
        CiesSettlementBillDetailResponse response = BeanCopyUtil.copyProperties(entity, CiesSettlementBillDetailResponse::new);
        // 申诉倒计时
//        response.setDisputeCountdown(calculateTimeRemaining(response.getDisputeCountdown()));
        response.setDisputeCountdown(response.getDisputeCountdown());
        // 组装峰谷价差信息
        CiesPeakValleyPriceGapRequest request = new CiesPeakValleyPriceGapRequest();
        request.setEssBillId(essBillId);
        List<String> projectIdList = new ArrayList<>();
        projectIdList.add(entity.getProjectId());
        List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findConnByProjectIds(projectIdList);
        if (CollectionUtils.isEmpty(connInfo)){
            return response;
        }
        // 并网点统一排序
        connInfo.sort(
                Comparator.comparing(
                        CiesConnectionPointResponse::getSortOrder,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ).thenComparing(
                        CiesConnectionPointResponse::getChannelName,
                        Comparator.nullsLast(Comparator.naturalOrder())
                )
        );
        for (CiesConnectionPointResponse connectionPointResponse : connInfo) {
            // 充电正向有功
            request.setConnectionPointId(connectionPointResponse.getConnectionPointId());
            request.setStatPeriod("充电（正向有功）");
            List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses = iCiesPeakValleyPriceGapService.queryPriceGap(request);
            List<CiesPeakValleyPriceGapResponse> sortedChargingData = ciesPeakValleyPriceGapResponses.stream()
                    .sorted(Comparator.comparingInt(
                            item -> TIME_PERIODS.indexOf(item.getTimeRange())  // 通过顺序列表定位优先级
                    ))
                    .toList();
            billList.addAll(sortedChargingData);
            // 放电反向有功
            request.setStatPeriod("放电（反向有功）");
            List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses1 = iCiesPeakValleyPriceGapService.queryPriceGap(request);
            List<CiesPeakValleyPriceGapResponse> sortedChargingData1 = ciesPeakValleyPriceGapResponses1.stream()
                    .sorted(Comparator.comparingInt(
                            item -> TIME_PERIODS.indexOf(item.getTimeRange())  // 通过顺序列表定位优先级
                    ))
                    .toList();
            billList.addAll(sortedChargingData1);
            billList.forEach(item->{
                item.setConnectionPointName(connectionPointResponse.getConnectionPointName());
                item.setPeakValleyRevenue(entity.getPeakValleyRevenue());
            });
        }
        response.setBillList(billList);
        return response;
    }


    /**
     * 获取收益统计（小程序）
     *
     * @param request
     * @return
     */
    public CiesMobileIncomeResponse statisticIncome(CiesMobileIncomeRequest request) {
        CiesMobileIncomeResponse response = new CiesMobileIncomeResponse();
        String month = request.getMonth();
        List<String> result = getPrevious12Months(month);
        List<BigDecimal> partyAIncome = new ArrayList<>();
        List<BigDecimal> partyBIncome = new ArrayList<>();
        List<BigDecimal> peakValleyRevenue = new ArrayList<>();
        BigDecimal totalPartyAIncome = BigDecimal.ZERO;
        BigDecimal totalPartyBIncome = BigDecimal.ZERO;
        BigDecimal totalPeakValleyRevenue = BigDecimal.ZERO;
        List<CiesEssMonthlyBillResponse> essMonthlyBillByMonth = iCiesEssMonthlyBillService.getEssMonthlyBillByMonth(request.getProjectIds(), result);
        if (essMonthlyBillByMonth != null) {
            // 甲方收益
            Map<String, BigDecimal> partyAIncomeMap = essMonthlyBillByMonth.stream()
                    .collect(Collectors.toMap(
                            CiesEssMonthlyBillResponse::getSettlementMonth,
                            CiesEssMonthlyBillResponse::getPartyAIncome
                    ));
            // 乙方收益
            Map<String, BigDecimal> partyBIncomeMap = essMonthlyBillByMonth.stream()
                    .collect(Collectors.toMap(
                            CiesEssMonthlyBillResponse::getSettlementMonth,
                            CiesEssMonthlyBillResponse::getPartyBIncome
                    ));
            // 峰谷价差总收益
            Map<String, BigDecimal> peakValleyRevenueMap = essMonthlyBillByMonth.stream()
                    .collect(Collectors.toMap(
                            CiesEssMonthlyBillResponse::getSettlementMonth,
                            CiesEssMonthlyBillResponse::getPeakValleyRevenue
                    ));
            for (String temp : result) {
                if (partyAIncomeMap != null) {
                    partyAIncome.add(partyAIncomeMap.get(temp) != null ? partyAIncomeMap.get(temp) : BigDecimal.ZERO);
                    totalPartyAIncome.add(partyAIncomeMap.get(temp) != null ? partyAIncomeMap.get(temp) : BigDecimal.ZERO);
                }
                if (partyBIncomeMap != null) {
                    partyBIncome.add(partyBIncomeMap.get(temp) != null ? partyBIncomeMap.get(temp) : BigDecimal.ZERO);
                    totalPartyBIncome.add(partyBIncomeMap.get(temp) != null ? partyBIncomeMap.get(temp) : BigDecimal.ZERO);
                }
                if (peakValleyRevenueMap != null) {
                    peakValleyRevenue.add(peakValleyRevenueMap.get(temp) != null ? peakValleyRevenueMap.get(temp) : BigDecimal.ZERO);
                    totalPeakValleyRevenue.add(peakValleyRevenueMap.get(temp) != null ? peakValleyRevenueMap.get(temp) : BigDecimal.ZERO);

                }
            }
            response.setMonth(result);
            response.setPartyAIncome(partyAIncome);
            response.setTotalPartyAIncome(totalPartyAIncome);
            response.setPartyBIncome(partyBIncome);
            response.setTotalPartyBIncome(totalPartyBIncome);
            response.setPeakValleyRevenue(peakValleyRevenue);
            response.setTotalPeakValleyRevenue(totalPeakValleyRevenue);
        }
        return response;
    }

    /**
     * 申诉结算单（小程序）
     * @param essBillId
     */
    public void appealSettle(String essBillId){
        CiesEssMonthlyBillEntity entity = iCiesEssMonthlyBillService.getById(essBillId);
        CiesProjectInfoEntity projectInfo = iCiesProjectInfoService.getById(entity.getProjectId());
        if (StringUtils.isEmpty(projectInfo.getDisputeDays())){
            throw new BusinessException("当前结算单未配置结算单异议等待工作日，不支持提出异议");
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureDateTime = now.plusDays(Integer.parseInt(projectInfo.getDisputeDays()));
        // 开启申诉修改结算单状态
        CiesEssMonthlyBillIncomeRequest request = new CiesEssMonthlyBillIncomeRequest();
        request.setEssBillId(essBillId);
        request.setUpdateBy("服务器");
        request.setSettlementStatus(SettlementBillStatusEnum.APPEALED.getCode());
        request.setDisputeCountdown(futureDateTime);
        iCiesEssMonthlyBillService.updateMonthlyBill(request);
        // 添加任务到调度器中
        CiesEssMonthlyBillResponse task = new CiesEssMonthlyBillResponse();
        task.setEssBillId(essBillId);
        task.setDisputeCountdown(futureDateTime);
        taskScheduler.addTask(task);
    }

    /**
     * 生成结算单excel和pdf
     * @param response
     */
    private void generateSettlementBillFile( CiesSettlementBillGenerateResponse response) {
        try {
                // 生成结算单excel
                byte[] bytes = SettlementBillUtil.generateBillExcel(response);
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                // 上传exel到oss
                String excelFileName = billUrl + LocalDateTime.now().format(formatter) + ".xlsx";
                String fileName1 = ossConfig.writeOss(excelFileName, bytes);
                // excel转化为pdf
                byte[] pdfBytes = SettlementBillUtil.convertExcelToPdfBytes(bytes);
                String pdfFileName = billUrl+LocalDateTime.now().format(formatter)  + ".pdf";
                String fileName2 = ossConfig.writeOss(pdfFileName, pdfBytes);
                CiesEssMonthlyBillIncomeRequest request = new CiesEssMonthlyBillIncomeRequest();
                request.setOfficeAttachmentUrl(fileName1.replace("/",""));
                request.setPdfAttachmentUrl(fileName2.replace("/",""));
                request.setUpdateBy("服务器");
                request.setEssBillId(response.getEssBillId());
                iCiesEssMonthlyBillService.updateMonthlyBill(request);
        }catch (Exception e){
            log.error("重新计算生成结算单文件失败",e);
            throw new BusinessException("重新计算生成结算单文件失败",e);
        }
    }
    /**
     * 深谷作用到峰谷价差计算
     * @param essBillId
     * @param bigDecimalHashMap
     * @return
     */
    public CiesSettlementBillGenerateResponse generateDeepPeakValleyPriceGap(String essBillId, Map<String, BigDecimal> bigDecimalHashMap) {
        CiesEssMonthlyBillEntity entity = iCiesEssMonthlyBillService.getById(essBillId);
        // 初始化峰谷价差总收益（元）
        BigDecimal peakValleyRevenue = BigDecimal.ZERO;
        // 查询项目指定月电价
        CiesMarketElecPriEntity elecPriInfo = iCiesMarketElecPriService.findElecPriInfo(entity.getSettlementMonth().replace("-","/"), entity.getProjectId());
        // 查询最新峰谷价差信息
        List<CiesPeakValleyPriceGapResponse> gapResponseList = new ArrayList<>();
        CiesPeakValleyPriceGapRequest request = new CiesPeakValleyPriceGapRequest();
        request.setEssBillId(essBillId);
        List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses = iCiesPeakValleyPriceGapService.queryPriceGap(request);

        Map<String, List<CiesPeakValleyPriceGapResponse>> groupedMap = ciesPeakValleyPriceGapResponses.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getConnectionPointName() + "-" + item.getStatPeriod()
                ));
        // 峰谷价差总收益
        for (Map.Entry<String, List<CiesPeakValleyPriceGapResponse>> entry : groupedMap.entrySet()) {
            List<CiesPeakValleyPriceGapResponse> valueList = entry.getValue();
            // 统计分项合计
            //充电（正向有功）
            BigDecimal chargeTotalAmount = BigDecimal.ZERO;
            // 放电（反向有功）
            BigDecimal dischargeTotalAmount = BigDecimal.ZERO;
            String connPointId = null;
            // 遍历每一个并网点下面的每一个统计阶段
            for (CiesPeakValleyPriceGapResponse item : valueList) {
                connPointId = item.getConnectionPointId();
                String gapName = String.join("-",
                        item.getConnectionPointName(),
                        item.getStatPeriod(),
                        item.getTimeRange()
                ) + SUFFIX;
                if (bigDecimalHashMap.containsKey(gapName)) {
                    BigDecimal elecPrice = getElecPrice(item.getTimeRange(), elecPriInfo);
                    // 如果匹配到深谷对应的尖峰平谷，那么需要结算电量减去深谷部分数据
                    item.setBilledUsage(item.getBilledUsage().subtract(bigDecimalHashMap.get(gapName)));
                    item.setAmount(elecPrice.multiply(item.getBilledUsage()).setScale(2, RoundingMode.HALF_UP));
                }

                if ("放电（反向有功）".equals(item.getStatPeriod())) {
                    dischargeTotalAmount.add(item.getAmount());
                } else {
                    chargeTotalAmount.add(item.getAmount());
                }
            }
            // 处理深谷数据
            BigDecimal elecPrice1 = getElecPrice("深谷", elecPriInfo);
            if (entry.getKey().contains("放电（反向有功）")){
                dischargeTotalAmount.add(bigDecimalHashMap.get(entry.getKey()+"-"+"深谷").multiply(elecPrice1).setScale(2, RoundingMode.HALF_UP));
            }else{
                chargeTotalAmount.add(bigDecimalHashMap.get(entry.getKey()+"-"+"深谷").multiply(elecPrice1).setScale(2, RoundingMode.HALF_UP));
            }
            // 过滤带深谷数据（避免考虑无深谷切换有深谷，有深谷切换有深谷）
            List<CiesPeakValleyPriceGapResponse> filteredList = valueList.stream()
                    .filter(deep -> !"深谷".equals(deep.getTimeRange()))
                    .toList();
            List<CiesPeakValleyPriceGapEntity> ciesPeakValleyPriceGapEntities = generateDeepRecord(connPointId, essBillId);
            List<CiesPeakValleyPriceGapResponse> ciesPeakValleyPriceGapResponses1 = BeanCopyUtil.copyListProperties(ciesPeakValleyPriceGapEntities, CiesPeakValleyPriceGapResponse::new);
            filteredList.addAll(ciesPeakValleyPriceGapResponses1);
            // 峰谷价差总收益 = 放电-充电
            peakValleyRevenue = dischargeTotalAmount.subtract(chargeTotalAmount).add(peakValleyRevenue);
            gapResponseList.addAll(filteredList);
        }
        CiesSettlementBillGenerateResponse response = calIncome(peakValleyRevenue, entity.getProjectId(), entity.getEssBillId());
        response.setBillList(gapResponseList);
        response.setRemark(entity.getRemark());
        String monthDesc = entity.getSettlementMonth().replace("/", "年") + "月";
        response.setBillTitle("储能项目结算单-" + monthDesc);
        response.setEssBillId(entity.getEssBillId());
        return response;
    }

    private List<CiesPeakValleyPriceGapEntity> generateDeepRecord(String connPointId,String essBillId ) {
        List<CiesPeakValleyPriceGapEntity> gapEntityList = new ArrayList<>();
        // 公共字段值
        BigDecimal zero = BigDecimal.ZERO;

        // 第一条记录（正向）
        CiesPeakValleyPriceGapEntity positive = new CiesPeakValleyPriceGapEntity();
        positive.setPriceGapId(IdGenUtil.genUniqueId());
        positive.setConnectionPointId(connPointId);
        positive.setStatPeriod("充电（正向有功）");
        positive.setTimeRange("深谷");
        positive.setLastMonthRead(zero);
        positive.setCurrentMonthRead(zero);
        positive.setMeteredUsage(zero);
        positive.setBilledUsage(zero);
        positive.setUnitPrice(zero);
        positive.setEssBillId(essBillId);
        positive.setAmount(zero);
        positive.setCreateBy(CiesUserContext.getCurrentUser());
        positive.setCreateTime(LocalDateTime.now());

        CiesPeakValleyPriceGapEntity negative = new CiesPeakValleyPriceGapEntity();
        negative.setConnectionPointId(connPointId);
        negative.setPriceGapId(IdGenUtil.genUniqueId());
        negative.setStatPeriod("放电（反向有功）");
        negative.setTimeRange("深谷");
        negative.setLastMonthRead(zero);
        negative.setCurrentMonthRead(zero);
        negative.setMeteredUsage(zero);
        negative.setBilledUsage(zero);
        negative.setUnitPrice(zero);
        negative.setAmount(zero);
        negative.setCreateBy(CiesUserContext.getCurrentUser());
        negative.setCreateTime(LocalDateTime.now());
        gapEntityList.add(positive);
        gapEntityList.add(negative);
        iCiesPeakValleyPriceGapService.saveBatch(gapEntityList);
        return gapEntityList;
    }

    /**
     * 拆分深谷配置获取对应列名
     * @param timeRange
     * @param month
     * @param projectId
     * @return
     */
    private List<String> getDeepTimeRange(String[] timeRange,String month,String projectId){
        List<String> list = Arrays.stream(timeRange).toList();
        Map<String, String> map = new LinkedHashMap<>();

        // 查询数据库时段，将相同时段直接合并得到时间段数组
        String startTime = timeRange[0].split("-")[0];
        String endTime = timeRange[timeRange.length-1].split("-")[1];
        CiesMarketStageEntity ciesMarketStageEntity = iCiesMarketStageService.findMarketStageList(month, projectId).get(0);
        Map<String, String> timeRangeMap = convertToTimeRangeMap(ciesMarketStageEntity);
        Map<String, String> stageMap = mergeContinuousTimeRanges(timeRangeMap,startTime,endTime);
        // 匹配当前深谷的时间段是否包含在内  如果包含在内直接确认时间段
        for (String deep : list) {
            Map<String, String> stringStringMap = matchOrSplitTimeRange(stageMap, deep);
            map.putAll(stringStringMap);
        }
        // 有效的时间段
        Map<String, String> mergedMap = mergeContinuousTimeRanges(map, "00:00", "24:00");
        return spiltDeepTimeRange(mergedMap);
    }

    /**
     * 确定深谷列名
     * @param timeRangeMap
     * @return
     */
    public List<String> spiltDeepTimeRange(Map<String, String> timeRangeMap) {
        List<String> resultList = new ArrayList<>();
        String prevEndTime = null; // 记录上一条记录的结束时间

        for (Map.Entry<String, String> entry : timeRangeMap.entrySet())  {
            String[] times = entry.getKey().split("-");
            String startTime = times[0];
            String endTime = times[1];
            String value = entry.getValue();

            // 生成抄表数据记录
            if (prevEndTime == null || !startTime.equals(prevEndTime))  {
                resultList.add(startTime  + "抄表数据（kwh）"); // 起始时间记录
            }
            resultList.add(endTime  + "抄表数据（kwh）"); // 结束时间记录
            resultList.add(startTime  + "-" + endTime + "电量（kwh）#" + value); // 电量记录

            prevEndTime = endTime; // 更新前一个结束时间
        }
        return resultList;
    }


    /**
     * 合并相同时间段
     * @param timeRangeMap
     * @param inputRange
     * @return
     */
    public Map<String, String> matchOrSplitTimeRange(Map<String, String> timeRangeMap, String inputRange) {
        Map<String, String> resultMap = new LinkedHashMap<>();
        String[] inputTimes = inputRange.split("-");
        String inputStart = inputTimes[0];
        String inputEnd = inputTimes[1];

        // 遍历原始时间段Map
        for (Map.Entry<String, String> entry : timeRangeMap.entrySet())  {
            String currentRange = entry.getKey();
            String[] currentTimes = currentRange.split("-");
            String currentStart = currentTimes[0];
            String currentEnd = currentTimes[1];

            // Case 1: 输入时间段完全包含在当前时间段内
            if (inputStart.compareTo(currentStart)  >= 0 && inputEnd.compareTo(currentEnd)  <= 0) {
                resultMap.put(inputRange,  entry.getValue());
                return resultMap; // 直接返回匹配结果
            }

            // Case 2: 输入时间段部分覆盖当前时间段（需拆分）
            if (inputStart.compareTo(currentStart)  >= 0 && inputStart.compareTo(currentEnd)  < 0) {
                // 拆分出当前时间段内的部分
                String tempTime =  inputEnd.compareTo(currentEnd)  < 0 ? inputEnd : currentEnd;
                String matchedRange = inputStart + "-" + tempTime;
                resultMap.put(matchedRange,  entry.getValue());

                // 更新剩余需要匹配的时间段
                if (inputEnd.compareTo(currentEnd)  > 0) {
                    inputStart = currentEnd; // 剩余部分从当前结束时间开始
                } else {
                    break; // 已完全匹配
                }
            }
        }
        return resultMap;
    }

    /**
     * 过滤到时间深谷时间外的时间范围
     * @param timeRangeMap
     * @return
     */
    public Map<String, String> mergeContinuousTimeRanges(Map<String, String> timeRangeMap,String startTime,String endTime) {
        Map<String, String> mergedMap = new LinkedHashMap<>(); // 保持顺序
        if (timeRangeMap.isEmpty())  return mergedMap;

        // 初始化第一个条目
        String prevKey = null;
        String prevValue = null;
        String[] prevRange = null;

        for (Map.Entry<String, String> entry : timeRangeMap.entrySet())  {
            String currentKey = entry.getKey();
            String currentValue = entry.getValue();
            String[] currentRange = currentKey.split("-");

            // 跳过不符合时间范围的数据（11:30前或23:40后）
            if (currentRange[0].compareTo(startTime) < 0 || currentRange[0].compareTo(endTime) > 0) {
                continue;
            }

            // 如果是第一个有效条目，直接存入
            if (prevKey == null) {
                prevKey = currentKey;
                prevValue = currentValue;
                prevRange = currentRange;
                mergedMap.put(prevKey,  prevValue);
                continue;
            }

            // 检查是否可合并：时间连续且Value相同
            if (prevRange[1].equals(currentRange[0]) && prevValue.equals(currentValue))  {
                // 合并时间段（如 "11:30-12:00" + "12:00-12:30" → "11:30-12:30"）
                String mergedKey = prevRange[0] + "-" + currentRange[1];
                // 更新Map中已存入的数据
                mergedMap.remove(prevKey);
                mergedMap.put(mergedKey,  currentValue);
                // 更新前一个条目的引用
                prevKey = mergedKey;
                prevRange = new String[]{prevRange[0], currentRange[1]};
            } else {
                // 不可合并，直接存入新条目
                prevKey = currentKey;
                prevValue = currentValue;
                prevRange = currentRange;
                mergedMap.put(prevKey,  prevValue);
            }
        }
        return mergedMap;
    }

    /**
     * 时段转化为map
     * @param entity
     * @return
     */
    public Map<String, String> convertToTimeRangeMap(CiesMarketStageEntity entity) {
        Map<String, String> timeRangeMap = new LinkedHashMap<>(); // 保持插入顺序
        try {
            Class<?> clazz = entity.getClass();

            // 遍历所有字段
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                String fieldName = field.getName();

                // 匹配以"period_"开头的字段
                if (fieldName.startsWith("period_")) {
                    // 提取数字部分（如 "0005"）
                    String numbers = fieldName.substring(7);  // 去掉"period_"

                    // 转换为时间段格式（如 "00:00-00:30"）
                    String timeRange = convertNumbersToTimeRange(numbers);

                    // 将字段值存入Map
                    String value = (String) field.get(entity);
                    timeRangeMap.put(timeRange, value);
                }
            }
        }catch (IllegalAccessException e){
            log.error("计算深谷失败，失败原因：{}",e.getMessage());
            throw new BusinessException("计算深谷失败，失败原因："+e.getMessage());
        }
        return timeRangeMap;
    }

    /**
     * 将 "0005" 转换为 "00:00-00:30"
     * @param numbers
     * @return
     */
    private String convertNumbersToTimeRange(String numbers) {
        int startHour = Integer.parseInt(numbers.substring(0,  2)) / 10; // 前两位转小时
        int startMin = Integer.parseInt(numbers.substring(2,  4)) / 10; // 后两位转分钟

        int endHour = startHour;
        int endMin = startMin + 30;
        if (endMin >= 60) {
            endHour += 1;
            endMin -= 60;
        }

        return String.format("%02d:%02d-%02d:%02d",  startHour, startMin, endHour, endMin);
    }

    /**
     *拷贝结算单信息
     * @param response
     * @param entity
     */
    private void copyBillDetail(CiesSettlementBillDetailResponse response,CiesEssMonthlyBillEntity entity){
        response.setPartyARatio(entity.getPartyARatio());
        response.setPartyBRatio(entity.getPartyBRatio());
        response.setPartyAAmount(entity.getPartyAAmount());
        response.setPartyBAmount(entity.getPartyBAmount());
        response.setTaxAdjustmentRate(entity.getTaxAdjustmentRate());
        response.setTaxAdjustmentAmount(entity.getTaxAdjustmentAmount());
        response.setPartyAIncome(entity.getPartyAIncome());
        response.setPartyBIncome(entity.getPartyBIncome());
    }



    /**
     * 计算目标时间到当前时间的剩余时间（格式：X天X时X分）
     * @param targetDateTime 目标时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 剩余时间字符串（如 "3天15时27分"）
     */
//    private  String calculateTimeRemaining(String targetDateTime) {
//        if (StringUtils.isEmpty(targetDateTime) || "-".equals(targetDateTime)){
//            return null;
//        }
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd  HH:mm:ss");
//        LocalDateTime target = LocalDateTime.parse(targetDateTime,  formatter);
//        LocalDateTime now = LocalDateTime.now();
//
//        Duration duration = Duration.between(now,  target);
//        long days = duration.toDays();
//        long hours = duration.toHours()  % 24;
//        long minutes = duration.toMinutes()  % 60;
//
//        // 格式化为 "X天X时X分"
//        return String.format("%d天%d时%d分", days, hours, minutes);
//    }

    /**
     * 获取电价
     * @param timeRange
     * @param elecPriInfo
     * @return
     */
    private BigDecimal getElecPrice(String timeRange,CiesMarketElecPriEntity elecPriInfo){
        switch (timeRange) {
            case "峰": // 峰时段
                return elecPriInfo.getHighPeriodPrice()  != null ? elecPriInfo.getHighPeriodPrice()  : BigDecimal.ZERO;
            case "平": // 平时段
                return elecPriInfo.getFlatPeriodPrice()  != null ? elecPriInfo.getFlatPeriodPrice()  : BigDecimal.ZERO;
            case "谷": // 谷时段
                return elecPriInfo.getValleyPeriodPrice()  != null ? elecPriInfo.getValleyPeriodPrice()  : BigDecimal.ZERO;
            case "尖": // 尖峰时段
                return elecPriInfo.getPeakPeriodPrice()  != null ? elecPriInfo.getPeakPeriodPrice()  : BigDecimal.ZERO;
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 计算收益
     *
     * @param peakValleyRevenue
     * @param projectId
     * @param billId
     */
    private CiesSettlementBillGenerateResponse calIncome(BigDecimal peakValleyRevenue, String projectId, String billId) {
        CiesEssMonthlyBillIncomeRequest request = new CiesEssMonthlyBillIncomeRequest();
        // 获取项目的项目信息
        CiesProjectInfoEntity projectInfo = iCiesProjectInfoService.getById(projectId);
        String templateType = projectInfo.getTemplateType();
        // 甲方收益/甲方分成金额（元）】
        BigDecimal partyAIncome = peakValleyRevenue.multiply(projectInfo.getPartyARatio().divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);
        request.setPartyAIncome(partyAIncome);
        // 乙方收益/乙方分成金额（元）
        BigDecimal partyBIncome = peakValleyRevenue.multiply(projectInfo.getPartyBRatio().divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);
        request.setPartyBIncome(partyBIncome);
        if (ElectricityPriceTypeEnum.WITH_TAX_DIFF_AND_DEEP_VALLEY.getCode().equals(templateType)) {
            request.setPartyAAmount(partyAIncome);
            request.setPartyBAmount(partyBIncome);
            // 税差金额(元) = 【乙方分成金额（元）】*【税差计算比例】
            BigDecimal taxAdjustmentAmount = partyBIncome.multiply(projectInfo.getTaxAdjustmentRate().divide(new BigDecimal(100))).setScale(2, RoundingMode.HALF_UP);
            // 甲方收益(元)
            partyAIncome = partyAIncome.add(taxAdjustmentAmount);
            request.setPartyAIncome(partyAIncome);
            // 乙方收益(元)
            partyBIncome = partyBIncome.subtract(taxAdjustmentAmount);
            request.setPartyBIncome(partyBIncome);
            // 税差金额
            request.setTaxAdjustmentRate(projectInfo.getTaxAdjustmentRate());
            request.setTaxAdjustmentAmount(taxAdjustmentAmount);
        }
        request.setPartyARatio(projectInfo.getPartyARatio());
        request.setPartyBRatio(projectInfo.getPartyBRatio());
        request.setUpdateBy("服务器");
        request.setEssBillId(billId);
        iCiesEssMonthlyBillService.updateMonthlyBill(request);
        CiesSettlementBillGenerateResponse response = BeanCopyUtil.copyProperties(request, CiesSettlementBillGenerateResponse::new);
        response.setProjectName(projectInfo.getProName());
        response.setPartyAName(projectInfo.getPartyAName());
        response.setPartyBName(projectInfo.getPartyBName());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy 年M月d日");
        String chineseDate = LocalDate.now().format(formatter);
        response.setGenerateDate(chineseDate);
        return response;
    }

    /**
     * 获取倒推12个月
     * @param baseMonth
     * @return
     */
    public  List<String> getPrevious12Months(String baseMonth) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth startMonth = YearMonth.parse(baseMonth,  formatter);
        List<String> months = new ArrayList<>();

        for (int i = 11; i >= 0; i--) {
            YearMonth month = startMonth.minusMonths(i);
            months.add(month.format(formatter));
        }
        return months;
    }
}
