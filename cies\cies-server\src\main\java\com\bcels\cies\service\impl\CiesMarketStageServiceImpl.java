package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcels.cies.repository.entity.CiesMarketStageEntity;
import com.bcels.cies.repository.mapper.CiesMarketStageMapper;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesMarketStageRecordRequest;
import com.bcels.cies.response.CiesMarketStageResponse;
import com.bcels.cies.service.ICiesMarketStageService;
import com.zwy.common.utils.BeanCopyUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 市场分时阶段 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class CiesMarketStageServiceImpl extends ServiceImpl<CiesMarketStageMapper, CiesMarketStageEntity> implements ICiesMarketStageService {


    @Autowired
    private CiesMarketStageMapper ciesMarketStageMapper;
    @Override
    public List<CiesMarketStageEntity> findMarketStageList(String date, String projectId) {
        QueryWrapper<CiesMarketStageEntity> queryWrapper = new QueryWrapper<>();
        // 日期"2024-04 "切换为"4月"
        String[] split = date.split("/");
        String result = split[1].contains("0")
                ? split[1].replace("0", "") + "月"
                : split[1] + "月";

        queryWrapper.eq("year", split[0]);
        queryWrapper.eq("month", result);
        queryWrapper.eq("project_id", projectId);
        return list(queryWrapper);
    }

    @Override
    public List<CiesMarketStageResponse> findStageInfoByYear(CiesMarketConfigRequest request) {
        List<CiesMarketStageEntity> stageInfoByYear = ciesMarketStageMapper.findStageInfoByYear(request.getProjectId(), request.getYear());
        if (CollectionUtils.isEmpty(stageInfoByYear)) {
            return null;
        }
        return BeanCopyUtil.copyListProperties(stageInfoByYear, CiesMarketStageResponse::new);
    }

    @Override
    public void deleteStage(CiesMarketStageRecordRequest request) {
        QueryWrapper<CiesMarketStageEntity> queryWrapper= new QueryWrapper();
        queryWrapper.eq(StringUtils.isNotBlank(request.getProjectId()), "project_id", request.getProjectId());
        queryWrapper.eq(request.getYear()!=0, "year", request.getYear());
        remove(queryWrapper);
    }
}
