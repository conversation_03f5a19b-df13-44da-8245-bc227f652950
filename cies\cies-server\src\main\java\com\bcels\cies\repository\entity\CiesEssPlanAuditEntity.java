package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@TableName("cies_ess_plan_audit")
@ApiModel(value = "CiesEssPlanAuditEntity对象", description = "储能计划下发审核表")
public class CiesEssPlanAuditEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("储能计划下发审核主键")
    @TableId
    private String dispatchAuditId;

    @ApiModelProperty("计划日期")
    private String planDate;

    @ApiModelProperty("是否调整")
    private Integer isAdjusted;

    @ApiModelProperty("计划审核状态")
    private String auditStatus;

    @ApiModelProperty("计划下发状态")
    private String dispatchStatus;

    @ApiModelProperty("本日充电电量(kwh)")
    private BigDecimal dailyChargeEnergy;

    @ApiModelProperty("本日放电电量(kwh)")
    private BigDecimal dailyDischargeEnergy;

    @ApiModelProperty("本日充电成本（元）")
    private BigDecimal dailyChargeCost;

    @ApiModelProperty("本日放电收入（元）")
    private BigDecimal dailyDischargeIncome;

    @ApiModelProperty("本日充放电利润（元）")
    private BigDecimal dailyProfit;

    @ApiModelProperty("审核人")
    private String reviewBy;

    @ApiModelProperty("审核时间")
    private LocalDateTime reviewTime;

    @ApiModelProperty("项目主键")
    private String projectId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
