package com.zwy.common.utils.exception;


import com.zwy.common.utils.bean.ErrorCode;

/**
 * @Author: J.T.
 * @Date: 2021/10/12 11:18
 * @Version 1.0
 * 参数异常
 */
public class ParamException extends RuntimeException {

    private ErrorCode errorCode;

    public ParamException() {
        super();
    }

    public ParamException(String message) {
        super(message);
    }

    public ParamException(String message, Throwable cause) {
        super(message, cause);
    }

    public ParamException(Throwable cause) {
        super(cause);
    }

    public ParamException(ErrorCode errorCode) {
        super();
        this.errorCode = errorCode;
    }

    public ParamException(String message, ErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public ParamException(String message, ErrorCode errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public ParamException(Throwable cause, ErrorCode errorCode) {
        super(cause);
        this.errorCode = errorCode;
    }


}
