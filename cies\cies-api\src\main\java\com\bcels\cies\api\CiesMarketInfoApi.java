package com.bcels.cies.api;

import com.alibaba.fastjson.JSONObject;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesMarketElecPriRecordRequest;
import com.bcels.cies.request.CiesMarketStageRecordRequest;
import com.bcels.cies.response.CiesMarketElecPriListResponse;
import com.bcels.cies.response.CiesMarketListResponse;
import com.bcels.cies.response.CiesMarketStageListResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;

@Tag(name = "6、用户代购电API")
@FeignClient(name = "marketConfig", path = "/marketConfig/v1")
public interface CiesMarketInfoApi {
    @Operation(summary = "用户代购电列表")
    @PostMapping("page")
    ResultData<PageResponse<CiesMarketListResponse>> findForPage(@RequestBody CiesMarketConfigRequest request);

    @Operation(summary = "导出用户代购电列表")
    @PostMapping("export")
    ResultData<Void> exportPage(@RequestBody CiesMarketConfigRequest request, HttpServletResponse response) throws IOException;

    @Operation(summary = "展示电价图形")
    @PostMapping("showGraphics")
    ResultData<Map<Double, BigDecimal>> showGraphics(@RequestBody CiesMarketConfigRequest request);

    @Operation(summary = "查看电价")
    @PostMapping("showPrice")
    ResultData<CiesMarketElecPriListResponse> findElecPriByYear(@RequestBody CiesMarketConfigRequest request);

    @Operation(summary = "添加电价")
    @PostMapping("addElecPrice")
    ResultData<Void> addElecPriByYear(@RequestBody CiesMarketElecPriRecordRequest request);

    @Operation(summary = "查看分时阶段")
    @PostMapping("showStage")
    ResultData<CiesMarketStageListResponse> findStageByYear(@RequestBody CiesMarketConfigRequest request);

    @Operation(summary = "添加分时阶段")
    @PostMapping("addStage")
    ResultData<Void> addStageByYear(@RequestBody CiesMarketStageRecordRequest request);

    @Operation(summary = "导出分时阶段")
    @PostMapping("exportStage")
    ResultData<Void> exportStageList(@RequestBody CiesMarketConfigRequest request, HttpServletResponse response) throws IOException;

    @Operation(summary = "上传分时阶段文件")
    @PostMapping("upload")
    ResultData<String> upload(@RequestPart("file") MultipartFile file);

    @Operation(summary = "解析excel")
    @PostMapping("parseExcel")
    ResultData<Void> parseExcel(@RequestBody CiesMarketConfigRequest request);

    @Operation(summary = "应用本月/全年")
    @PostMapping("usePeriod")
    ResultData<Void> marketPeriodCorrelation(@RequestBody CiesMarketConfigRequest request);

    @Operation(summary = "时段模版下载")
    @PostMapping("downloadTemplate")
    ResultData<Void> downloadTemplate(HttpServletResponse response);

    @Operation(summary = "查询市场信息(图形)")
    @PostMapping("queryMarketInfo")
    ResultData<JSONObject> queryMarketInfo(@RequestBody CiesMarketConfigRequest request);

    @Operation(summary = "应用上月价格")
    @PostMapping("useLastPrice")
    ResultData<Void> useLastPrice(@RequestBody CiesMarketConfigRequest request);
}
