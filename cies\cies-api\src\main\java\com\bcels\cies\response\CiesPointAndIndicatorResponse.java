package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "测点和指标（数据中台）")
public class CiesPointAndIndicatorResponse implements Serializable {

    @Schema(description = "通道名称 ")
    private String channelName;

    @Schema(description = "通道ID ")
    private String cId;

    @Schema(description = "设备名称")
    private String equipName;

    @Schema(description = "设备ID")
    private String equipId;

    @Schema(description = "测点名称")
    private String testPointName;

    @Schema(description = "关联指标ID")
    private String relateIndicatorId;

    @Schema(description = "关联指标名称")
    private String relateIndicatorName;
}
