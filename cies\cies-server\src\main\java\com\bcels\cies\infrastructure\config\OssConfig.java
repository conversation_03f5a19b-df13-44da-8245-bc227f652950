/*
 * Copyright © 2024 富鸿资本（湖南）融资租赁有限公司 版权所有
 */

package com.bcels.cies.infrastructure.config;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.oss.model.VoidResult;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OssConfig {

    public static final String SINGLE_SLASH = "/";

    @Value("${mftcc.aliyun.oss.endpoint}")
    private String ALIYUN_OSS_ENDPOINT ;
    @Value("${mftcc.aliyun.oss.accessKeyId}")
    private String ALIYUN_OSS_ACCESSKEYID;
    @Value("${mftcc.aliyun.oss.accessKeySecret}")
    private String ALIYUN_OSS_ACCESSKEYSECRET ;
    @Value("${mftcc.aliyun.oss.bucketName}")
    private String BUCKET_NAME ;



    public OSS OssClient(){
        OSS ossClient = new OSSClientBuilder().build(this.ALIYUN_OSS_ENDPOINT, this.ALIYUN_OSS_ACCESSKEYID, this.ALIYUN_OSS_ACCESSKEYSECRET);
        return ossClient;
    }

    /**
     * 上传字节数组到 OSS，返回文件名
     */
    public String writeOss(String objectName, byte[] bytes) {
        OSS ossClient = this.OssClient();
        try {
            ossClient.putObject(this.BUCKET_NAME,  objectName, new ByteArrayInputStream(bytes));
            return objectName.substring(objectName.lastIndexOf("/")); // 直接返回文件名
        } catch (Exception e) {
            throw new RuntimeException("上传失败！", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();  // 确保关闭 OSSClient
            }
        }
    }

    /**
     * 上传输入流到 OSS，返回文件名
     */
    public String writeOss(String objectName, InputStream inputStream) {
        OSS ossClient = OssClient();
        try {
            ossClient.putObject(this.BUCKET_NAME,  objectName, inputStream);
            return objectName.substring(objectName.lastIndexOf("/")); // 直接返回文件名
        } catch (Exception e) {
            throw new RuntimeException("上传失败！", e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();  // 确保关闭 OSSClient
            }
        }
    }

    public InputStream readOss(String filePath){
        OSS ossClient = OssClient();
        try{
            OSSObject ossObject = ossClient.getObject(this.BUCKET_NAME,filePath);
            return ossObject.getObjectContent();
        }catch (Exception e){
            throw new RuntimeException("读取失败",e);
        }finally {
            if (ossClient != null) {
                ossClient.shutdown();  // 确保关闭 OSSClient
            }
        }
    }

    public void deteleOss(String filePath){
        OSS ossClient = OssClient();
        try{
            VoidResult voidResult = ossClient.deleteObject(this.BUCKET_NAME,filePath);
        }catch (Exception e){
            throw new RuntimeException("删除失败",e);
        }finally {
            if (ossClient != null) {
                ossClient.shutdown();  // 确保关闭 OSSClient
            }
        }
    }

    public void clientClose(){
        OSS ossClient = OssClient();
        ossClient.shutdown();
    }

    /**
     * 下载OSS文件
     * @param objectName OSS文件路径
     * @param response HttpServletResponse对象
     * @param originalFileName 下载时显示的文件名
     */
    public void downloadFile(String objectName, HttpServletResponse response, String originalFileName) {
        OSS ossClient = this.OssClient();
        try (OSSObject ossObject = ossClient.getObject(BUCKET_NAME,  objectName);
             BufferedInputStream inputStream = new BufferedInputStream(ossObject.getObjectContent());
             BufferedOutputStream outputStream = new BufferedOutputStream(response.getOutputStream()))  {

            // 设置响应头（强制下载+文件名编码，防止中文乱码）
            response.reset();
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(originalFileName,  "UTF-8"));

            //  获取文件大小并设置响应头（可选）
            long fileSize = ossObject.getObjectMetadata().getContentLength();
            response.setHeader("Content-Length",  String.valueOf(fileSize));

            // 流式传输文件内容
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer))  != -1) {
                outputStream.write(buffer,  0, bytesRead);
            }
            outputStream.flush();
        } catch (Exception e) {
            throw new RuntimeException("文件下载失败: " + objectName, e);
        }finally {
            if (ossClient != null) {
                ossClient.shutdown();  // 确保关闭 OSSClient
            }
        }
    }

    public  String upload(MultipartFile file, String fileDir) {

         String fileName = null;
        String targetName = null;
        if (file.isEmpty()) {
            log.info("上传文件为空");
        }

        // 使用OSS SDK上传文件
        OSS ossClient = this.OssClient();

        try {
            // 获取文件名
            String orgName = file.getOriginalFilename();
            if ("" == orgName) {
                orgName = file.getName();
            }
            fileName = orgName.indexOf(".") == -1
                    ? orgName + "_" + System.currentTimeMillis()
                    : orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.lastIndexOf("."));
            if (!fileDir.endsWith(SINGLE_SLASH)) {
                fileDir = fileDir.concat(SINGLE_SLASH);
            }
            // 构建目标路径
            targetName = fileDir + fileName;
            PutObjectResult result = ossClient.putObject(BUCKET_NAME, targetName, file.getInputStream());
            if (result != null) {
                log.info("------OSS文件上传成功------" + fileName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ossClient.shutdown();
        }
        return fileName;
    }
}