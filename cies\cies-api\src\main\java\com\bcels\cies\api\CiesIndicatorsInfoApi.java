package com.bcels.cies.api;

import com.bcels.cies.request.CiesIndicatorHisRequest;
import com.bcels.cies.request.CiesIndicatorsInfoListRequest;
import com.bcels.cies.request.CiesIndicatorsInfoUpdateRequest;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "4、测点与指标API")
@FeignClient(name = "ciesIndicators", path = "/ciesIndicators/v1")
public interface CiesIndicatorsInfoApi {

    @Operation(summary = "测点与指标列表")
    @PostMapping("page")
    ResultData<PageResponse<CiesIndicatorsInfoResponse>> findForPage(@RequestBody CiesIndicatorsInfoListRequest request);

    @Operation(summary = "测点与指标编辑")
    @PostMapping("update")
    ResultData<Void> updateIndicator(@RequestBody CiesIndicatorsInfoUpdateRequest request);

    @Operation(summary = "查询单个指标信息")
    @GetMapping("findById")
    @Parameters({
            @Parameter(name = "indicatorId", description = "指标ID", required = true)
    })
    ResultData<CiesIndicatorsInfoResponse> findIndicatorById(@RequestParam("indicatorId") String indicatorId);

    @Operation(summary = "查询项目下的指标和测点")
    @GetMapping("findIndicators")
    @Parameters({
            @Parameter(name = "projectId", description = "项目ID", required = true)
    })
    ResultData<List<CiesIndicatorsInfoResponse>> findIndicators(@RequestParam("projectId") String projectId);
}
