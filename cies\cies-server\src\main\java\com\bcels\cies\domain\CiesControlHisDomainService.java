package com.bcels.cies.domain;

import com.bcels.cies.repository.entity.CiesControlHisRecordsEntity;
import com.bcels.cies.request.CiesControlHisRequest;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.request.CiesQueryStationPlanRequest;
import com.bcels.cies.response.*;
import com.bcels.cies.service.ICiesConnectionPointService;
import com.bcels.cies.service.ICiesControlHisRecordsService;
import com.bcels.cies.service.ICiesEnergyStoragePlanService;
import com.bcels.cies.service.ICiesProjectInfoService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.bean.PageResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Service
public class CiesControlHisDomainService {


    @Autowired
     private ICiesControlHisRecordsService iCiesControlHisRecordsService;

    @Autowired
    private ICiesProjectInfoService iCiesProjectInfoService;

    @Autowired
    private ICiesEnergyStoragePlanService iCiesEnergyStoragePlanService;

    @Autowired
    private ICiesConnectionPointService iCiesConnectionPointService;

    /**
     * 查询场站控制记录（分页）
     * @param request
     * @return
     */
    public PageResponse<CiesControlHisRecordsListResponse> findControlHisRecordsForPage(CiesControlHisRequest request){
        return iCiesControlHisRecordsService.findControlHisRecordsForPage(request);
    }

    /**
     * 查看详情（除并网点计划）
     * @param historyRecordId 历史记录id
     * @return
     */
    public CiesControlHisRecordsResponse findControlHisDetail(String historyRecordId){
        CiesControlHisRecordsResponse response = new CiesControlHisRecordsResponse();
        // 获取基础信息
        CiesControlHisRecordsEntity entity = iCiesControlHisRecordsService.getById(historyRecordId);
        response.setControlMode(entity.getControlMode());
        response.setDistributeTime(entity.getCreateTime());
        CiesProjectInfoResponse enterpriseInfo = iCiesProjectInfoService.findEnterpriseByProjectId(entity.getProjectId());
        response.setEnterpriseName(enterpriseInfo.getEnterpriseName());
        response.setProName(enterpriseInfo.getProName());
        // 获取下一指令覆盖时间
        List<CiesControlHisRecordsListResponse> controlHisRecord = iCiesControlHisRecordsService.findControlHisRecord(entity.getProjectId(),entity.getCreateTime());
        if (!CollectionUtils.isEmpty(controlHisRecord)) {
            response.setNextInstructionCoverTime(controlHisRecord.get(0).getCreateTime());
        }
        // 获取全场站储能计划
        CiesEnergyPlanListRequest request = new CiesEnergyPlanListRequest();
        request.setHistoryRecordId(historyRecordId);
        List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
        response.setPlanResponseList(stationEnergyPlan);
        return response;
    }


    /**
     * 查看并网点计划历史记录
     * @param historyRecordId
     * @return
     */
    public CiesInitConnPlanResponse findConnHisPlan(String historyRecordId){

        CiesControlHisRecordsEntity entity = iCiesControlHisRecordsService.getById(historyRecordId);
        CiesInitConnPlanResponse result = BeanCopyUtil.copyProperties(entity, CiesInitConnPlanResponse::new);
        String connId;
        // 查询并网点
        List<CiesConnectionPointResponse> connInfo = iCiesConnectionPointService.findByrHisRecordId(historyRecordId);
        connInfo.sort(Comparator.comparing(CiesConnectionPointResponse::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(
                CiesConnectionPointResponse::getConnectionPointName,
                Comparator.nullsLast(Comparator.naturalOrder())
        ));
        result.setConn(connInfo);
        // 查询第一个并网点对应计划
        CiesEnergyPlanListRequest planListRequest =new CiesEnergyPlanListRequest();
        if (StringUtils.isEmpty(historyRecordId)){
            connId = connInfo.get(0).getConnectionPointId();
        }else{
            connId = historyRecordId;
        }
        planListRequest.setConnectionPointId(connId);
        result.setConnPlan(iCiesEnergyStoragePlanService.findStationEnergyPlan(planListRequest));
        return result;
    }



    /**
     * 查看全场站计划历史记录
     * @param historyRecordId
     * @return
     */
    public CiesFullStationResponse findStationHisPlan(String historyRecordId){
        CiesControlHisRecordsEntity entity = iCiesControlHisRecordsService.getById(historyRecordId);
        CiesFullStationResponse result = BeanCopyUtil.copyProperties(entity, CiesFullStationResponse::new);
        // 获取全场站储能计划
        CiesEnergyPlanListRequest request = new CiesEnergyPlanListRequest();
        request.setHistoryRecordId(historyRecordId);
        List<CiesEnergyStoragePlanResponse> stationEnergyPlan = iCiesEnergyStoragePlanService.findStationEnergyPlan(request);
        result.setPlanResponseList(stationEnergyPlan);
        return result;
    }
}
