package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.repository.entity.CiesEssPlanAuditEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.response.CiesEnergyPlanAuditResponse;
import com.bcels.cies.response.CiesEnergyPlanListResponse;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 储能计划下发审核表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Mapper
public interface CiesEssPlanAuditMapper extends BaseMapper<CiesEssPlanAuditEntity> {

    IPage<CiesEnergyPlanListResponse>  findProjectInfoForPage(@Param("page") Page<CiesEnergyPlanListResponse> page, @Param("request") CiesEnergyPlanListRequest request);


    CiesEnergyPlanAuditResponse findPlanAuditInfo(@Param("projectId") String projectId, @Param("planDate") String planDate);
}
