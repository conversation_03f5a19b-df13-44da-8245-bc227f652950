package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ToString
@Schema(description = "场站控制详情")
public class CiesControlHisRecordsResponse implements Serializable {

    @Schema(description = "历史记录ID")
    private String historyRecordId;

    @Schema(description = "企业")
    private String enterpriseName;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "控制模式")
    private String controlMode;

    @Schema(description = "下发时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime distributeTime;

    @Schema(description = "下一指令覆盖时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextInstructionCoverTime;

    @Schema(description = "时间维度")
    private String timeDimension;

    @Schema(description = "下发规则")
    private String dispatchRule;

    @Schema(description = "是否支持手动控制")
    private String manualControlEnabled;

    @Schema(description = "当日覆盖规则")
    private String dailyOverrideRule;

    @Schema(description = "是否调整")
    private Integer isAdjusted;

    @Schema(description = "储能计划")
    List<CiesEnergyStoragePlanResponse> planResponseList;
}
