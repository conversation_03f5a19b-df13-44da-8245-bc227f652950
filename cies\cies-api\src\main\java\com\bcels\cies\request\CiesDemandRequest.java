package com.bcels.cies.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.zwy.common.utils.bean.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "需量下发")
public class CiesDemandRequest extends PageRequest implements Serializable {

    @Schema(description = "并网点名称")
    private String connectionPointName;

    @Schema(description = "上次下发需量（KW）")
    private BigDecimal lastDistributionDemand;

    @Schema(description = "上次下发时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastDistributionTime;

    @Schema(description = "本次下发需量（KW）")
    private BigDecimal distributeValue;
    
    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "并网点id")
    private String connectionPointId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}
