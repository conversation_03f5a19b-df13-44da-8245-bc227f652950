package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bcels.cies.repository.entity.CiesPeakValleyPriceGapEntity;
import com.bcels.cies.repository.mapper.CiesPeakValleyPriceGapMapper;
import com.bcels.cies.request.CiesPeakValleyPriceGapRequest;
import com.bcels.cies.response.CiesPeakValleyPriceGapResponse;
import com.bcels.cies.service.ICiesPeakValleyPriceGapService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.enums.YesOrNo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 储能峰谷价差信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class CiesPeakValleyPriceGapServiceImpl extends ServiceImpl<CiesPeakValleyPriceGapMapper, CiesPeakValleyPriceGapEntity> implements ICiesPeakValleyPriceGapService {


    @Autowired
    private CiesPeakValleyPriceGapMapper ciesPeakValleyPriceGapMapper;
    @Override
    public List<CiesPeakValleyPriceGapResponse> queryPriceGap(CiesPeakValleyPriceGapRequest request) {
        return ciesPeakValleyPriceGapMapper.queryPriceGap(request);
    }

    @Override
    public void deleteByBillId(String billId) {
        QueryWrapper<CiesPeakValleyPriceGapEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ess_bill_id",billId);
        ciesPeakValleyPriceGapMapper.delete(queryWrapper);
    }
}
