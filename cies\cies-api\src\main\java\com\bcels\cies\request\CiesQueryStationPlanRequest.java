package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@Schema(description = "查询全场站计划")
public class CiesQueryStationPlanRequest implements Serializable {

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "计划日期")
    private String planDate;

    @Schema(description = "并网点主键")
    private String connectionPointId;

    @Schema(description = "历史记录ID")
    private String historyRecordId;
}
