package com.bcels.cies.domain;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bcels.cies.emuns.PeriodTypeEnum;
import com.bcels.cies.emuns.TimeSlotEnum;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.infrastructure.config.OssConfig;
import com.bcels.cies.infrastructure.utils.BatchConverter;
import com.bcels.cies.infrastructure.utils.ExcelExportUtil;
import com.bcels.cies.infrastructure.utils.ExcelPoiParser;
import com.bcels.cies.infrastructure.utils.MarketApiUtil;
import com.bcels.cies.model.CiesMarketListExport;
import com.bcels.cies.model.CiesMarketStageExport;
import com.bcels.cies.model.CiesMarketStageImport;
import com.bcels.cies.model.TimeRange;
import com.bcels.cies.repository.entity.CiesMarketElecPriEntity;
import com.bcels.cies.repository.entity.CiesMarketStageEntity;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesMarketElecPriRecordRequest;
import com.bcels.cies.request.CiesMarketStageRecordRequest;
import com.bcels.cies.response.*;
import com.bcels.cies.service.ICiesMarketElecPriService;
import com.bcels.cies.service.ICiesMarketStageService;
import com.bcels.cies.service.ICiesProjectInfoService;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.IdGenUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import com.zwy.common.utils.exception.BusinessException;
import io.swagger.annotations.ApiModelProperty;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bcels.cies.emuns.PeriodTypeEnum.*;

@Slf4j
@Service
public class CiesMarketDomainService {

    /**
     * 时段模版名称
     */
    private static final String FILE_NAME = "用户代购电-时段模版.xlsx";

    private static final String SINGLE_SLASH = "/";

    /**
     * 市场上传市场文件基础路径
     */
    @Value("${market.uploadUrl}")
    private String baseUrl;

    /**
     * 区间分时电价图表地址
     */
    private static final String ELE_TYPE_URL = "/eesa-report/electricityPrice/minutesAndMonths/api/v4/getSeparationTimeElectricityPrice";

    /**
     * 获取分时电价（按月）
     */
    private static final String MONTH_ELE_TYPE_URL = "/eesa-report/electricityPrice/minutesAndMonths/api/v4/getMonthlyElectricityPrices";

    private static final int SUCCESS = 0;


    @Autowired
    private ICiesProjectInfoService iCiesProjectInfoService;

    @Autowired
    private ICiesMarketStageService iCiesMarketStageService;

    @Autowired
    private ICiesMarketElecPriService iCiesMarketElecPriService;

    @Autowired
    private OssConfig ossConfig;

    @Autowired
    private MarketApiUtil marketApiUtil;

    @Value("${mftcc.aliyun.oss.downloadUrl}")
    private String downloadUrl;

    /**
     * 按条件查询用户代购电
     *
     * @param request
     * @return
     */
    public PageResponse<CiesMarketListResponse> findForPage(CiesMarketConfigRequest request) {
        // 分页查询项目
        PageResponse<CiesProjectInfoResponse> projectInfoForPage = iCiesProjectInfoService.findMarketProjectInfoForPage(request);
        List<CiesProjectInfoResponse> projectInfoList = projectInfoForPage.getPageData();
        if (CollectionUtils.isEmpty(projectInfoList)) {
            return null;
        }
        List<CiesMarketListResponse> marketResponseList = BeanCopyUtil.copyListProperties(projectInfoList, CiesMarketListResponse::new);
        marketResponseList.stream().forEach(item -> {
            Map<PeriodTypeEnum, String> periodTypeMap = summaryPeriod(request.getMonth(), item.getProjectId());
            // 分组填充时段
            item.setPeakPeriod(periodTypeMap.get(PeriodTypeEnum.PEAK));
            item.setOffPeakPeriod(periodTypeMap.get(OFF_PEAK));
            item.setHighPeriod(periodTypeMap.get(HIGH));
            item.setNormalPeriod(periodTypeMap.get(NORMAL));

            // 填充分时电价数据
            CiesMarketElecPriEntity elecPriInfo = iCiesMarketElecPriService.findElecPriInfo(request.getMonth(), item.getProjectId());
            if (!ObjectUtils.isEmpty(elecPriInfo)) {
                item.setPeakEnergyPrice(elecPriInfo.getPeakPeriodPrice());
                item.setOffPeakEnergyPrice(elecPriInfo.getValleyPeriodPrice());
                item.setHighEnergyPrice(elecPriInfo.getHighPeriodPrice());
                item.setNormalEnergyPrice(elecPriInfo.getFlatPeriodPrice());
                item.setDemandPrice(elecPriInfo.getDemandPrice());
                item.setCapacityPrice(elecPriInfo.getCapacityPrice());
            }
        });
        // 组装数据
        return new PageResponse<>(projectInfoForPage.getTotal(), projectInfoForPage.getPage(), projectInfoForPage.getPageSize(), marketResponseList);
    }

    /**
     * 用户代购电列表导出
     *
     * @param request
     * @param response
     * @throws IOException
     */
    public void exportPage(CiesMarketConfigRequest request, HttpServletResponse response) throws IOException {
        PageResponse<CiesMarketListResponse> forPage = this.findForPage(request);
        List<CiesMarketListResponse> pageData = forPage.getPageData();

        List<CiesMarketListExport> ciesMarketListExports = BeanCopyUtil.copyListProperties(pageData, CiesMarketListExport::new);

        response.reset();

        String customFileName = "用户代购电" + System.currentTimeMillis() + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        response.setHeader("Content-Disposition", "attachment; filename=" + customFileName);


        byte[] excelBytes = ExcelExportUtil.exportToExcel(ciesMarketListExports, "用户代购电",CiesMarketListExport.class);
        response.getOutputStream().write(excelBytes);
    }

    /**
     * 展示当月电价图形
     *
     * @param request
     * @return
     */
    public Map<Double, BigDecimal> showGraphics(CiesMarketConfigRequest request) {

        // 获取电价
        CiesMarketElecPriEntity elecPriInfo = iCiesMarketElecPriService.findElecPriInfo(request.getMonth(), request.getProjectId());
        if (ObjectUtils.isEmpty(elecPriInfo)) {
            return null;
        }

        // 获取时段
        List<CiesMarketStageEntity> marketStageList = iCiesMarketStageService.findMarketStageList(request.getMonth(), request.getProjectId());
        if (CollectionUtils.isEmpty(marketStageList)) {
            return null;
        }
        Map<String, String> stringBigDecimalMap = mapPeriodFields(marketStageList.get(0));

        LinkedHashMap<Double, BigDecimal> result = new LinkedHashMap<>();
        stringBigDecimalMap.forEach((k, v) -> {
                result.put(extractNumericValue(k), getPriceByPeriodType(elecPriInfo, v));
        });
        result.put(0.0,result.get(0.5));
        return result;
    }


    /**
     * 查看分时电价
     *
     * @param request
     * @return
     */
    public CiesMarketElecPriListResponse findElecPriByYear(CiesMarketConfigRequest request) {

        CiesProjectInfoResponse projectInfo = iCiesProjectInfoService.findProjectInfo(request.getProjectId()).get(0);
        CiesMarketElecPriListResponse response = BeanCopyUtil.copyProperties(projectInfo, CiesMarketElecPriListResponse::new);


        List<CiesMarketElecPriResponse> elecPriInfoByYear = iCiesMarketElecPriService.findElecPriInfoByYear(request);
        if (CollectionUtils.isEmpty(elecPriInfoByYear)) {
            // 默认初始化数据
            elecPriInfoByYear = new ArrayList<>();
            for (int i = 1; i <= 12; i++) {
                CiesMarketElecPriResponse response1 = new CiesMarketElecPriResponse();
                response1.setMonth(i + "月");
                elecPriInfoByYear.add(response1);
            }
        }
        response.setPriResponseList(elecPriInfoByYear);
        return response;
    }

    /**
     * 添加分时电价
     *
     * @param request
     */
    @Transactional
    public void addElecPriByYear(CiesMarketElecPriRecordRequest request) {
        iCiesMarketElecPriService.deleteElecPri(request);
        List<CiesMarketElecPriEntity> ciesMarketElecPriEntities = BeanCopyUtil.copyListProperties(request.getList(), CiesMarketElecPriEntity::new);
        ciesMarketElecPriEntities.stream().forEach(item -> {
            item.setElecPriId(IdGenUtil.genUniqueId());
            item.setUpdateTime(LocalDateTime.now());
            item.setUpdateBy(CiesUserContext.getCurrentUser());
            item.setProjectId(request.getProjectId());
            item.setYear(request.getYear());
        });
        iCiesMarketElecPriService.saveBatch(ciesMarketElecPriEntities);
    }

    /**
     * 查看分时阶段
     *
     * @param request
     * @return
     */
    public CiesMarketStageListResponse findStageByYear(CiesMarketConfigRequest request) {

        CiesProjectInfoResponse projectInfo = iCiesProjectInfoService.findProjectInfo(request.getProjectId()).get(0);
        CiesMarketStageListResponse response = BeanCopyUtil.copyProperties(projectInfo, CiesMarketStageListResponse::new);

        List<CiesMarketStageResponse> stageResponses = iCiesMarketStageService.findStageInfoByYear(request);

        if (CollectionUtils.isEmpty(stageResponses)) {
            // 默认初始化数据
            stageResponses = new ArrayList<>();
            for (int i = 1; i <= 12; i++) {
                CiesMarketStageResponse response1 = new CiesMarketStageResponse();
                response1.setMonth(i + "月");
                stageResponses.add(response1);
            }
        }
        response.setStageResponseList(stageResponses);
        return response;
    }

    /**
     * 添加分时阶段
     *
     * @param request
     */
    @Transactional
    public void addStageByYear(CiesMarketStageRecordRequest request) {
        iCiesMarketStageService.deleteStage(request);
        List<CiesMarketStageEntity> stageEntitiesList = BeanCopyUtil.copyListProperties(request.getList(), CiesMarketStageEntity::new);
        stageEntitiesList.stream().forEach(item -> {
            item.setStageId(IdGenUtil.genUniqueId());
            item.setUpdateTime(LocalDateTime.now());
            item.setUpdateBy(CiesUserContext.getCurrentUser());
            item.setProjectId(request.getProjectId());
            item.setYear(request.getYear());
        });
        iCiesMarketStageService.saveBatch(stageEntitiesList);
    }

    /**
     * 分时阶段导出
     *
     * @param request
     * @param response
     * @throws IOException
     */
    public void exportStageList(CiesMarketConfigRequest request, HttpServletResponse response) throws IOException {
        List<CiesMarketStageResponse> stageResponses = iCiesMarketStageService.findStageInfoByYear(request);

        List<CiesMarketStageExport> ciesMarketStageListExports = BeanCopyUtil.copyListProperties(stageResponses, CiesMarketStageExport::new);
        if (!CollectionUtils.isEmpty(ciesMarketStageListExports)){
            ciesMarketStageListExports.forEach(item->{
                translatePeriodFields(item);
            });
        }
        String fileName = "用户代购电时段_" + System.currentTimeMillis()  + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition",
                "attachment; filename=" + URLEncoder.encode(fileName,  "UTF-8"));

        byte[] excelBytes = ExcelExportUtil.exportToExcel(ciesMarketStageListExports, "用户代购电时段",CiesMarketStageExport.class);
        response.getOutputStream().write(excelBytes);
    }


    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    public String upload(MultipartFile file) {
        try {
            return ossConfig.upload(file, baseUrl);
        } catch (Exception e) {
            throw new BusinessException("上传用户代购电-时段文件失败");
        }
    }

    /**
     * 按照文件名解析excel文件
     *
     * @param request
     * @return
     */
    public void batchParseExcel(CiesMarketConfigRequest request) {
        String filePath = baseUrl.concat(SINGLE_SLASH) + request.getFileName();
        try (InputStream is = ossConfig.readOss(filePath)) {
            // 解析excel
            List<CiesMarketStageImport> dataList = ExcelPoiParser.parse(is, CiesMarketStageImport.class);
            if (CollectionUtils.isEmpty(dataList)){
                return;
            }
            List<Integer> list = dataList.stream().map(CiesMarketStageImport::getYear).toList();
            List<Integer> newList = list.stream().distinct().toList();
             if (newList.size()!=1){
                 throw new BusinessException("导入用户代购电-时段文件填写年份不一致，请重新填写" );
             }
             // 删除历史记录
            CiesMarketStageRecordRequest request1 =new CiesMarketStageRecordRequest();
            request1.setYear(newList.get(0));
            request1.setProjectId(request.getProjectId());
            iCiesMarketStageService.deleteStage(request1);
            List<CiesMarketStageEntity> stageEntities = BeanCopyUtil.copyListProperties(dataList, CiesMarketStageEntity::new);
            BatchConverter.batchConvert(stageEntities);
            stageEntities.forEach(item->{
                item.setProjectId(request1.getProjectId());
                item.setDr(YesOrNo.NO.getCode());
                item.setCreateBy(CiesUserContext.getCurrentUser());
                item.setCreateTime(LocalDateTime.now());
            });
            iCiesMarketStageService.saveBatch(stageEntities);
        } catch (Exception e) {
            log.error(" 解析用户代购电-时段文件失败", e);
            throw new BusinessException("解析用户代购电-时段失败" + e.getMessage());
        }
    }

    /**
     * 模版下载
     *
     * @param response
     */
    public void downloadTemplate(HttpServletResponse response) {
        String downloadPath = downloadUrl + FILE_NAME;
        ossConfig.downloadFile(downloadPath, response, FILE_NAME);
    }

    /**
     * 应用上月电价
     * @param request
     */
    public void useLastPrice(CiesMarketConfigRequest request) {
        if (request.getSourceId()==null || request.getTargetId() == null){
            return;
        }
        CiesMarketElecPriEntity byId = iCiesMarketElecPriService.getById(request.getSourceId());
        if (!ObjectUtils.isEmpty(byId)){
            byId.setElecPriId(request.getTargetId());
            byId.setUpdateTime(LocalDateTime.now());
            byId.setUpdateBy(CiesUserContext.getCurrentUser());
            iCiesMarketElecPriService.updateById(byId);
        }
    }

    /**
     * 查询市场信息（图形）
     *
     * @param request
     * @return
     */
    public JSONObject findMarketData(CiesMarketConfigRequest request) {
        JSONObject result = new JSONObject();
        if (StringUtils.isEmpty(request.getElecArea()) || StringUtils.isEmpty(request.getIndustryType())){
            return null;
        }
        JSONObject datas = getSeparationTimeElectricityPrice(request);
        if (datas == null) {
            return result;
        }
        // 如果包含分时电价数据
        if (datas != null && datas.containsKey("timeElectricityPriceResps")) {
            JSONArray jsonArray = datas.getJSONArray("timeElectricityPriceResps");
            List<String> timePeriodList = new ArrayList<>();
            List<String> lengends = new ArrayList<>();
            JSONArray jsonArray1 = new JSONArray();
            for (int i = 0; i < jsonArray.size(); i++) {
                String[] arr = new String[i + 2];
                Arrays.fill(arr, 0, i, "-");
                JSONObject jsonObject1 = new JSONObject();
                // 填充初始时间
                timePeriodList.add(jsonArray.getJSONObject(i).getString("startTime"));
                // 填充分时阶段
                String periodType = PeriodTypeEnum.getNameByCode(jsonArray.getJSONObject(i).getString("periodType"));
                lengends.add(periodType);
                arr[i] = jsonArray.getJSONObject(i).getBigDecimal("electrovalence").toString();
                arr[i + 1] = jsonArray.getJSONObject(i).getBigDecimal("electrovalence").toString();
                jsonObject1.put("name", periodType);
                jsonObject1.put("data", arr);
                jsonArray1.add(jsonObject1);
            }
            if (!CollectionUtils.isEmpty(timePeriodList)){
                timePeriodList.add("24:00");
            }
            result.put("xAxis", timePeriodList);
            List<String> collect = lengends.stream().distinct().collect(Collectors.toList());
            result.put("legend", collect);
            result.put("list", jsonArray1);
        }
        return result;
    }

    /**
     * 应用本月/全年
     *
     * @param ciesMarketConfigRequest
     */
    @Transactional
    public void marketPeriodCorrelation(CiesMarketConfigRequest ciesMarketConfigRequest) {
        // 应用电价
        applicationMarket(ciesMarketConfigRequest);
    }


    /**
     * 获取并合并时段
     *
     * @param month
     * @return
     */
    private Map<PeriodTypeEnum, String> summaryPeriod(String month, String projectId) {
        List<CiesMarketStageEntity> marketStageList = iCiesMarketStageService.findMarketStageList(month, projectId);
        if (marketStageList == null || marketStageList.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, String> stringMap = mapPeriodFields(marketStageList.get(0));

        // 按照阶段分组，并对时间段汇总成集合
        Map<PeriodTypeEnum, List<TimeRange>> grouped = stringMap.entrySet().stream().filter(e -> e.getValue() != null).collect(Collectors.groupingBy(e -> PeriodTypeEnum.fromCode(e.getValue()), Collectors.mapping(e -> parseTimeRange(e.getKey()), Collectors.toList())));
        // 合并连续时段
        Map<PeriodTypeEnum, String> result = new EnumMap<>(PeriodTypeEnum.class);
        grouped.forEach((type, ranges) -> {
            ranges.sort(Comparator.comparing(TimeRange::getStart));
            result.put(type, mergeContinuousRanges(ranges));
        });
        return result;
    }


    /**
     * 解析字符串为时间范围（如"0时-0.5时" → 00:00-00:30）
     *
     * @param periodStr
     * @return
     */
    private TimeRange parseTimeRange(String periodStr) {
        String[] parts = periodStr.split("-");
        return new TimeRange(parseHour(parts[0]), "24时".equals(parts[1])?LocalTime.parse("23:59"):parseHour(parts[1]));
    }

    /**
     * 合并连续时间段
     *
     * @param ranges
     * @return
     */
    private String mergeContinuousRanges(List<TimeRange> ranges) {
        if (ranges.isEmpty()) return "";

        List<String> merged = new ArrayList<>();
        TimeRange current = ranges.get(0);

        for (int i = 1; i < ranges.size(); i++) {
            if (ranges.get(i).getStart().equals(current.getEnd())) {
                current = new TimeRange(current.getStart(), ranges.get(i).getEnd());
            } else {
                merged.add(current.toString());
                current = ranges.get(i);
            }
        }
        merged.add(current.toString());

        return String.join(",", merged);
    }


    /**
     * 解析时间字符串（支持"0.5时"/"1时"等格式）
     *
     * @param hourStr
     * @return
     */
    private LocalTime parseHour(String hourStr) {
        if (!"24时".equals(hourStr)){
            double hours = Double.parseDouble(hourStr.replace("时", ""));
            int totalMinutes = (int) (hours * 60);
            return LocalTime.of(totalMinutes / 60, totalMinutes % 60);
        }else{
            return LocalTime.of(23,59);
        }
    }

    /**
     * 从periodType中提取数值（如"0时-0.5时" → 0.5）
     */
    private static double extractNumericValue(String periodType) {
        // 截取第一个时之前的数字
        String substring = periodType.substring(periodType.indexOf("-") + 1, periodType.lastIndexOf("时"));
        return Double.parseDouble(substring);  // 取第二部分"0.5"
    }

    /**
     * 根据时段类型获取对应电价
     *
     * @param elecPriInfo   电价实体对象
     * @param periodTypeStr 时段类型字符串（如"高峰"）
     * @return 对应时段电价
     * @throws IllegalArgumentException 当输入无效或字段不存在时抛出
     */
    private BigDecimal getPriceByPeriodType(CiesMarketElecPriEntity elecPriInfo, String periodTypeStr) {
        PeriodTypeEnum periodTypeEnum = PeriodTypeEnum.fromCode(periodTypeStr);
        if (periodTypeEnum == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal price = switch (periodTypeEnum) {
            case PEAK -> elecPriInfo.getPeakPeriodPrice();
            case HIGH -> elecPriInfo.getHighPeriodPrice();
            case NORMAL -> elecPriInfo.getFlatPeriodPrice();
            case OFF_PEAK -> elecPriInfo.getValleyPeriodPrice();
            case DEEP_VALLEY -> elecPriInfo.getDeepValleyPeriodPrice();
        };
        return price;
    }

    /**
     * 获取时段对应阶段映射
     *
     * @param entity
     * @return
     */
    private Map<String, String> mapPeriodFields(CiesMarketStageEntity entity) {
        return Stream.of(entity.getClass().getDeclaredFields()).filter(field -> field.getName().startsWith("period"))  // 筛选period开头的字段
                .collect(LinkedHashMap::new, (map, field) -> {
                    try {
                        field.setAccessible(true);
                        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                        if (annotation != null) {
                            String key = annotation.value();  // 注解文本作为Key
                            String value = (String) field.get(entity);  // 字段值作为Value
                            map.put(key, value);
                        }
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException("Failed to access field: " + field.getName(), e);
                    }
                }, LinkedHashMap::putAll);
    }


    /**
     * 应用市场(本年/本月)
     *
     * @param request
     */
    public void applicationMarket(CiesMarketConfigRequest request) {
        List<CiesMarketElecPriEntity> elecPriEntityList = new ArrayList<>();
        List<CiesMarketStageEntity> list = new ArrayList<>();
        try {
            String projectId = request.getProjectId();
            String year = request.getMonth().split("/")[0];
            String month = request.getMonth().split("/")[1];
            JSONObject datas;
            // 替换本年最新分时价格
            Map<String, Object> params = new HashMap<>();
            params.put("regionName", request.getElecArea());
            params.put("electricityTypeOneName", request.getElecType());
            params.put("electricityTypeTwoName", request.getIndustryType());
            // 特殊的确特殊处理
            if ("西藏自治区".equals(request.getElecArea())){
                params.put("electricityTypeOneName", "");
            }
            if ("广东省深圳市".equals(request.getElecArea())){
                params.put("electricityTypeTwoName", "");
            }
            params.put("tariffLevelId", request.getVoltageLevel());

            if ("1".equals(request.getUseType())) {
                params.put("startTime", year + "-01");
                params.put("endTime", year + "-12");
            } else {
                String yearMonth = request.getMonth().replace("/", "-");
                params.put("startTime", yearMonth);
                params.put("endTime", yearMonth);
                // 应用本月删除电价
                QueryWrapper<CiesMarketElecPriEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("year", year);
                wrapper.eq("project_id", request.getProjectId());
                // 应用本月删除电价
                QueryWrapper<CiesMarketStageEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("year", year);
                queryWrapper.eq("project_id", projectId);
                if (month.length() == 2 && month.startsWith("0")) {
                    wrapper.eq("month", month.substring(1) + "月");
                    queryWrapper.eq("month", month.substring(1) + "月");
                } else {
                    wrapper.eq("month", month + "月");
                    queryWrapper.eq("month", month + "月");
                }
                iCiesMarketElecPriService.remove(wrapper);
                iCiesMarketStageService.remove(queryWrapper);
            }
            JSONObject jsonObject = marketApiUtil.doPost(MONTH_ELE_TYPE_URL, params, JSONObject.class);

            if (SUCCESS == jsonObject.getInteger("resp_code")) {
                if (jsonObject.containsKey("datas") && !jsonObject.getJSONArray("datas").isEmpty()) {
                    JSONArray jsonArray = jsonObject.getJSONArray("datas");
                    for (int i = 1; i <= jsonArray.size(); i++) {
                        CiesMarketElecPriEntity ciesMarketElecPriEntity = new CiesMarketElecPriEntity();
                        ciesMarketElecPriEntity.setYear(Integer.parseInt(year));

                        JSONObject jsonObject1 = jsonArray.getJSONObject(i - 1);
                        if (jsonObject1 != null) {
                            if (i == 1) {
                                // 第一个日期会拼接年份，后续则不会
                                ciesMarketElecPriEntity.setMonth(jsonObject1.getString("month").substring(5));
                            } else {
                                ciesMarketElecPriEntity.setMonth(jsonObject1.getString("month"));
                            }
                            JSONObject data = jsonObject1.getJSONObject("data");
                            ciesMarketElecPriEntity.setDeepValleyPeriodPrice("-".equals(data.getString("deepTime")) ? BigDecimal.ZERO : new BigDecimal(data.getString("deepTime")));
                            ciesMarketElecPriEntity.setFlatPeriodPrice("-".equals(data.getString("flatTime")) ? BigDecimal.ZERO : new BigDecimal(data.getString("flatTime")));
                            ciesMarketElecPriEntity.setHighPeriodPrice("-".equals(data.getString("highTime")) ? BigDecimal.ZERO : new BigDecimal(data.getString("highTime")));
                            ciesMarketElecPriEntity.setPeakPeriodPrice("-".equals(data.getString("topTime")) ? BigDecimal.ZERO : new BigDecimal(data.getString("topTime")));
                            ciesMarketElecPriEntity.setValleyPeriodPrice("-".equals(data.getString("flatTime")) ? BigDecimal.ZERO : new BigDecimal(data.getString("lowTime")));
                            ciesMarketElecPriEntity.setDr(YesOrNo.NO.getCode());
                            ciesMarketElecPriEntity.setElecPriId(IdGenUtil.genUniqueId());
                            ciesMarketElecPriEntity.setProjectId(projectId);
                            ciesMarketElecPriEntity.setCreateBy(CiesUserContext.getCurrentUser());
                            ciesMarketElecPriEntity.setCreateTime(LocalDateTime.now());
                            elecPriEntityList.add(ciesMarketElecPriEntity);
                            QueryWrapper<CiesMarketElecPriEntity> wrapper = new QueryWrapper<>();
                            wrapper.eq("year", year);
                            wrapper.eq("project_id", request.getProjectId());
                            wrapper.eq("month",ciesMarketElecPriEntity.getMonth());
                            iCiesMarketElecPriService.remove(wrapper);

                        }
                        String yearMonth = year + "." + String.format("%02d",i);
                        // 填充容量和需量电价
                        // 如果是应用本月无需循环处理
                        if ("2".equals(request.getUseType())){
                            yearMonth = year + "." + String.format("%02d", Integer.parseInt(ciesMarketElecPriEntity.getMonth().substring(0, ciesMarketElecPriEntity.getMonth().indexOf("月"))));
                            i = Integer.parseInt(ciesMarketElecPriEntity.getMonth().substring(0, ciesMarketElecPriEntity.getMonth().indexOf("月")));
                        }
                        // 组装时间参数
                        CiesMarketConfigRequest configRequest = BeanCopyUtil.copyProperties(request, CiesMarketConfigRequest::new);
                        configRequest.setMonth(yearMonth);
                        if (i == 11) {
                            TimeUnit.MILLISECONDS.sleep(1000);
                        }
                        // 填充时段
                        datas = getSeparationTimeElectricityPrice(configRequest);
                        if (datas != null) {
                            ciesMarketElecPriEntity.setCapacityPrice(datas.getBigDecimal("capacityElectricityPrice"));
                            ciesMarketElecPriEntity.setDemandPrice(datas.getBigDecimal("demandElectricityPrice"));
                            // 填充时段
                            if (datas.containsKey("timeElectricityPriceResps") && datas.getJSONArray("timeElectricityPriceResps").size() > 0) {
                                JSONArray jsonArray1 = datas.getJSONArray("timeElectricityPriceResps");
                                List<CiesTimeElectricityPriceResponse> ciesTimeElectricityPriceResponses = splitToHalfHour(jsonArray1);
                                CiesMarketStageEntity ciesMarketStageEntity = mapToRecord(ciesTimeElectricityPriceResponses);
                                ciesMarketStageEntity.setDr(YesOrNo.NO.getCode());
                                ciesMarketStageEntity.setStageId(IdGenUtil.genUniqueId());
                                ciesMarketStageEntity.setCreateTime(LocalDateTime.now());
                                ciesMarketStageEntity.setCreateBy(CiesUserContext.getCurrentUser());
                                ciesMarketStageEntity.setMonth(i + "月");
                                ciesMarketStageEntity.setYear(Integer.parseInt(year));
                                ciesMarketStageEntity.setProjectId(projectId);
                                list.add(ciesMarketStageEntity);
                                QueryWrapper<CiesMarketStageEntity> queryWrapper = new QueryWrapper<>();
                                queryWrapper.eq("year", year);
                                queryWrapper.eq("project_id", projectId);
                                queryWrapper.eq("month",ciesMarketStageEntity.getMonth());
                                iCiesMarketStageService.remove(queryWrapper);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new BusinessException("市场代购电应用本年/本月失败，错误原因" + e.getMessage());
        }
        iCiesMarketElecPriService.saveBatch(elecPriEntityList);
        iCiesMarketStageService.saveBatch(list);
    }

    /**
     * 获取分时电价图表
     *
     * @param request
     * @return
     */
    public JSONObject getSeparationTimeElectricityPrice(CiesMarketConfigRequest request) {
        JSONObject result = new JSONObject();
        Map<String, Object> params = new HashMap<>();
        params.put("regionName", request.getElecArea());
        params.put("electricityTypeOneName", request.getElecType());
        params.put("electricityTypeTwoName", request.getIndustryType());
        // 两个特殊的地区特殊处理
        if ("西藏自治区".equals(request.getElecArea())){
            params.put("electricityTypeOneName", "");
        }
        if ("广东省深圳市".equals(request.getElecArea())){
            params.put("electricityTypeTwoName", "");
        }
        params.put("tariffLevelId", request.getVoltageLevel());
        params.put("years", request.getMonth().replace("/", "."));
        JSONObject jsonObject = marketApiUtil.doPost(ELE_TYPE_URL, params, JSONObject.class);
        if (SUCCESS == jsonObject.getInteger("resp_code")) {
            result = jsonObject.getJSONObject("datas");
        }
        return result;
    }


    /**
     * 转化时间为24时点
     *
     * @param jsonArray
     * @return
     */
    public static List<CiesTimeElectricityPriceResponse> splitToHalfHour(JSONArray jsonArray) {
        List<CiesTimeElectricityPriceResponse> result = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            LocalTime start = LocalTime.parse(jsonObject.getString("startTime"), formatter);
            LocalTime end = LocalTime.parse(jsonObject.getString("endTime"), formatter);

            // 按半小时步长拆分
            LocalTime current = start;
            while (current.isBefore(end)) {
                LocalTime next = current.plusMinutes(30);

                result.add(new CiesTimeElectricityPriceResponse(current.format(formatter), next.format(formatter), jsonObject.getString("periodType")));
                 if ("00:00".equals(next.toString())){
                     break;
                 }
                current = next;
            }
        }
        return result;
    }

    public CiesMarketStageEntity mapToRecord(List<CiesTimeElectricityPriceResponse> priceList) {
        CiesMarketStageEntity record = new CiesMarketStageEntity();

        // 确保数据量一致（24条）
        if (priceList.size() != TimeSlotEnum.values().length) {
            throw new BusinessException("数据量必须为24条");
        }

        // 按顺序直接赋值
        for (int i = 0; i < priceList.size(); i++) {
            TimeSlotEnum slot = TimeSlotEnum.values()[i];
            String periodType = priceList.get(i).getPeriodType();
            setFieldValue(record, slot.getFieldName(), periodType);
        }
        return record;
    }

    private void setFieldValue(CiesMarketStageEntity record, String fieldName, String value) {
        try {
            Field field = record.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(record, value);
        } catch (Exception e) {
            throw new BusinessException(" 字段赋值失败: " + fieldName);
        }
    }

    /**
     * 翻译对象中所有 period 开头的字段值
     */
    public static void translatePeriodFields(CiesMarketStageExport export) {
        // 获取所有以 period 开头的字段
        List<Field> periodFields = Arrays.stream(export.getClass().getDeclaredFields())
                .filter(field -> field.getName().startsWith("period"))
                .collect(Collectors.toList());

        // 遍历字段并翻译
        periodFields.forEach(field  -> {
            try {
                field.setAccessible(true);
                String code = (String) field.get(export);
                if (code != null) {
                    String desc = PeriodTypeEnum.getNameByCode(code);
                    field.set(export,  desc);
                }
            } catch (IllegalAccessException e) {
                log.error(" 字段翻译失败: {}", field.getName(),  e);
            }
        });
    }
}
