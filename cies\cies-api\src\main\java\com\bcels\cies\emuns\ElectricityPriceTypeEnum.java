package com.bcels.cies.emuns;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 结算单模版
 */
@Getter
@AllArgsConstructor
public enum ElectricityPriceTypeEnum {
    /**
     * 线下计算 - 不基于系统自动计算，采用线下确定的价格
     */
    OFFLINE_CALCULATION("OFFLINE_CALCULATION","线下结算"),

    /**
     * 普通无深谷 - 标准电价模式，不包含深谷时段
     */
    STANDARD_NO_DEEP_VALLEY("STANDARD_NO_DEEP_VALLEY","普通无深谷"),

    /**
     * 普通有深谷 - 标准电价模式，包含深谷时段
     */
    STANDARD_WITH_DEEP_VALLEY("STANDARD_WITH_DEEP_VALLEY","普通有深谷"),

    /**
     * 含税差有深谷 - 考虑税差因素且包含深谷时段的电价模式
     */
    WITH_TAX_DIFF_AND_DEEP_VALLEY("WITH_TAX_DIFF_AND_DEEP_VALLEY","含税差有深谷");

    private  final String code;
    private  final String desc;

    private static final Map<String, String> CODE_TO_DESC = Arrays.stream(values())
            .collect(Collectors.toMap(ElectricityPriceTypeEnum::getCode,  ElectricityPriceTypeEnum::getDesc));

    private static final Map<String, String> DESC_TO_CODE = Arrays.stream(values())
            .collect(Collectors.toMap(
                    ElectricityPriceTypeEnum::getDesc,
                    ElectricityPriceTypeEnum::getCode,
                    (oldVal, newVal) -> oldVal
            ));

    // 根据code获取desc
    public static String getDescByCode(String code) {
        return CODE_TO_DESC.getOrDefault(code,  "未知类型");
    }

    public static ElectricityPriceTypeEnum fromCode(String code) {
        for (ElectricityPriceTypeEnum period : values()) {
            if (period.code.equals(code))  {
                return period;
            }
        }
        return null;
    }
}
