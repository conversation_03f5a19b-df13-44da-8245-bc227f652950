package com.bcels.cies.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.repository.entity.CiesEssMonthlyBillEntity;
import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.request.CiesEssMonthlyBillIncomeRequest;
import com.bcels.cies.request.CiesEssMonthlyBillRequest;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.bcels.cies.response.CiesEssMonthlyBillResponse;
import com.zwy.common.utils.bean.PageResponse;

import java.util.List;

/**
 * <p>
 * 设备信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface ICiesEssMonthlyBillService extends IService<CiesEssMonthlyBillEntity> {

    PageResponse<CiesEssMonthlyBillResponse> findForPage(CiesEssMonthlyBillRequest request);

    void save(CiesEssMonthlyBillRequest request);

    void update(CiesEssMonthlyBillRequest request);

    void reviewUpdate(CiesEssMonthlyBillRequest request);

    PageResponse<CiesEssMonthlyBillResponse> historySettlementPage(CiesEssMonthlyBillRequest request);

    void updateMonthlyBill(CiesEssMonthlyBillIncomeRequest request);

    List<CiesEssMonthlyBillResponse> getEssMonthlyBill(List<String> projectIds,List<String> settlementStatus);

    List<CiesEssMonthlyBillResponse> getEssMonthlyBillByMonth(List<String> projectIds,List<String> month);

    List<CiesEssMonthlyBillResponse> getBillByStatus(String settlementStatus);

    void batchUpdateMonthlyBill(List<String> essBillIds,String status);

}
