package com.bcels.cies.infrastructure.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.BeanCopyUtil;

import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;


public class PageListUtil {

    public static <S, T> PageResponse<T> convertPage(IPage<S> pageResult, Supplier<T> target) {
        List<T> collect = pageResult.getRecords().stream().map(item -> {
            T t = target.get();
            BeanCopyUtil.copyProperties(item, t);
            return t;
        }).collect(Collectors.toList());
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), collect);
    }

}
