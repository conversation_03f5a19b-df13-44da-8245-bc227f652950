package com.bcels.cies.controller;

import com.bcels.cies.api.CiesDictApi;
import com.bcels.cies.domain.CiesDictDomainService;
import com.zwy.common.utils.bean.ResultData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 * 字典映射表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@RestController
@RequestMapping("/dict/v1")
public class CiesDictController implements CiesDictApi {

    @Autowired
    private CiesDictDomainService ciesDictDomainService;
    @Override
    @GetMapping("findDictByDictType")
    public ResultData<Map<String,String>> findDictByDictType(@RequestParam("dictType") String dictType) {
        return ResultData.success(ciesDictDomainService.findDictByDictType(dictType));
    }

    @Override
    @GetMapping("findDictByDictCode")
    public ResultData<Map<String,String>> findDictByDictCode(@RequestParam("dictCode") String dictCode) {
        return ResultData.success(ciesDictDomainService.findDictByDictCode(dictCode));
    }
}
