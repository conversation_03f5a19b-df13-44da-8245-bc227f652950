package com.bcels.cies.infrastructure.utils;


import org.springframework.http.*;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class RestTemplateUtil {

    @Autowired
    private RestTemplate restTemplate;

    /**
     * GET 请求（不带请求头）
     * @param url 请求地址
     * @param responseType 返回类型
     * @param <T> 泛型
     * @return 响应结果
     */
    public <T> T get(String url, Class<T> responseType) {
        return get(url, null, responseType);
    }

    /**
     * GET 请求（带请求头）
     * @param url 请求地址
     * @param headers 请求头
     * @param responseType 返回类型
     * @param <T> 泛型
     * @return 响应结果
     */
    public <T> T get(String url, HttpHeaders headers, Class<T> responseType) {
        HttpEntity<?> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<T> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                requestEntity,
                responseType
        );
        return response.getBody();
    }

    /**
     * GET 请求（带参数）
     * @param url 请求地址
     * @param headers 请求头
     * @param params 请求参数
     * @param responseType 返回类型
     * @param <T> 泛型
     * @return 响应结果
     */
    public <T> T get(String url, HttpHeaders headers, Map<String, Object> params, Class<T> responseType) {
        HttpEntity<?> requestEntity = new HttpEntity<>(headers);
        String finalUrl = buildUrlWithParams(url, params);
        ResponseEntity<T> response = restTemplate.exchange(
                finalUrl,
                HttpMethod.GET,
                requestEntity,
                responseType
        );
        return response.getBody();
    }

    /**
     * POST 请求（JSON格式）
     * @param url 请求地址
     * @param headers 请求头
     * @param body 请求体
     * @param responseType 返回类型
     * @param <T> 请求体类型
     * @param <R> 返回类型
     * @return 响应结果
     */
    public <T, R> R postJson(String url, HttpHeaders headers, T body, Class<R> responseType) {
        if (headers == null) {
            headers = new HttpHeaders();
        }
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<T> requestEntity = new HttpEntity<>(body, headers);
        return restTemplate.postForObject(url,  requestEntity, responseType);
    }

    /**
     * POST 请求（表单格式）
     * @param url 请求地址
     * @param headers 请求头
     * @param formData 表单数据
     * @param responseType 返回类型
     * @param <R> 返回类型
     * @return 响应结果
     */
    public <R> R postForm(String url, HttpHeaders headers, MultiValueMap<String, Object> formData, Class<R> responseType) {
        if (headers == null) {
            headers = new HttpHeaders();
        }
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(formData, headers);
        return restTemplate.postForObject(url,  requestEntity, responseType);
    }

    /**
     * 构建带参数的URL
     * @param url 原始URL
     * @param params 参数Map
     * @return 构建后的URL
     */
    private String buildUrlWithParams(String url, Map<String, Object> params) {
        if (params == null || params.isEmpty())  {
            return url;
        }

        StringBuilder sb = new StringBuilder(url);
        if (!url.contains("?"))  {
            sb.append("?");
        } else {
            sb.append("&");
        }

        params.forEach((key,  value) -> sb.append(key).append("=").append(value).append("&"));

        return sb.substring(0,  sb.length()  - 1);
    }

}
