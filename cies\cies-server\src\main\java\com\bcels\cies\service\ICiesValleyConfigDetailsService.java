package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesValleyConfigDetailsEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.response.CiesDeepResponse;

/**
 * <p>
 * 深谷电量配置明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface ICiesValleyConfigDetailsService extends IService<CiesValleyConfigDetailsEntity> {

    CiesDeepResponse queryDeepInfo(String essBillId);

    CiesDeepResponse queryDeepInfoByDataType(String essBillId,String dataType);


}
