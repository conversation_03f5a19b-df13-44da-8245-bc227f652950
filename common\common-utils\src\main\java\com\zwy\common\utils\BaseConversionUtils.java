package com.zwy.common.utils;

/**
 * 进制转换工具类
 */
public class BaseConversionUtils {

    public static final int NUM_5 = 5;

    /**
     * 16进制转10 返回intger类型1
     *
     * @param
     * @return
     */
    public static Integer base16to10(String base16Str) {
        return Integer.parseInt(base16Str, 16);
    }

    /**
     * 16进制转10 返回String类型
     *
     * @param
     * @return
     */
    public static String base16to10Str(String base16Str) {
        return String.valueOf(base16to10(base16Str));
    }

    /**
     * 16进制转10补零 返回String类型
     *
     * @param
     * @return
     */
    public static String base16to10ZeroPadding(Integer num, String base16Str) {
        return String.format("%0" + num + "d", base16to10(base16Str));
    }

    /**
     * 16进制转10补零 默认5位字符 返回String类型
     *
     * @param
     * @return
     */
    public static String base16to10ZeroPadding(String base16Str) {
        return base16to10ZeroPadding(NUM_5, base16Str);
    }


    /**
     * 10进制转16
     *
     * @param
     * @return
     */
    public static String base10to16(Integer base10) {
        return String.format("%01X", base10);
    }

    /**
     * 10进制转16补零
     * @param
     * @return
     */
    public static String base10to16ZeroPadding(Integer num, Integer base10) {
        return String.format("%0" + num + "X",base10);
    }

    /**
     * 10进制转16补零 默认5位字符
     *
     * @param
     * @return
     */
    public static String base10to16ZeroPadding(Integer base10) {
        return base10to16ZeroPadding(NUM_5, base10);
    }


}
