package com.zwy.common.utils.http;

import java.util.Map;
import lombok.Getter;

/**
 * http请求响应
 * 
 * <AUTHOR>
 * @param <T>
 */
@Getter
public class HttpResponse<T> {
    private int                 statusCode;// 状态码
    private Map<String, String> headers;   // 响应头
    private Map<String, String> cookies;   // cookie值
    private T                   body;      // 响应体

    public HttpResponse(int statusCode, Map<String, String> headers, Map<String, String> cookies, T body) {
        this.statusCode = statusCode;
        this.headers = headers;
        this.cookies = cookies;
        this.body = body;
    }
}
