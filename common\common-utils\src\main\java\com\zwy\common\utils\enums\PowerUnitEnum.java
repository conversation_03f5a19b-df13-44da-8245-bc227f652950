package com.zwy.common.utils.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum PowerUnitEnum {


    MWH("MWh", "兆瓦时"),
    KWH("KWh", "千瓦时"),
    WH("WH", "瓦时");


    private String code;


    private String desc;

    public static PowerUnitEnum findByUnit(String unit) {
        return Stream.of(PowerUnitEnum.values()).filter(f -> f.getCode().equals(unit)).findFirst().orElse(null);
    }

}
