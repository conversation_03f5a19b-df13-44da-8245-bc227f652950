package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.infrastructure.config.CiesUserContext;
import com.bcels.cies.repository.entity.CiesIndicatorsInfoEntity;
import com.bcels.cies.repository.mapper.CiesIndicatorsInfoMapper;
import com.bcels.cies.request.CiesIndicatorsInfoListRequest;
import com.bcels.cies.request.CiesIndicatorsInfoUpdateRequest;
import com.bcels.cies.request.CiesSynIndicatorRequest;
import com.bcels.cies.response.CiesIndicatorsInfoResponse;
import com.bcels.cies.service.ICiesIndicatorsInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.enums.YesOrNo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CiesIndicatorsInfoServiceImpl extends ServiceImpl<CiesIndicatorsInfoMapper, CiesIndicatorsInfoEntity> implements ICiesIndicatorsInfoService {

    @Autowired
    private CiesIndicatorsInfoMapper ciesIndicatorsInfoMapper;
    @Override
    public PageResponse<CiesIndicatorsInfoResponse> findIndicatorsForPage(CiesIndicatorsInfoListRequest request) {
        Page<CiesIndicatorsInfoResponse> page = new Page<>(request.getPage(), request.getPageSize());
        IPage<CiesIndicatorsInfoResponse> pageResult = ciesIndicatorsInfoMapper.findProjectInfoForPage(page, request);
        return new PageResponse<>(pageResult.getTotal(), pageResult.getCurrent(), pageResult.getSize(), pageResult.getRecords());
    }

    @Override
    public void updateIndicator(CiesIndicatorsInfoUpdateRequest request) {

        LambdaUpdateWrapper<CiesIndicatorsInfoEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesIndicatorsInfoEntity::getIndicatorId, request.getIndicatorId())
                .set(CiesIndicatorsInfoEntity::getPageDisplayName, request.getPageDisplayName())
                .set(request.getSort() != null, CiesIndicatorsInfoEntity::getSort, request.getSort())
                .set(CiesIndicatorsInfoEntity::getIsShow, request.getIsShow())
                .set(StringUtils.isNotEmpty(request.getUnit()), CiesIndicatorsInfoEntity::getUnit, request.getUnit())
                .set(CiesIndicatorsInfoEntity::getUpdateBy, CiesUserContext.getCurrentUser())
                .set(CiesIndicatorsInfoEntity::getUpdateTime, LocalDateTime.now());
        ciesIndicatorsInfoMapper.update(null, wrapper);
    }

    @Override
    public CiesIndicatorsInfoResponse findIndicatorById(String indicatorId) {
       return ciesIndicatorsInfoMapper.findIndicatorById(indicatorId);
    }

    @Override
    public List<CiesIndicatorsInfoResponse> findIndicatorsByEquipId(String equipId) {
        QueryWrapper<CiesIndicatorsInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("equip_id", equipId);
        queryWrapper.eq("is_show", 1);
        List<CiesIndicatorsInfoEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<CiesIndicatorsInfoResponse> response = BeanCopyUtil.copyListProperties(list, CiesIndicatorsInfoResponse::new);
        return response.stream().sorted(Comparator.comparing(CiesIndicatorsInfoResponse::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }

    @Override
    public List<CiesIndicatorsInfoResponse> findIndicators(String projectId) {
        QueryWrapper<CiesIndicatorsInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(projectId),"project_id", projectId);
        queryWrapper.eq("dr", YesOrNo.NO.getCode());
        List<CiesIndicatorsInfoEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<CiesIndicatorsInfoResponse> response = BeanCopyUtil.copyListProperties(list, CiesIndicatorsInfoResponse::new);
        return response.stream().sorted(Comparator.comparing(CiesIndicatorsInfoResponse::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());
    }

    @Override
    public void updateSynIndicator(CiesSynIndicatorRequest request) {
        String dataName;
        if ("测点".equals(request.getDataType())) {
            String pointName = String.format("%s-%s-%s", request.getChannelName(), request.getEquipName(), request.getDataName()
            );
            dataName = pointName;
        } else {
            dataName = request.getDataName();
        }
        LambdaUpdateWrapper<CiesIndicatorsInfoEntity> wrapper = new LambdaUpdateWrapper<>();

        wrapper.eq(CiesIndicatorsInfoEntity::getIndicatorId, request.getIndicatorId())
                .set(CiesIndicatorsInfoEntity::getProjectId, request.getProjectId())
                .set(CiesIndicatorsInfoEntity::getEquipId, request.getEquipId())
                .set(CiesIndicatorsInfoEntity::getDataType, request.getDataType())
                .set(CiesIndicatorsInfoEntity::getPageDisplayName,request.getDataName())
                .set(CiesIndicatorsInfoEntity::getDataName, dataName)
                .set(CiesIndicatorsInfoEntity::getUpdateBy, "服务器")
                .set(CiesIndicatorsInfoEntity::getUpdateTime, LocalDateTime.now());
        ciesIndicatorsInfoMapper.update(null, wrapper);
    }

    @Override
    public void updateCurrentData(CiesIndicatorsInfoEntity entity) {
        LambdaUpdateWrapper<CiesIndicatorsInfoEntity> wrapper = Wrappers.lambdaUpdate();
        wrapper.eq(CiesIndicatorsInfoEntity::getIndicatorId, entity.getIndicatorId())
                .set(CiesIndicatorsInfoEntity::getCurrentValue,entity.getCurrentValue())
                .set(CiesIndicatorsInfoEntity::getUpdateBy, entity.getUpdateBy())
                .set(CiesIndicatorsInfoEntity::getUpdateTime, LocalDateTime.now());
        ciesIndicatorsInfoMapper.update(null, wrapper);
    }
}
