package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Schema(description = "储能计划监控")
public class CiesEnergyPlanMonitorRequest {

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "项目id")
    private String projectId;

    @Schema(description = "企业名称")
    private String enterpriseName;

}
