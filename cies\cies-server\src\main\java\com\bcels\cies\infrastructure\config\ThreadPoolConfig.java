package com.bcels.cies.infrastructure.config;

import lombok.extern.slf4j.*;
import org.springframework.context.annotation.*;
import org.springframework.scheduling.concurrent.*;

import java.util.concurrent.*;


@Slf4j
@Configuration
public class ThreadPoolConfig {


    private int corePoolSize = 32;

    private int maxPoolSize = 100;

    private int queueCapacity = 200;

    private String namePrefix = "pool-";

    private int keepAliveSeconds = 60000;

    @Bean(name = "asyncTaskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数
        executor.setMaxPoolSize(maxPoolSize);
        // 任务队列大小
        executor.setQueueCapacity(queueCapacity);
        // 线程前缀名
        executor.setThreadNamePrefix(namePrefix);
        // 线程的空闲时间
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 线程初始化
        executor.initialize();
        return executor;
    }

}
