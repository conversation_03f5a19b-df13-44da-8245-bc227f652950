package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "日充放电量（数据中台）")
public class CiesDailyChargeResponse implements Serializable {

    @Schema(description = "时间")
    private LocalDateTime time;

    @Schema(description = "指标值")
    private BigDecimal IndicatorValue;
}
