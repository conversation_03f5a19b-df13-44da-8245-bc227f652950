package com.bcels.cies.domain;

import com.bcels.cies.service.ICiesDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class CiesDictDomainService {

    @Autowired
    private ICiesDictService iCiesDictService;


    /**
     * 按照字典类型查询枚举
     * @param dictType
     * @return
     */
    public Map<String,String> findDictByDictType(String dictType){
        return iCiesDictService.findDictByDictType(dictType);
    }

    /**
     * 按照字典code获取对应枚举值
     * @param dictCode
     * @return
     */
    public Map<String,String> findDictByDictCode(String dictCode){
        return iCiesDictService.findDictByDictCode(dictCode);
    }
}
