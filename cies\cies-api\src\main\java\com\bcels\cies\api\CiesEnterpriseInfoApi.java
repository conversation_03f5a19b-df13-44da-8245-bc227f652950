package com.bcels.cies.api;

import com.bcels.cies.request.CiesEnterpriseInfoRequest;
import com.bcels.cies.response.CiesEnterpriseInfoResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "1、企业信息API")
@FeignClient(name = "ciesEnterprise", path = "/ciesEnterprise/v1")
public interface CiesEnterpriseInfoApi {

    @Operation(summary = "企业列表")
    @PostMapping("page")
    ResultData<PageResponse<CiesEnterpriseInfoResponse>> findForPage(@RequestBody CiesEnterpriseInfoRequest request);

    @Operation(summary = "编辑企业信息")
    @PostMapping("update")
    ResultData<Void> updateEquipInfo(@RequestBody CiesEnterpriseInfoRequest request);

    @Operation(summary = "新建企业信息")
    @PostMapping("add")
    ResultData<Void> addEquipInfo(@RequestBody CiesEnterpriseInfoRequest request);

    @Operation(summary = "查询企业信息")
    @GetMapping("queryByName")
    @Parameters({
            @Parameter(name = "enterpriseName", description = "企业名称", required = false)
    })
    ResultData<List<CiesEnterpriseInfoResponse>> findEnterprise(@RequestParam(value = "enterpriseName",required = false) String enterpriseName);

    @Operation(summary = "查询储能企业信息")
    @GetMapping("findEnergyEnterprise")
    @Parameters({
            @Parameter(name = "enterpriseName", description = "企业名称", required = false)
    })
    ResultData<List<CiesEnterpriseInfoResponse>> findEnergyEnterprise(@RequestParam(value = "enterpriseName",required = false) String enterpriseName);

    @Operation(summary = "查询单个企业信息")
    @GetMapping("findById")
    ResultData<CiesEnterpriseInfoResponse> findEnterpriseById(@RequestParam("enterpriseId") String enterpriseId);
}
