package com.bcels.cies.repository.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bcels.cies.repository.entity.CiesEquipInfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesEquipInfoRequest;
import com.bcels.cies.request.CiesProjectInfoRequest;
import com.bcels.cies.response.CiesEquipInfoResponse;
import com.bcels.cies.response.CiesProjectInfoResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 设备信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Mapper
public interface CiesEquipInfoMapper extends BaseMapper<CiesEquipInfoEntity> {

    IPage<CiesEquipInfoResponse> findEquipInfoForPage(@Param("page") Page<CiesEquipInfoResponse> page, @Param("request") CiesEquipInfoRequest request);


}
