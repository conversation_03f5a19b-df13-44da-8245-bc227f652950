package com.bcels.cies.repository.mapper;

import com.bcels.cies.repository.entity.CiesEnergyStoragePlanEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bcels.cies.request.CiesEnergyPlanListRequest;
import com.bcels.cies.response.CiesEnergyStoragePlanResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 储能计划表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Mapper
public interface CiesEnergyStoragePlanMapper extends BaseMapper<CiesEnergyStoragePlanEntity> {


    List<CiesEnergyStoragePlanEntity> findAllConnPointPlan(@Param("request") CiesEnergyPlanListRequest request);

    List<CiesEnergyStoragePlanEntity> findConfirmedPlan(@Param("projectIdList") List<String> projectIdList, @Param("date") String date);
}
