package com.bcels.cies.api;

import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@Tag(name = "13、字典枚举")
@FeignClient(name = "dict", path = "/dict/v1")
public interface CiesDictApi {


    @Operation(summary = "按照字典类型查询")
    @GetMapping("findDictByDictType")
    @Parameters({
            @Parameter(name = "dictType", description = "字典类型", required = true)
    })
    ResultData<Map<String,String>> findDictByDictType(@RequestParam("dictType") String dictType);



    @Operation(summary = "按照字典code查询")
    @GetMapping("findDictBydDictCode")
    @Parameters({
            @Parameter(name = "dictCode", description = "字典code", required = true)
    })
    ResultData<Map<String,String>> findDictByDictCode(@RequestParam("dictCode") String dictCode);
}
