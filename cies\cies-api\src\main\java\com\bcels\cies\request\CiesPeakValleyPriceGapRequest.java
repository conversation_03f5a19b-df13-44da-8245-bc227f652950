package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "结算单查询参数")
public class CiesPeakValleyPriceGapRequest implements Serializable {

    @Schema(description = "并网点Id")
    private String connectionPointId;

    @Schema(description = "统计阶段")
    private String statPeriod;

    @Schema(description = "分项合计")
    private BigDecimal totalAmount;

    @Schema(description = "峰谷价差总收益（元）")
    private BigDecimal peakValleyRevenue;

    @Schema(description = "储能结算主键")
    private String essBillId;
}

