package com.bcels.cies.api;

import com.bcels.cies.request.CiesEnergyPlanMonitorRequest;
import com.bcels.cies.response.CiesPlanMonitorListResponse;
import com.bcels.cies.response.CiesPlanMonitorViewResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.IOException;
import java.util.List;

@Tag(name = "8、储能计划监控API")
@FeignClient(name = "planMonitor", path = "/planMonitor/v1")
public interface CiesEnergyPlanMonitorApi {
    @Operation(summary = "储能计划监控图形")
    @PostMapping("query")
    ResultData<CiesPlanMonitorViewResponse> findForPage(@RequestBody CiesEnergyPlanMonitorRequest request);

    @Operation(summary = "储能计划监控导出")
    @PostMapping("export")
    ResultData<Void> export(@RequestBody CiesEnergyPlanMonitorRequest request, HttpServletResponse response) throws IOException;
}
