<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesDemandDistributeRecordMapper">

    <select id="findDemandRecordsForPage" resultType="com.bcels.cies.response.CiesDemandResponse">
        SELECT records.*,dict.dict_desc as commandType FROM cies_demand_distribute_record records
        left join cies_dict dict on dict.dict_code = records.command_type
        WHERE  records.project_id = #{request.projectId}
        order by records.create_time desc
    </select>
</mapper>
