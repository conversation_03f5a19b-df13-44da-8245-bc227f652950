package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 项目信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Getter
@Setter
@TableName("cies_project_info")
@ApiModel(value = "CiesProjectInfoEntity对象", description = "项目信息")
public class CiesProjectInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("项目主键")
    @TableId
    private String projectId;

    @ApiModelProperty("项目名称")
    private String proName;

    @ApiModelProperty("项目类型")
    private String projectType;

    @ApiModelProperty("租户编码")
    private String tenantCode;

    @ApiModelProperty("运行状态")
    private String projectStatus;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("项目地区-省市区")
    private String areaCode;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("项目说明")
    private String projectDesc;

    @ApiModelProperty("额定容量(KWH)")
    private BigDecimal ratedCapacity;

    @ApiModelProperty("额定功率(KW)")
    private BigDecimal ratedPower;

    @ApiModelProperty("SOC上限(%)")
    private BigDecimal socUpperLimit;

    @ApiModelProperty("SOC下限(%)")
    private BigDecimal socLowerLimit;

    @ApiModelProperty("储能分类")
    private String energyStorageType;

    @ApiModelProperty("变压器容量(KVA)")
    private BigDecimal transformerCapacity;

    @ApiModelProperty("用户负荷功率上限(KW)")
    private BigDecimal loadPowerUpperLimit;

    @ApiModelProperty("项目投资金额(万元)")
    private BigDecimal investmentAmount;

    @ApiModelProperty("项目投资日期")
    private String investmentDate;

    @ApiModelProperty("项目投产日期")
    private String operationDate;

    @ApiModelProperty("合同充放电效率(%)")
    private BigDecimal chargeDischargeEfficiency;

    @ApiModelProperty("标称功率(KW)")
    private BigDecimal nominalPower;

    @ApiModelProperty("变流器数量")
    private Integer converterCount;

    @ApiModelProperty("电池单元数量")
    private Integer batteryModuleCount;

    @ApiModelProperty("电池单体数量")
    private Integer batteryCellCount;

    @ApiModelProperty("电池规格")
    private String batterySpec;

    @ApiModelProperty("最大可充功率(KW)")
    private BigDecimal maxChargePower;

    @ApiModelProperty("最大可放功率(KW)")
    private BigDecimal maxDischargePower;

    @ApiModelProperty("单柜额定容量(KWH)")
    private BigDecimal singleCabinetCapacity;

    @ApiModelProperty("用电地区")
    private String elecArea;

    @ApiModelProperty("用电类型")
    private String elecType;

    @ApiModelProperty("行业类型")
    private String industryType;

    @ApiModelProperty("电压等级")
    private String voltageLevel;

    @ApiModelProperty("甲方企业名称")
    private String partyAName;

    @ApiModelProperty("乙方企业名称")
    private String partyBName;

    @ApiModelProperty("结算单项目名称")
    private String settlementProjectName;

    @ApiModelProperty("甲方分成比例(%)")
    private BigDecimal partyARatio;

    @ApiModelProperty("乙方分成比例(%)")
    private BigDecimal partyBRatio;

    @ApiModelProperty("结算单模板")
    private String templateType;

    @ApiModelProperty("结算单税差（%）")
    private BigDecimal taxAdjustmentRate;

    @ApiModelProperty("结算电表倍率")
    private BigDecimal meterMultiplier;

    @ApiModelProperty("结算单异议等待工作日")
    private String disputeDays;

    @ApiModelProperty("结算单预期付款到账时间")
    private String expectedPaymentDate;

    @ApiModelProperty("企业主键")
    private String enterpriseId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}
