package com.bcels.cies.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zwy.common.utils.bean.PageRequest;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ToString
@Schema(description = "结算单信息")
public class CiesEssMonthlyBillRequest extends PageRequest implements Serializable {

    @Schema(description = "储能结算主键")
    private String essBillId;

    @Schema(description = "企业")
    private String enterpriseName;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "结算状态")
    private String settlementStatus;

    @Schema(description = "开具方式")
    private String issueMethod;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "结算月份")
    private String settlementMonth;

    @Schema(description = "峰谷价差总收益（元）")
    private BigDecimal peakValleyRevenue;

    @Schema(description = "甲方收益（元）")
    private BigDecimal partyAIncome;

    @Schema(description = "乙方收益（元）")
    private BigDecimal partyBIncome;

    @Schema(description = "用户结算附件地址")
    private String userAttachmentUrl;

    @Schema(description = "登记结算单PDF附件地址")
    private String pdfAttachmentUrl;

    @Schema(description = "登记结算单Excel/Word附件地址")
    private String officeAttachmentUrl;

    @Schema(description = "深谷日期")
    private String deepDate;

    @Schema(description = "深谷时间段")
    private String deepTime;

    @Schema(description = "开始时间")
    private String settlementStartTime;

    @Schema(description = "结束时间")
    private String settlementEndTime;

    @Schema(description = "申诉时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime disputeCountdown;
}
