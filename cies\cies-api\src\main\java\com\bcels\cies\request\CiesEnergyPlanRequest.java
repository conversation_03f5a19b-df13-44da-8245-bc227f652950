package com.bcels.cies.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "储能计划明细")
public class CiesEnergyPlanRequest {

    @Schema(description = "计划日期")
    private String planDate;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "充放电类型")
    private String chargeDischargeType;

    @Schema(description = "计划功率")
    private BigDecimal plannedPower;

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "并网点主键")
    private String connectionPointId;
}
