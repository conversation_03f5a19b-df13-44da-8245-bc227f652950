package com.bcels.cies.api;

import com.bcels.cies.request.CiesEssMonthlyBilCalRequest;
import com.bcels.cies.request.CiesEssMonthlyBillIncomeRequest;
import com.bcels.cies.request.CiesEssMonthlyBillRequest;
import com.bcels.cies.request.CiesMobileIncomeRequest;
import com.bcels.cies.response.CiesEssMonthlyBillResponse;
import com.bcels.cies.response.CiesMobileIncomeResponse;
import com.bcels.cies.response.CiesSettlementBillDetailResponse;
import com.zwy.common.utils.bean.PageResponse;
import com.zwy.common.utils.bean.ResultData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "储能月度结算表")
@FeignClient(name = "essMonthlyBill", path = "/EssMonthlyBill/v1")
public interface CiesEssMonthlyBillApi {

    @Operation(summary = "设备列表")
    @PostMapping("page")
    public ResultData<PageResponse<CiesEssMonthlyBillResponse>> findForPage(@RequestBody CiesEssMonthlyBillRequest request);

    @Operation(summary = "查询结算单详情")
    @GetMapping("findById")
    @Parameters({
            @Parameter(name = "essBillId", description = "结算单ID", required = true)
    })
    ResultData<CiesSettlementBillDetailResponse> findById(@RequestParam("essBillId") String essBillId);

    @Operation(summary = "应用深谷计算")
    @PostMapping("generateDeep")
    ResultData<Void> generateDeep(@RequestBody CiesEssMonthlyBillRequest request);

    @Operation(summary = "结算单重新计算")
    @PostMapping("reCalculate")
    ResultData<Void> reCalculate(@RequestBody CiesEssMonthlyBillIncomeRequest request);

    @Operation(summary = "结算单提交")
    @PostMapping("submit")
    ResultData<Void> submit(@RequestBody CiesEssMonthlyBillIncomeRequest request);

    @Operation(summary = "文件上传")
    @PostMapping("upload")
    ResultData<String> upload(@RequestPart("file") MultipartFile file);

    @Operation(summary = "文件下载")
    @GetMapping("downloadFile")
    @Parameters({
            @Parameter(name = "fileName", description = "文件名", required = true)
    })
    ResultData<Void> downloadFile(@RequestParam String fileName,HttpServletResponse response);

    @Operation(summary = "删除文件")
    @GetMapping("deleteFile")
    ResultData<Void> deleteFile(@RequestParam("fileName") String fileName);

    @Operation(summary = "查看结算单详情(小程序)")
    @GetMapping("findByBillId")
    ResultData<CiesSettlementBillDetailResponse> findByBillId(@RequestParam("essBillId") String essBillId);

    @Operation(summary = "获取收益统计（小程序）")
    @PostMapping("statisticIncome")
    ResultData<CiesMobileIncomeResponse> statisticIncome(@RequestBody CiesMobileIncomeRequest request);


    @Operation(summary = "申诉结算单（小程序）")
    @GetMapping("appealSettle")
    ResultData<Void> appealSettle(@RequestParam("essBillId") String essBillId);

}
