package com.bcels.cies.infrastructure.utils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Map;

public class TimeSlotConverter {
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private final Map<Integer, String> indexToTimeMap = new LinkedHashMap<>();
    private final Map<String, Integer> timeToIndexMap = new LinkedHashMap<>();
    private final int startIndex;

    /**
     * @param startIndex      起始点位编号（如起始为0或1）
     * @param intervalMinutes 时间间隔（单位：分钟）
     * @param totalSlots      总点位数量
     */
    public TimeSlotConverter(int startIndex, int intervalMinutes, int totalSlots) {
        this.startIndex = startIndex;
        initMaps(intervalMinutes, totalSlots);
    }

    private void initMaps(int interval, int total) {
        for (int i = 0; i < total; i++) {
            int minutesOfDay = i * interval;
            String time = LocalTime.of(minutesOfDay / 60, minutesOfDay % 60)
                    .format(TIME_FORMATTER);
            int currentIndex = startIndex + i;
            indexToTimeMap.put(currentIndex, time);
            timeToIndexMap.put(time, currentIndex);
        }
    }

    // 时间→索引转换（支持"HH:mm"格式）
    public Integer getIndex(String time) {
        Integer index = timeToIndexMap.get(time);
        if (index == null) throw new IllegalArgumentException("无效时间格式或超出范围");
        return index;
    }

    // 索引→时间转换（支持自定义起始索引）
    public  String getTime(int index) {
        String time = indexToTimeMap.get(index);
        if (time == null) throw new IllegalArgumentException("索引超出有效范围");
        return time;
    }
}
