package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@Schema(description = "并网点返回信息")
public class CiesInitConnResponse implements Serializable {

    @Schema(description = "并网点主键")
    private String connectionPointId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "编号")
    private String code;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "中台ID")
    private String platformId;

    @Schema(description = "容量占比")
    private BigDecimal capacityRatio;

    @Schema(description = "上级节点ID")
    private String parentId;

    @Schema(description = "规则主键")
    private String ruleId;

    @Schema(description = "并网点子节点集合")
    List<CiesInitConnResponse> children;
}
