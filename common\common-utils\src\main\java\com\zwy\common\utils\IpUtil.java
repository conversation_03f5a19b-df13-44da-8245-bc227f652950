package com.zwy.common.utils;

import java.util.Optional;

public class IpUtil {
    private static final int I_32 = 32;
    /** ip *************** >>> 11111111111111111111111111111111 */
    private static final long VALIDATE_DATA = 4294967295L;
    /** 1-255 正则 */
    private static final String REG_1_255 = "(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|[1-9])";
    /** 0-255 正则 */
    private static final String REG_0_255 = "(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)";
    /** ip 正则 */
    private static final String IP_REGEX = "^" + REG_1_255 + "\\." + REG_0_255 + "\\." + REG_0_255 + "\\." + REG_0_255 + "$";





    /** 把long类型的Ip转为一般Ip类型：xx.xx.xx.xx */
    public static String long2ip(Long ip){
        Long tmp = Optional.ofNullable(ip).filter(l -> l >= 0 && l <= VALIDATE_DATA )
                .orElseThrow(()-> new RuntimeException(ip + " 数字 不在 有效ip范围."));
        // ********* ~ *******,********* ~ *******,********* ~ *******,********* ~ *******
        return (tmp>>>24) + "." + (tmp>>>16&255L) + "." + (tmp>>>8&255L) + "." + (tmp&255L);
    }
    /** 将字符串形式IP地址转换long类型 */
    public static long ip2Long(String ip){
        ip = checkIp(ip);
        String[] split = ip.trim().split("\\.");
        return ( s2l(split[0]) << 24 ) + ( s2l(split[1]) << 16 ) + ( s2l(split[2]) << 8 )+ ( s2l(split[3]) );
    }
    private static long s2l(String s){
        return Long.parseLong(s,10);
    }
    private static String checkIp(String ip){
        return Optional.ofNullable(ip).filter(s -> !s.isEmpty()&&s.matches(IP_REGEX))
                .orElseThrow(() -> new RuntimeException(ip + " ip地址不合法."));
    }



}
