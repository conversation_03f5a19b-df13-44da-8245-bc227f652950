package com.zwy.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

public class RegexUtil {

    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+$");

    public static boolean isNumber(String value) {
        if(StringUtils.isBlank(value)){
            return false;
        }
        return NUMBER_PATTERN.matcher(value).find();
    }
}

