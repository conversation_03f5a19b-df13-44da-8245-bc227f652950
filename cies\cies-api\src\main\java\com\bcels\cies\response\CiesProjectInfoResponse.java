package com.bcels.cies.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zwy.common.utils.annotation.Dict;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@ToString
@Schema(description = "项目信息")
public class CiesProjectInfoResponse implements Serializable {

    @Schema(description = "项目主键")
    private String projectId;

    @Schema(description = "项目名称")
    private String proName;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "项目类型")
    @Dict(code ="SYS_PROJECT_TYPE")
    private String projectType;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "项目地区-省市区")
    private String areaCode;

    @ApiModelProperty("省份")
    private String province;

    @Schema(description = "项目说明")
    private String projectDesc;

    @Schema(description = "额定容量(KWH)")
    private BigDecimal ratedCapacity;

    @Schema(description = "额定功率(KW)")
    private BigDecimal ratedPower;

    @Schema(description = "SOC上限(%)")
    private BigDecimal socUpperLimit;

    @Schema(description = "SOC下限(%)")
    private BigDecimal socLowerLimit;

    @Schema(description = "储能分类")
    private String energyStorageType;

    @Schema(description = "变压器容量(KVA)")
    private BigDecimal transformerCapacity;

    @Schema(description = "用户负荷功率上限(KW)")
    private BigDecimal loadPowerUpperLimit;

    @Schema(description = "项目投资金额(万元)")
    private BigDecimal investmentAmount;

    @Schema(description = "项目投资日期")
    private String investmentDate;

    @Schema(description = "项目投产日期")
    private String operationDate;

    @Schema(description = "合同充放电效率(%)")
    private BigDecimal chargeDischargeEfficiency;

    @Schema(description = "标称功率(KW)")
    private BigDecimal nominalPower;

    @Schema(description = "变流器数量")
    private Integer converterCount;

    @Schema(description = "电池单元数量")
    private Integer batteryModuleCount;

    @Schema(description = "电池单体数量")
    private Integer batteryCellCount;

    @Schema(description = "电池规格")
    private String batterySpec;

    @Schema(description = "最大可充功率(KW)")
    private BigDecimal maxChargePower;

    @Schema(description = "最大可放功率(KW)")
    private BigDecimal maxDischargePower;

    @Schema(description = "单柜额定容量(KWH)")
    private BigDecimal singleCabinetCapacity;

    @Schema(description = "用电地区")
    private String elecArea;

    @Schema(description = "用电类型")
    private String elecType;

    @Schema(description = "行业类型")
    private String industryType;

    @Schema(description = "电压等级")
    private String voltageLevel;

    @Schema(description = "市场类型")
    private String marketType;

    @Schema(description = "甲方企业名称")
    private String partyAName;

    @Schema(description = "乙方企业名称")
    private String partyBName;

    @Schema(description = "结算单项目名称")
    private String settlementProjectName;

    @Schema(description = "甲方分成比例(%)")
    private BigDecimal partyARatio;

    @Schema(description = "乙方分成比例(%)")
    private BigDecimal partyBRatio;

    @Schema(description = "结算单模板")
    private String templateType;

    @Schema(description = "结算单税差（%）")
    private BigDecimal taxAdjustmentRate;

    @Schema(description = "结算电表倍率")
    private BigDecimal meterMultiplier;

    @Schema(description = "结算单异议等待工作日")
    private String disputeDays;

    @Schema(description = "结算单预期付款到账时间")
    private String expectedPaymentDate;
    
    @Schema(description = "企业主键")
    private String enterpriseId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除 0否 1是")
    private Integer dr;

    @Schema(description = "申诉时间")
    private LocalDateTime disputeCountdown;

    @Schema(description = "甲方收益")
    private BigDecimal partyAincome;

    @Schema(description = "乙方收益")
    private BigDecimal partyBincome;

    @Schema(description = "乙方收益")
    private BigDecimal peakValleyRevenue;

    @Schema(description = "结算单状态")
    @Dict(code = "SETTLEMENT_BILL_STATUS")
    private String settlementStatus;

    @Schema(description = "结算月份")
    private String settlementMonth;

    @Schema(description = "结算ID")
    private String essBillId;

    @Schema(description = "指标ID")
    private String specialIndicatorsId;

    @Schema(description = "指标名称")
    private String indicatorName;

    @ApiModelProperty("运行状态")
    private String projectStatus;

    @ApiModelProperty("最新数据")
    private BigDecimal currentData;

    @ApiModelProperty("充放电状态")
    private String chargeStatus;

    @ApiModelProperty("soc")
    private String soc;

    @ApiModelProperty("储能站有功功率")
    private String activePower;

    @ApiModelProperty("单位")
    private String unit;

    @Schema(description = "指标的曲线图数据")
    private Map<String, List<CiesHisIndicatorsDataResponse>> ciesHisIndicatorsData;

}
