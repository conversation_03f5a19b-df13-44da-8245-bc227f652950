<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bcels.cies.repository.mapper.CiesEnergyStoragePlanMapper">

    <select id="findAllConnPointPlan" resultType="com.bcels.cies.repository.entity.CiesEnergyStoragePlanEntity">
        select plan.*
        from cies_project_info pro
        left join cies_connection_point point on point.project_id = pro.project_id
        left join cies_energy_storage_plan plan on plan.connection_point_id = point.connection_point_id
        <where>
            <if test="request.projectId !=null">
                and pro.project_id = #{request.projectId}
            </if>
            <if test="request.planDateList != null and request.planDateList.size()> 0">
                AND plan.plan_date IN
                <foreach collection="request.planDateList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="findConfirmedPlan" resultType="com.bcels.cies.repository.entity.CiesEnergyStoragePlanEntity">
        select plan.*
        from cies_ess_plan_audit audit
        left join cies_connection_point point on point.project_id = audit.project_id
        left join cies_energy_storage_plan plan on plan.connection_point_id = point.connection_point_id
        <where>
            <if test="projectIdList != null and projectIdList.size()> 0">
                AND audit.project_id IN
                <foreach collection="projectIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND audit.plan_date = #{date}
            and audit.dr = '0' and plan.plan_date = #{date}
        </where>
    </select>
</mapper>
