package com.bcels.cies.repository.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@TableName("cies_peak_valley_summary")
@ApiModel(value = "CiesPeakValleySummaryEntity对象", description = "峰谷价差分项合计")
public class CiesPeakValleySummaryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("分项合计主键")
    @TableId
    private String id;

    @ApiModelProperty("统计阶段")
    private String statPeriod;

    @ApiModelProperty("合计金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("并网点主键")
    private String connectionPointId;

    @ApiModelProperty("储能结算主键")
    private String essBillId;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新人")
    private String updateBy;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("是否删除 0否 1是")
    private Integer dr;
}

