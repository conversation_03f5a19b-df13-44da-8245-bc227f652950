package com.zwy.common.utils;


import com.zwy.common.utils.enums.PowerUnitEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.zwy.common.utils.BigDecimalUtils.THOUSAND;

public class ConvertPowerUnitUtil {


    public static BigDecimal toKWH(PowerUnitEnum unit, BigDecimal value) {
        if (unit == null || value == null) {
            return value;
        }
        BigDecimal result = value;
        switch (unit) {
            case MWH:
                result = value.multiply(THOUSAND);
                break;
            case WH:
                result = value.divide(THOUSAND, 4, RoundingMode.HALF_DOWN);
                break;
        }

        return result;
    }
}
