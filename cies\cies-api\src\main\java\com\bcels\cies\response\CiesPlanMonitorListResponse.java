package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "场站监控")
public class CiesPlanMonitorListResponse implements Serializable {

    @Schema(description = "时间")
    private String time;

    @Schema(description = "计划功率 ")
    private BigDecimal planPower;

    @Schema(description = "储能站有功功率")
    private BigDecimal storageStation;

    @Schema(description = "工厂用电有功功率")
    private BigDecimal factoryElecActivePower;

    @Schema(description = "工厂总有功功率")
    private BigDecimal factoryElecTotalPower;

    @Schema(description = "储能站SOC")
    private BigDecimal storageStationSOC;
}
