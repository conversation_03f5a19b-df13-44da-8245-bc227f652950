package com.bcels.cies.service;

import com.bcels.cies.repository.entity.CiesEsPlanRuleEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.response.CiesConnectionPointResponse;
import com.bcels.cies.response.CiesEsPlanRuleResponse;
import com.bcels.cies.response.CiesStatisticsProjectResponse;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-13
 */
public interface ICiesEsPlanRuleService extends IService<CiesEsPlanRuleEntity> {

    CiesEsPlanRuleResponse findByProjectId(String projectId);

    CiesConnectionPointResponse findConnInfoById(String connectionPointId);

    void updateRule(CiesEsPlanRuleEntity esPlanRuleEntity);

}
