package com.bcels.cies.service;

import com.bcels.cies.repository.entity.DcpChannelConfigEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcels.cies.request.CiesPointAndIndicatorRequest;
import com.bcels.cies.response.CiesPointAndIndicatorResponse;
import com.bcels.cies.response.DcpChannelConfigResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 通道配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface IDcpChannelConfigService extends IService<DcpChannelConfigEntity> {

    List<DcpChannelConfigResponse> findByProjectId(String projectId);

    List<CiesPointAndIndicatorResponse> findChannelForPoint(String projectId);

    List<CiesPointAndIndicatorResponse> findEquipForPoint(CiesPointAndIndicatorRequest request);

    List<CiesPointAndIndicatorResponse> findTestPoint(CiesPointAndIndicatorRequest request);

    List<CiesPointAndIndicatorResponse> findEquip(String projectId);

    List<CiesPointAndIndicatorResponse> findIndicatorForOne(CiesPointAndIndicatorRequest request);

    List<CiesPointAndIndicatorResponse> findIndicatorForTwo(CiesPointAndIndicatorRequest request);

    String findEquipById(String equipId);

    String findChannelById(String cId);

}
