package com.zwy.common.utils.http;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.security.KeyStore;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.net.ssl.SSLContext;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HeaderElement;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.NameValuePair;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CookieStore;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.DeflateDecompressingEntity;
import org.apache.http.client.entity.GzipDecompressingEntity;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpHead;
import org.apache.http.client.methods.HttpOptions;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.methods.HttpTrace;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.config.SocketConfig;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.cookie.Cookie;
import org.apache.http.entity.BasicHttpEntity;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.BasicHttpClientConnectionManager;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * http客户端工具 可以做为调用API的工具 也可以作为上传或者下载工具
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpUtil {
    private HttpUtil() {
    }

    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);
    public static final int DEFAULT_SO_TIMEOUT = 10000;
    public static final int DEFAULT_BACKLOG_SIZE = 2048;
    public static final int DEFAULT_SND_BUF_SIZE = 512;
    public static final int DEFAULT_RCV_BUF_SIZE = 8192;

    public static final int DEFAULT_POOL_MAX_TOTAL = 1024;
    public static final int DEFAULT_POOL_MAX_PER_ROUTE = 128;
    public static final long DEFAULT_POOL_EVICT_IDLE = 60;

    private static volatile CloseableHttpClient pooledClient;

    @SuppressWarnings("resource")
    private static CloseableHttpClient buildClient(boolean pooled, KeyStore keyStore) {
        SocketConfig soConfig = SocketConfig.custom().setTcpNoDelay(true).setSoTimeout(DEFAULT_SO_TIMEOUT)
                .setSoReuseAddress(true).setSoKeepAlive(pooled).setBacklogSize(DEFAULT_BACKLOG_SIZE)
                .setSndBufSize(DEFAULT_SND_BUF_SIZE).setRcvBufSize(DEFAULT_RCV_BUF_SIZE).build();

        HttpClientBuilder builder = HttpClients.custom();

        builder.evictIdleConnections(DEFAULT_POOL_EVICT_IDLE, TimeUnit.SECONDS);

        // https协议支持
        SSLContext sslContext = null;
        try {
            sslContext = SSLContexts.custom().loadTrustMaterial(keyStore, (chain, authType) -> true).build();
        } catch (Exception e) {
            e.printStackTrace();
        }
        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
        builder.setSSLSocketFactory(sslConnectionSocketFactory);

        // gzip,deflate支持
        builder.addInterceptorFirst((org.apache.http.HttpResponse response, HttpContext context) -> {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                Header ceheader = entity.getContentEncoding();
                if (ceheader != null) {
                    HeaderElement[] codecs = ceheader.getElements();
                    for (HeaderElement codec : codecs) {
                        if ("gzip".equalsIgnoreCase(codec.getName())) {
                            response.setEntity(new GzipDecompressingEntity(entity));
                            return;
                        } else if ("deflate".equalsIgnoreCase(codec.getName())) {
                            response.setEntity(new DeflateDecompressingEntity(entity));
                            return;
                        }
                    }
                }
            }
        });

        final HttpClientConnectionManager connManager;

        if (pooled) {
            PoolingHttpClientConnectionManager poolConnManager = new PoolingHttpClientConnectionManager();
            poolConnManager.setMaxTotal(DEFAULT_POOL_MAX_TOTAL);
            poolConnManager.setDefaultMaxPerRoute(DEFAULT_POOL_MAX_PER_ROUTE);
            poolConnManager.setDefaultSocketConfig(soConfig);
            poolConnManager.setDefaultConnectionConfig(ConnectionConfig.DEFAULT);
            connManager = poolConnManager;
        } else {
            Registry<ConnectionSocketFactory> factoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", sslConnectionSocketFactory)
                    .build();
            BasicHttpClientConnectionManager basicConnManager = new BasicHttpClientConnectionManager(factoryRegistry);
            basicConnManager.setSocketConfig(soConfig);
            basicConnManager.setConnectionConfig(ConnectionConfig.DEFAULT);
            connManager = basicConnManager;
        }

        builder.setConnectionManager(connManager);
        CloseableHttpClient rst = builder.build();

        if (pooled) {
            Runtime.getRuntime().addShutdownHook(new Thread() {
                @Override
                public void run() {
                    try {
                        rst.close();
                        connManager.shutdown();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            });
        }

        return rst;
    }

    private static CloseableHttpClient getClient(boolean pooled, KeyStore keyStore) {
        if (pooled) {
            if (pooledClient == null) {
                synchronized (HttpUtil.class) {
                    if (pooledClient == null) {
                        pooledClient = buildClient(true, keyStore);
                    }
                }
            }
            return pooledClient;
        } else {
            return buildClient(false, keyStore);
        }
    }

    private static URI buildURI(HttpRequest request) throws URISyntaxException {
        Map<String, String> params = request.getParams();
        URIBuilder builder = new URIBuilder(request.getUrl());
        if (!params.isEmpty()) {
            params.forEach(builder::addParameter);
        }
        return builder.build();
    }

    private static HttpEntity buildEntity(HttpRequest request) throws UnsupportedEncodingException {
        String charset = request.getReqCharset();
        String mime = request.getMime();
        Map<String, String> params = request.getParams();
        String postString = request.getPostString();
        byte[] postBody = request.getPostBody();
        ContentType contentType = ContentType.create(mime, charset);
        HttpEntity entity = null;

        if (params != null && !params.isEmpty()) { // 优先使用参数列表填充表单来post
            List<NameValuePair> form = params.entrySet().stream()
                    .map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue()))
                    .collect(Collectors.toList());
            entity = new UrlEncodedFormEntity(form, charset);
        } else if (StringUtils.isNotBlank(postString)) {// 如果参数列表为空，则使用字符串请求体
            entity = new StringEntity(postString, contentType);
        } else if (postBody != null && postBody.length > 0) {
            entity = new ByteArrayEntity(postBody);
        } else {
            entity = new BasicHttpEntity();
        }

        return entity;
    }

    private static <T> HttpResponse<T> request(HttpRequest request, ResponseHandler<T> responseHandler)
            throws URISyntaxException, IOException {
        HttpMethod method = request.getMethod();
        String url = request.getUrl();

        HttpRequestBase httpRequest = null;
        switch (method) {
            case GET:
                httpRequest = new HttpGet(buildURI(request));
                break;
            case POST:
                httpRequest = new HttpPost(url);
                ((HttpPost) httpRequest).setEntity(buildEntity(request));
                break;
            case PUT:
                httpRequest = new HttpPut(url);
                ((HttpPut) httpRequest).setEntity(buildEntity(request));
                break;
            case PATCH:
                httpRequest = new HttpPatch(url);
                ((HttpPatch) httpRequest).setEntity(buildEntity(request));
                break;
            case DELETE:
                httpRequest = new HttpDelete(buildURI(request));
                break;
            case HEAD:
                httpRequest = new HttpHead(buildURI(request));
                break;
            case OPTIONS:
                httpRequest = new HttpOptions(buildURI(request));
                break;
            case TRACE:
                httpRequest = new HttpTrace(buildURI(request));
                break;
        }

        HttpClientContext context = new HttpClientContext();

        // 设置header
        for (Map.Entry<String, String> entry : request.getHeaders().entrySet()) {
            httpRequest.addHeader(entry.getKey(), entry.getValue());
        }

        // 设置cookie
        CookieStore cookieStore = new BasicCookieStore();
        context.setCookieStore(cookieStore);
        request.getCookies().forEach((key, value) -> {
            cookieStore.addCookie(new BasicClientCookie(key, value));
        });

        // 设置请求参数
        RequestConfig.Builder builder = RequestConfig.custom().setRedirectsEnabled(request.isFollowRedirect());

        int connTimeout = request.getConnTimeout();
        int readTimeout = request.getReadTimeout();
        if (connTimeout > 0) {
            builder.setConnectTimeout(connTimeout).setConnectionRequestTimeout(connTimeout);
        }
        if (readTimeout > 0) {
            builder.setSocketTimeout(readTimeout);
        }

        if (request.needProxy()) {
            String proxyHost = request.getProxyHost();
            int proxyPort = request.getProxyPort();
            String proxyUser = request.getProxyUser();
            String proxyPassword = request.getProxyPwd();
            builder.setProxy(new HttpHost(proxyHost, proxyPort));

            builder.setProxy(new HttpHost(proxyHost, proxyPort));
            if (request.needProxyAuth()) {
                CredentialsProvider credsProvider = new BasicCredentialsProvider();
                UsernamePasswordCredentials usernamePasswordCredentials = new UsernamePasswordCredentials(
                        proxyUser, proxyPassword);
                credsProvider.setCredentials(new AuthScope(proxyHost, proxyPort), usernamePasswordCredentials);
                context.setCredentialsProvider(credsProvider);
            }
        }

        httpRequest.setConfig(builder.build());

        CloseableHttpClient client = getClient(request.isPooled(), request.getKeyStore());

        // 重试发送请求
        CloseableHttpResponse response = null;
        int retryCount = 0;
        boolean stopRetry = false;
        while (!stopRetry) {
            try {
                response = client.execute(httpRequest, context);
                stopRetry = true;

                int statusCode = response.getStatusLine().getStatusCode();

                Map<String, String> headers = Stream.of(response.getAllHeaders())
                        .collect(Collectors.toMap(Header::getName, Header::getValue, (s, a) -> s + ";" + a));

                Map<String, String> cookies = cookieStore.getCookies().stream()
                        .collect(Collectors.toMap(Cookie::getName, Cookie::getValue));

                T body = responseHandler.handleResponse(response);

                return new HttpResponse<T>(statusCode, headers, cookies, body);
            } catch (Exception e) {
                logger.debug("请求失败:", e);
                retryCount++;
                stopRetry = retryCount > request.getRetryMax();
                if (!stopRetry) {
                    try {
                        Thread.sleep(request.getRetryInterval());
                    } catch (InterruptedException e1) {
                        e1.printStackTrace();
                    }
                    continue;
                } else {
                    throw e;
                }
            } finally {
                if (stopRetry) {
                    if (response != null) {
                        response.close();
                    }
                    if (httpRequest != null) {
                        httpRequest.reset();
                    }
                    if (!request.isPooled()) {
                        client.close();
                    }
                }
            }
        }
        return null;
    }

    /**
     * 请求文本.可以作为调用API或者加载网页
     *
     * @param request
     * @return
     * @throws Exception
     */
    public static HttpResponse<String> requestText(final HttpRequest request) throws Exception {
        return request(request, (org.apache.http.HttpResponse response) -> {
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            String charset = null;
            // 优先选用contentType解析
            ContentType contentType = ContentType.get(entity);
            if (contentType != null) {
                Charset chars = contentType.getCharset();
                if (chars != null) {
                    charset = chars.name();
                }
            }

            if (charset == null) {// 最后选用请求默认的字符编码
                charset = request.getResCharset();
            }
            return EntityUtils.toString(entity, charset);
        });
    }

    /**
     * 下载二进制数据.
     *
     * @param request
     * @return
     * @throws Exception
     */
    public static HttpResponse<byte[]> requestByteArray(HttpRequest request) throws Exception {
        return request(request, (org.apache.http.HttpResponse response) -> {
            HttpEntity entity = response.getEntity();
            return entity == null ? null : EntityUtils.toByteArray(entity);
        });
    }

    /**
     * 下载文件.该方法会在临时目录里生成一个临时文件
     *
     * @param request
     * @return
     * @throws Exception
     */
    public static HttpResponse<File> requestFile(final HttpRequest request) throws Exception {
        final File target = File.createTempFile("httputil_", ".temp");
        return request(request, (org.apache.http.HttpResponse response) -> {
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }
            try (OutputStream os = new BufferedOutputStream(new FileOutputStream(target), 8192)) {
                entity.writeTo(os);
            }
            return target;
        });
    }
}
