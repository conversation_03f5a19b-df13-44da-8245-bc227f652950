package com.zwy.common.utils;

public class NumberToChineseUtil {
    private static final String[] CN_NUMBERS = {"零", "一", "两", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] CN_UNITS = {"", "十", "百", "千", "万"};


    public static String toChinese(int number) {
        String result = String.valueOf(number);
        //将该字符串分割为数组存放
        char[] ch = result.toCharArray();
        //结果 字符串
        String str = "";
        int length = ch.length;
        for (int i = 0; i < length; i++) {
            int c = (int) ch[i] - 48;
            if (c != 0) {
                str += CN_NUMBERS[c - 1] + CN_UNITS[length - i - 1];
            }
        }
        return str;
    }


}