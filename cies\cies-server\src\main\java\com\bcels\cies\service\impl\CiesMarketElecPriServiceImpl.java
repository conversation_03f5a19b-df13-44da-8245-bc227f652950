package com.bcels.cies.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.Query;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bcels.cies.repository.entity.CiesMarketElecPriEntity;
import com.bcels.cies.repository.mapper.CiesMarketElecPriMapper;
import com.bcels.cies.request.CiesMarketConfigRequest;
import com.bcels.cies.request.CiesMarketElecPriRecordRequest;
import com.bcels.cies.request.CiesMarketElecPriRequest;
import com.bcels.cies.response.CiesMarketElecPriResponse;
import com.bcels.cies.service.ICiesMarketElecPriService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 市场分时电价 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Service
public class CiesMarketElecPriServiceImpl extends ServiceImpl<CiesMarketElecPriMapper, CiesMarketElecPriEntity> implements ICiesMarketElecPriService {

    @Autowired
    private CiesMarketElecPriMapper ciesMarketElecPriMapper;

    @Override
    public CiesMarketElecPriEntity findElecPriInfo(String month,String projectId) {
        QueryWrapper<CiesMarketElecPriEntity> queryWrapper = new QueryWrapper<>();

        // 日期"2024/04 "切换为"4月"
        String[] split = month.split("/");
        String result = split[1].contains("0")
                ? split[1].replace("0", "") + "月"
                : split[1] + "月";

        queryWrapper.eq("year", split[0]);
        queryWrapper.eq("month", result);
        queryWrapper.eq("project_id", projectId);
        return getOne(queryWrapper);
    }

    @Override
    public List<CiesMarketElecPriResponse> findElecPriInfoByYear(CiesMarketConfigRequest request) {
        List<CiesMarketElecPriEntity> elecPriInfoByYear = ciesMarketElecPriMapper.findElecPriInfoByYear(request.getProjectId(), request.getYear());
        if (CollectionUtils.isEmpty(elecPriInfoByYear)) {
            return null;
        }
        return BeanCopyUtil.copyListProperties(elecPriInfoByYear, CiesMarketElecPriResponse::new);
    }

    @Override
    public void deleteElecPri(CiesMarketElecPriRecordRequest request) {
        QueryWrapper<CiesMarketElecPriEntity> queryWrapper= new QueryWrapper();
        queryWrapper.eq("year",request.getYear());
        queryWrapper.eq("project_id",request.getProjectId());
        remove(queryWrapper);
    }
}
