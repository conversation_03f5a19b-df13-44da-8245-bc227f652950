package com.bcels.cies.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bcels.cies.repository.entity.DcpChannelConfigEntity;
import com.bcels.cies.repository.mapper.DcpChannelConfigMapper;
import com.bcels.cies.request.CiesPointAndIndicatorRequest;
import com.bcels.cies.response.CiesPointAndIndicatorResponse;
import com.bcels.cies.response.DcpChannelConfigResponse;
import com.bcels.cies.service.IDcpChannelConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zwy.common.utils.BeanCopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 通道配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
@DS("dcp")
public class DcpChannelConfigServiceImpl extends ServiceImpl<DcpChannelConfigMapper, DcpChannelConfigEntity> implements IDcpChannelConfigService {


    @Autowired
    private DcpChannelConfigMapper dcpChannelConfigMapper;
    @Override
    public List<DcpChannelConfigResponse> findByProjectId(String projectId) {
        QueryWrapper<DcpChannelConfigEntity> queryWrapper= new QueryWrapper<>();
        queryWrapper.eq("p_id",projectId);
        List<DcpChannelConfigEntity> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return BeanCopyUtil.copyListProperties(list,DcpChannelConfigResponse::new);
    }

    @Override
    public List<CiesPointAndIndicatorResponse> findChannelForPoint(String projectId) {
        return dcpChannelConfigMapper.findChannelForPoint(projectId);
    }

    @Override
    public List<CiesPointAndIndicatorResponse> findEquipForPoint(CiesPointAndIndicatorRequest request) {
        return dcpChannelConfigMapper.findEquipForPoint(request);
    }

    @Override
    public List<CiesPointAndIndicatorResponse> findTestPoint(CiesPointAndIndicatorRequest request) {
        return dcpChannelConfigMapper.findTestPoint(request);
    }

    @Override
    public List<CiesPointAndIndicatorResponse> findEquip(String projectId) {
        return dcpChannelConfigMapper.findEquip(projectId);
    }

    @Override
    public List<CiesPointAndIndicatorResponse> findIndicatorForOne(CiesPointAndIndicatorRequest request) {
        return dcpChannelConfigMapper.findIndicatorForOne(request);
    }

    @Override
    public List<CiesPointAndIndicatorResponse> findIndicatorForTwo(CiesPointAndIndicatorRequest request) {
        return dcpChannelConfigMapper.findIndicatorForTwo(request);
    }

    @Override
    public String findEquipById(String equipId) {
        return dcpChannelConfigMapper.findEquipById(equipId);
    }

    @Override
    public String findChannelById(String cId) {
        return dcpChannelConfigMapper.findChannelById(cId);
    }
}
