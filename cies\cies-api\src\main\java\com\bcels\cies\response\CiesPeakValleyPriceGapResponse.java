package com.bcels.cies.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@Schema(description = "生成结算单明细")
public class CiesPeakValleyPriceGapResponse implements Serializable {

    @Schema(description = "峰谷价差主键")
    private String priceGapId;

    @Schema(description = "并网点名称")
    private String connectionPointName;

    @Schema(description = "并网点Id")
    private String connectionPointId;

    @Schema(description = "统计阶段")
    private String statPeriod;

    @Schema(description = "时段")
    private String timeRange;

    @Schema(description = "上月读数")
    private BigDecimal lastMonthRead;

    @Schema(description = "本月读数")
    private BigDecimal currentMonthRead;

    @Schema(description = "结算电表倍率")
    private BigDecimal meterMultiplier;

    @Schema(description = "抄见电量（kwh）")
    private BigDecimal meteredUsage;

    @Schema(description = "结算电量（kwh）")
    private BigDecimal billedUsage;

    @Schema(description = "电价（元/kwh）")
    private BigDecimal unitPrice;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "分项合计")
    private BigDecimal totalAmount;

    @Schema(description = "峰谷价差总收益（元）")
    private BigDecimal peakValleyRevenue;
}

